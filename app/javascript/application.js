// Configure your import map in config/importmap.rb. Read more: https://github.com/rails/importmap-rails
import "@hotwired/turbo-rails"
import "controllers"

// Import Lucide and initialize icons
import "lucide"

// Initialize Lucide icons
function initializeLucideIcons() {
  console.log('Initializing Lucide icons...')
  console.log('Lucide available:', !!window.lucide)
  
  if (window.lucide && window.lucide.createIcons) {
    window.lucide.createIcons()
    console.log('Lucide icons initialized')
  } else {
    console.error('Lucide not available or createIcons method missing')
  }
}

// Initialize on page load and after Turbo navigation
document.addEventListener('DOMContentLoaded', initializeLucideIcons)
document.addEventListener('turbo:load', initializeLucideIcons)
document.addEventListener('turbo:frame-load', initializeLucideIcons)

// Also try immediate initialization in case DOM is already loaded
if (document.readyState === 'loading') {
  // DOM is still loading
} else {
  // DOM is already loaded
  initializeLucideIcons()
}