import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="push-notifications"
export default class extends Controller {
  static targets = ["status", "subscribeButton", "unsubscribeButton"]
  static values = { 
    vapidPublicKey: String,
    subscribeUrl: String,
    unsubscribeUrl: String
  }

  connect() {
    this.checkPushSupport()
    this.updateSubscriptionStatus()
  }

  checkPushSupport() {
    if (!('serviceWorker' in navigator)) {
      this.showStatus('Service Worker not supported', 'error')
      return false
    }

    if (!('PushManager' in window)) {
      this.showStatus('Push messaging not supported', 'error')
      return false
    }

    return true
  }

  async updateSubscriptionStatus() {
    if (!this.checkPushSupport()) return

    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()
      
      if (subscription) {
        this.showSubscribed()
      } else {
        this.showUnsubscribed()
      }
    } catch (error) {
      console.error('Error checking subscription status:', error)
      this.showStatus('Error checking subscription status', 'error')
    }
  }

  async subscribe() {
    if (!this.checkPushSupport()) return

    try {
      const registration = await navigator.serviceWorker.ready
      
      // Convert VAPID key from base64 to Uint8Array
      const applicationServerKey = this.urlBase64ToUint8Array(this.vapidPublicKeyValue)
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: applicationServerKey
      })

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription)
      
      this.showSubscribed()
      this.showStatus('Successfully subscribed to push notifications!', 'success')
      
    } catch (error) {
      console.error('Error subscribing to push notifications:', error)
      this.showStatus('Failed to subscribe to push notifications', 'error')
    }
  }

  async unsubscribe() {
    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()
      
      if (subscription) {
        // Unsubscribe from push service
        await subscription.unsubscribe()
        
        // Remove subscription from server
        await this.removeSubscriptionFromServer(subscription)
        
        this.showUnsubscribed()
        this.showStatus('Successfully unsubscribed from push notifications', 'success')
      }
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error)
      this.showStatus('Failed to unsubscribe from push notifications', 'error')
    }
  }

  async sendSubscriptionToServer(subscription) {
    const subscriptionData = {
      endpoint: subscription.endpoint,
      p256dh_key: this.arrayBufferToBase64(subscription.getKey('p256dh')),
      auth_key: this.arrayBufferToBase64(subscription.getKey('auth'))
    }

    const response = await fetch(this.subscribeUrlValue, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ subscription: subscriptionData })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  async removeSubscriptionFromServer(subscription) {
    const response = await fetch(this.unsubscribeUrlValue, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ endpoint: subscription.endpoint })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  showSubscribed() {
    if (this.hasSubscribeButtonTarget) {
      this.subscribeButtonTarget.style.display = 'none'
    }
    if (this.hasUnsubscribeButtonTarget) {
      this.unsubscribeButtonTarget.style.display = 'block'
    }
  }

  showUnsubscribed() {
    if (this.hasSubscribeButtonTarget) {
      this.subscribeButtonTarget.style.display = 'block'
    }
    if (this.hasUnsubscribeButtonTarget) {
      this.unsubscribeButtonTarget.style.display = 'none'
    }
  }

  showStatus(message, type = 'info') {
    if (this.hasStatusTarget) {
      this.statusTarget.textContent = message
      this.statusTarget.className = `status-${type} p-2 rounded mb-4`
      
      // Auto-hide success/info messages after 5 seconds
      if (type === 'success' || type === 'info') {
        setTimeout(() => {
          this.statusTarget.textContent = ''
          this.statusTarget.className = ''
        }, 5000)
      }
    }
  }

  // Utility functions
  urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return window.btoa(binary)
  }
}