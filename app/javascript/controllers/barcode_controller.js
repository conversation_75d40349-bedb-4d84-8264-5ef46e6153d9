import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "scanButton"]
  static values = {
    fps: { type: Number, default: 10 },
    qrbox: { type: Number, default: 250 },
    aspectRatio: { type: Number, default: 1.0 },
    disableFlip: { type: Boolean, default: false }
  }

  connect() {
    this.isScanning = false
    this.scanner = null
    this.setupScanButton()
  }

  disconnect() {
    this.stopScanner()
  }

  setupScanButton() {
    // If a scanButtonTarget exists, wire it up, else create one for backward compatibility
    if (this.hasScanButtonTarget) {
      this.scanButtonTarget.onclick = () => this.startScanner()
      // Ensure parent is relative for absolute positioning
      this.inputTarget.parentNode.style.position = 'relative'
      return
    }
    
    // Fallback: create the button if not present (legacy support)
    const existingBtn = this.element.querySelector('.barcode-scan-btn')
    if (existingBtn) existingBtn.remove()

    const scanBtn = document.createElement('button')
    scanBtn.type = 'button'
    scanBtn.className = 'barcode-scan-btn absolute inset-y-0 right-0 flex items-center pr-3'
    scanBtn.setAttribute('data-barcode-btn', '')
    scanBtn.innerHTML = `
      <svg class="w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h5m3 0h6m-9 4h2m3 0h2M4 20h5m3 0h6m-9-4h2m3 0h2"></path>
      </svg>
    `
    scanBtn.title = "Scan barcode"
    scanBtn.onclick = () => this.startScanner()
    this.inputTarget.parentNode.style.position = 'relative'
    this.inputTarget.parentNode.appendChild(scanBtn)
  }

  startScanner() {
    if (this.isScanning) return

    try {
      this.isScanning = true
      this.createScannerOverlay()
      this.initializeScanner()
    } catch (error) {
      console.error("Scanner initialization error:", error)
      this.showError("Failed to initialize camera: " + error.message)
      this.stopScanner()
    }
  }

  createScannerOverlay() {
    this.overlay = document.createElement('div')
    this.overlay.className = 'fixed inset-0 bg-black bg-opacity-80 z-50 flex flex-col'

    this.overlay.innerHTML = `
      <div class="flex-shrink-0 bg-white px-4 py-3 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">Scan Barcode</h3>
        <button type="button" class="close-scanner p-2 rounded-full hover:bg-gray-100 transition-colors">
          <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="flex-1 bg-white">
        <div id="barcode-scanner-region" class="w-full h-full"></div>
      </div>

      <div class="flex-shrink-0 bg-white px-4 py-3">
        <div class="flex items-center justify-center">
          <button type="button" class="manual-entry px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors">
            Enter Manually
          </button>
        </div>
      </div>
    `

    document.body.appendChild(this.overlay)

    // Add event listeners
    this.overlay.querySelector('.close-scanner').onclick = () => this.stopScanner()
    this.overlay.querySelector('.manual-entry').onclick = () => this.stopScanner()

    // Prevent body scroll
    document.body.style.overflow = 'hidden'
  }

  initializeScanner() {
    // Check if Html5QrcodeScanner is available globally
    if (typeof Html5QrcodeScanner === 'undefined') {
      throw new Error('Html5QrcodeScanner not loaded. Make sure the script tag is included.')
    }

    const config = {
      fps: this.fpsValue,
      qrbox: this.qrboxValue,
      aspectRatio: this.aspectRatioValue,
      disableFlip: this.disableFlipValue
    }

    this.scanner = new Html5QrcodeScanner(
      "barcode-scanner-region",
      config,
      false // verbose
    )

    // Success callback
    const onScanSuccess = (decodedText, decodedResult) => {
      console.log(`Scan result: ${decodedText}`, decodedResult)
      this.handleSuccessfulScan(decodedText)
    }

    // Error callback (optional)
    const onScanError = (errorMessage) => {
      // Handle scan error - usually just means no barcode detected
      // Don't log this as it's very noisy
    }

    this.scanner.render(onScanSuccess, onScanError)
  }

  handleSuccessfulScan(code) {
    // Visual feedback
    this.showSuccessMessage()

    // Update input
    this.inputTarget.value = code
    this.inputTarget.dispatchEvent(new Event('input', { bubbles: true }))
    this.inputTarget.dispatchEvent(new Event('change', { bubbles: true }))

    // Stop scanner after short delay
    setTimeout(() => {
      this.stopScanner()
    }, 1000)
  }

  showSuccessMessage() {
    const successDiv = document.createElement('div')
    successDiv.className = 'fixed top-4 left-4 right-4 bg-green-600 text-white px-4 py-3 rounded-lg z-50 text-sm text-center'
    successDiv.innerHTML = `
      <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
      </svg>
      Barcode scanned successfully!
    `

    document.body.appendChild(successDiv)

    setTimeout(() => {
      if (successDiv.parentNode) {
        document.body.removeChild(successDiv)
      }
    }, 2000)
  }

  stopScanner() {
    if (!this.isScanning) return

    this.isScanning = false

    // Stop the scanner
    if (this.scanner) {
      this.scanner.clear().catch(error => {
        console.error("Failed to clear scanner:", error)
      })
      this.scanner = null
    }

    // Remove overlay
    if (this.overlay) {
      document.body.removeChild(this.overlay)
      this.overlay = null
    }

    // Restore body scroll
    document.body.style.overflow = ''
  }

  showError(message) {
    // Create a simple error toast
    const toast = document.createElement('div')
    toast.className = 'fixed top-4 left-4 right-4 bg-red-600 text-white px-4 py-3 rounded-lg z-50 text-sm'
    toast.textContent = message

    document.body.appendChild(toast)

    setTimeout(() => {
      if (toast.parentNode) {
        document.body.removeChild(toast)
      }
    }, 5000)
  }
}