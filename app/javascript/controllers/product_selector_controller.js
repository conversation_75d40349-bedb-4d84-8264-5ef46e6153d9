import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "searchInput", 
    "productsList", 
    "loadingIndicator", 
    "emptyIndicator",
    "loadMoreButton",
    "selectedCount",
    "selectedProductsList",
    "selectedProductInputs"
  ]
  
  static values = {
    url: String,
    selectedIds: Array
  }

  connect() {
    this.page = 1
    this.hasMore = true
    this.searchTimeout = null
    this.loadProducts()
  }

  // Action methods for Stimulus data-action
  clearAll() {
    this.selectedIdsValue = []
    this.selectedProductInputsTarget.innerHTML = ''
    this.selectedProductsListTarget.innerHTML = ''
    this.updateSelectedCount()
    this.loadProducts()
  }

  removeProduct(event) {
    const productId = parseInt(event.target.dataset.productId)
    this.selectedIdsValue = this.selectedIdsValue.filter(id => id !== productId)
    
    // Remove hidden input
    const input = this.selectedProductInputsTarget.querySelector(`input[data-product-id="${productId}"]`)
    if (input) input.remove()
    
    // Remove from selected list display
    const tag = event.target.closest('span')
    if (tag) tag.remove()
    
    this.updateSelectedCount()
    this.loadProducts()
  }

  loadMore() {
    this.page += 1
    this.loadProducts(true)
  }

  toggleProduct(event) {
    const productId = parseInt(event.target.value)
    const productData = event.target.dataset
    
    if (event.target.checked) {
      this.addProduct(productId, productData)
    } else {
      this.removeProductById(productId)
    }
  }

  searchInputTargetConnected() {
    this.searchInputTarget.addEventListener('input', (e) => {
      clearTimeout(this.searchTimeout)
      this.searchTimeout = setTimeout(() => {
        this.search(e.target.value)
      }, 300)
    })
  }

  async loadProducts(append = false) {
    this.showLoading()
    
    const params = new URLSearchParams({
      page: this.page,
      search: this.getCurrentSearch(),
      selected_ids: this.selectedIdsValue.join(',')
    })

    try {
      const response = await fetch(`${this.urlValue}?${params}`, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })
      const data = await response.json()
      
      if (append) {
        this.productsListTarget.insertAdjacentHTML('beforeend', data.html)
      } else {
        this.productsListTarget.innerHTML = data.html
      }
      
      this.hasMore = data.has_more
      this.updateLoadMoreButton()
      this.hideLoading()
      
      if (data.total === 0) {
        this.showEmpty()
      } else {
        this.hideEmpty()
      }
    } catch (error) {
      console.error('Error loading products:', error)
      this.hideLoading()
    }
  }

  search(query) {
    this.page = 1
    this.loadProducts(false)
  }

  addProduct(productId, productData) {
    if (this.selectedIdsValue.includes(productId)) return
    
    this.selectedIdsValue = [...this.selectedIdsValue, productId]
    
    // Add hidden input
    const input = document.createElement('input')
    input.type = 'hidden'
    input.name = 'promotion[product_ids][]'
    input.value = productId
    input.dataset.productId = productId
    this.selectedProductInputsTarget.appendChild(input)
    
    // Add to selected list display
    const tag = document.createElement('span')
    tag.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800'
    tag.dataset.productId = productId
    tag.innerHTML = `
      ${productData.productName}
      <button type="button" class="ml-1 text-blue-600 hover:text-blue-800" data-action="click->product-selector#removeProduct" data-product-id="${productId}">x</button>
    `
    this.selectedProductsListTarget.appendChild(tag)
    
    this.updateSelectedCount()
  }

  removeProductById(productId) {
    this.selectedIdsValue = this.selectedIdsValue.filter(id => id !== productId)
    
    // Remove hidden input
    const input = this.selectedProductInputsTarget.querySelector(`input[data-product-id="${productId}"]`)
    if (input) input.remove()
    
    // Remove from selected list display
    const tag = this.selectedProductsListTarget.querySelector(`span[data-product-id="${productId}"]`)
    if (tag) tag.remove()
    
    this.updateSelectedCount()
  }

  updateSelectedCount() {
    this.selectedCountTarget.textContent = this.selectedIdsValue.length
  }

  getCurrentSearch() {
    return this.searchInputTarget.value || ''
  }

  showLoading() {
    this.loadingIndicatorTarget.classList.remove('hidden')
  }

  hideLoading() {
    this.loadingIndicatorTarget.classList.add('hidden')
  }

  showEmpty() {
    this.emptyIndicatorTarget.classList.remove('hidden')
  }

  hideEmpty() {
    this.emptyIndicatorTarget.classList.add('hidden')
  }

  updateLoadMoreButton() {
    if (this.hasMore) {
      this.loadMoreButtonTarget.classList.remove('hidden')
      this.loadMoreButtonTarget.classList.add('inline-flex')
    } else {
      this.loadMoreButtonTarget.classList.add('hidden')
      this.loadMoreButtonTarget.classList.remove('inline-flex')
    }
  }
}