import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "value", 
    "field", 
    "product",
    "breakdown", 
    "basePoints", 
    "bonusPoints", 
    "bonusSection", 
    "promotions", 
    "promotionsList"
  ]

  connect() {
    if (this.hasProductTarget) {
      this.productTarget.addEventListener('change', this.updatePoints.bind(this))
    }
  }

  async updatePoints() {
    const productId = this.hasProductTarget ? this.productTarget.value : undefined
    console.debug('[Stimulus] updatePoints called, productId:', productId)
    
    if (!productId) {
      this.resetDisplay()
      return
    }
    
    try {
      const response = await fetch(`/sales/points?product_id=${productId}`)
      console.debug('[Stimulus] fetch response:', response)
      
      if (response.ok) {
        const data = await response.json()
        console.debug('[Stimulus] response data:', data)
        
        // Update main points display
        this.valueTarget.textContent = data.points
        this.fieldTarget.value = data.points === "-" ? "" : data.total_points
        
        // Show/hide breakdown and promotions
        if (data.points !== "-" && data.total_points > 0) {
          this.showPointsBreakdown(data)
          this.showPromotions(data.promotions)
        } else {
          this.hideBreakdown()
          this.hidePromotions()
        }
      } else {
        console.error('[Stimulus] fetch error:', response.statusText)
        this.resetDisplay()
      }
    } catch (e) {
      console.error('[Stimulus] fetch error:', e)
      this.resetDisplay()
    }
  }

  showPointsBreakdown(data) {
    if (this.hasBasePointsTarget) {
      this.basePointsTarget.textContent = data.base_points
    }
    if (this.hasBonusPointsTarget) {
      this.bonusPointsTarget.textContent = data.bonus_points
    }
    
    if (this.hasBonusSectionTarget) {
      if (data.bonus_points > 0) {
        this.bonusSectionTarget.classList.remove('hidden')
      } else {
        this.bonusSectionTarget.classList.add('hidden')
      }
    }
    
    if (this.hasBreakdownTarget) {
      this.breakdownTarget.classList.remove('hidden')
    }
  }

  hideBreakdown() {
    if (this.hasBreakdownTarget) {
      this.breakdownTarget.classList.add('hidden')
    }
  }

  showPromotions(promotions) {
    if (promotions && promotions.length > 0 && this.hasPromotionsListTarget) {
      this.promotionsListTarget.innerHTML = ''
      
      promotions.forEach(promotion => {
        const promotionElement = this.createPromotionElement(promotion)
        this.promotionsListTarget.appendChild(promotionElement)
      })
      
      if (this.hasPromotionsTarget) {
        this.promotionsTarget.classList.remove('hidden')
      }
    } else {
      this.hidePromotions()
    }
  }

  hidePromotions() {
    if (this.hasPromotionsTarget) {
      this.promotionsTarget.classList.add('hidden')
    }
  }

  createPromotionElement(promotion) {
    const div = document.createElement('div')
    div.className = 'bg-green-50 border border-green-200 rounded-lg p-3'
    
    div.innerHTML = `
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="text-sm font-medium text-green-900">${promotion.name}</div>
          ${promotion.description ? `<div class="text-xs text-green-700 mt-1">${promotion.description}</div>` : ''}
          <div class="text-xs text-green-600 mt-1">Ends: ${promotion.end_date}</div>
        </div>
        <div class="ml-3 text-right">
          <div class="text-sm font-semibold text-green-900">+${promotion.bonus_points}</div>
          <div class="text-xs text-green-600">bonus pts</div>
        </div>
      </div>
    `
    
    return div
  }

  resetDisplay() {
    this.valueTarget.textContent = '-'
    if (this.hasFieldTarget) {
      this.fieldTarget.value = ""
    }
    this.hideBreakdown()
    this.hidePromotions()
  }
}
