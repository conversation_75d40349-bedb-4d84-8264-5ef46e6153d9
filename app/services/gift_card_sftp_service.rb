# frozen_string_literal: true

require "net/sftp"

class GiftCardSftpService
  class SftpError < StandardError; end

  def initialize
    @host = Rails.application.credentials.dig(:gift_card_sftp, :host) || ENV["GIFT_CARD_SFTP_HOST"]
    @username = Rails.application.credentials.dig(:gift_card_sftp, :username) || ENV["GIFT_CARD_SFTP_USERNAME"]
    @password = Rails.application.credentials.dig(:gift_card_sftp, :password) || ENV["GIFT_CARD_SFTP_PASSWORD"]
    @port = Rails.application.credentials.dig(:gift_card_sftp, :port) || ENV["GIFT_CARD_SFTP_PORT"] || 22
    @remote_path = Rails.application.credentials.dig(:gift_card_sftp, :remote_path) || ENV["GIFT_CARD_SFTP_PATH"] || "/uploads"

    validate_configuration!
  end

  # Upload a file to the SFTP server
  def upload_file(file_attachment, filename)
    raise SftpError, "File attachment is required" unless file_attachment&.attached?

    begin
      Net::SFTP.start(@host, @username, password: @password, port: @port.to_i) do |sftp|
        # Ensure remote directory exists
        ensure_remote_directory_exists(sftp)

        # Upload file
        remote_file_path = File.join(@remote_path, filename)

        file_attachment.open do |file|
          sftp.upload!(file.path, remote_file_path)
        end

        Rails.logger.info "Successfully uploaded #{filename} to SFTP server"
        true
      end
    rescue Net::SSH::Exception, Net::SFTP::Exception => e
      error_message = "SFTP upload failed: #{e.message}"
      Rails.logger.error error_message
      raise SftpError, error_message
    rescue => e
      error_message = "Unexpected error during SFTP upload: #{e.message}"
      Rails.logger.error error_message
      raise SftpError, error_message
    end
  end

  # Test SFTP connection
  def test_connection
    Net::SFTP.start(@host, @username, password: @password, port: @port.to_i) do |sftp|
      sftp.dir.entries(@remote_path)
    end
    true
  rescue => e
    Rails.logger.error "SFTP connection test failed: #{e.message}"
    false
  end

  # List files in remote directory
  def list_files
    files = []
    Net::SFTP.start(@host, @username, password: @password, port: @port.to_i) do |sftp|
      files = sftp.dir.entries(@remote_path).map(&:name).reject { |name| name.start_with?(".") }
    end
    files
  rescue => e
    Rails.logger.error "Failed to list SFTP files: #{e.message}"
    []
  end

  private

  def validate_configuration!
    missing_configs = []
    missing_configs << "host" if @host.blank?
    missing_configs << "username" if @username.blank?
    missing_configs << "password" if @password.blank?

    if missing_configs.any?
      raise SftpError, "Missing SFTP configuration: #{missing_configs.join(", ")}"
    end
  end

  def ensure_remote_directory_exists(sftp)
    # Try to create directory if it doesn't exist

    sftp.stat!(@remote_path)
  rescue Net::SFTP::StatusException => e
    if e.code == 2 # No such file or directory
      begin
        sftp.mkdir!(@remote_path)
        Rails.logger.info "Created remote directory: #{@remote_path}"
      rescue Net::SFTP::StatusException => mkdir_error
        Rails.logger.warn "Could not create remote directory #{@remote_path}: #{mkdir_error.message}"
      end
    else
      raise
    end
  end
end
