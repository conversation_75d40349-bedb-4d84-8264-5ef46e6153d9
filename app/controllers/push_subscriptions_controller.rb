class PushSubscriptionsController < ApplicationController
  before_action :authenticate_user!

  # POST /push_subscriptions
  def create
    subscription_params = params.require(:subscription).permit(:endpoint, :p256dh_key, :auth_key)
    device_info = extract_device_info

    subscription = current_user.add_push_subscription(
      endpoint: subscription_params[:endpoint],
      p256dh_key: subscription_params[:p256dh_key],
      auth_key: subscription_params[:auth_key],
      user_agent: request.user_agent,
      device_name: device_info[:device_name]
    )

    if subscription.persisted?
      render json: {
        status: "success",
        message: "Push subscription created successfully",
        subscription_id: subscription.id
      }
    else
      render json: {
        status: "error",
        message: "Failed to create push subscription",
        errors: subscription.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # DELETE /push_subscriptions
  def destroy
    endpoint = params.require(:endpoint)

    if current_user.remove_push_subscription(endpoint)
      render json: {status: "success", message: "Push subscription removed successfully"}
    else
      render json: {status: "error", message: "Push subscription not found"}, status: :not_found
    end
  end

  # GET /push_subscriptions
  def index
    subscriptions = current_user.push_notification_subscriptions.recent

    render json: {
      subscriptions: subscriptions.map do |sub|
        {
          id: sub.id,
          device_name: sub.display_name,
          active: sub.active,
          last_used_at: sub.last_used_at,
          created_at: sub.created_at,
          stale: sub.stale?
        }
      end
    }
  end

  # POST /push_subscriptions/cleanup
  def cleanup
    count = current_user.push_notification_subscriptions.where("last_used_at < ? OR last_used_at IS NULL", 30.days.ago).count
    current_user.cleanup_stale_subscriptions!

    render json: {
      status: "success",
      message: "Cleaned up #{count} stale subscription(s)"
    }
  end

  # POST /push_subscriptions/:id/test
  def test
    subscription = current_user.push_notification_subscriptions.find(params[:id])

    # Create a test notification
    test_notification = NewOrderNotifier.with(
      title: "Test Notification",
      body: "This is a test push notification from Zeiss Points",
      icon: "/icon.svg"
    )

    test_notification.deliver(current_user)

    render json: {
      status: "success",
      message: "Test notification sent"
    }
  rescue ActiveRecord::RecordNotFound
    render json: {
      status: "error",
      message: "Subscription not found"
    }, status: :not_found
  end

  private

  def extract_device_info
    user_agent = request.user_agent.to_s.downcase

    device_name = case user_agent
    when /iphone/
      "iPhone"
    when /ipad/
      "iPad"
    when /android.*mobile/
      "Android Phone"
    when /android/
      "Android Tablet"
    when /chrome/
      "Chrome Browser"
    when /firefox/
      "Firefox Browser"
    when /safari/
      "Safari Browser"
    when /edge/
      "Edge Browser"
    else
      "Unknown Device"
    end

    {
      device_name: device_name,
      user_agent: request.user_agent
    }
  end
end
