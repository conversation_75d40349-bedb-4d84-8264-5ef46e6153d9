class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern, block: :handle_outdated_browser

  helper_method :current_cart

  def test_barcode
    render "test/barcode", layout: false
  end

  def lucide_test
    render "test/lucide_test", layout: "application"
  end

  private

  def handle_outdated_browser
    Rails.logger.warn "Blocked browser: #{request.user_agent.inspect}"
    render file: Rails.root.join("public/406-unsupported-browser.html"), layout: false, status: :not_acceptable
  end

  def current_cart
    if user_signed_in?
      existing_cart = current_user.cart
      Rails.logger.debug "DEBUG: current_user.id=#{current_user.id}, existing_cart=#{existing_cart&.id}"
      return existing_cart if existing_cart
      new_cart = current_user.create_cart
      Rails.logger.debug "DEBUG: created new cart with id=#{new_cart.id}"
      new_cart
    else
      Cart.find(session[:cart_id])
    end
  rescue ActiveRecord::RecordNotFound
    cart = Cart.create
    session[:cart_id] = cart.id
    cart
  end

  protected

  def after_sign_in_path_for(resource)
    authenticated_root_path
  end
end
