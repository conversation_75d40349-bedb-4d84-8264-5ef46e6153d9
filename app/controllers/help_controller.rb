class HelpController < ApplicationController
  before_action :authenticate_user!

  def index
    @faqs = [
      {
        question: "How do I earn points?",
        answer: "You earn points by recording sales of Zeiss products. Each product has a specific point value that gets added to your wallet when the sale is approved by an admin."
      },
      {
        question: "How do I redeem my points?",
        answer: "Browse the Shop section to see available products. You can add items to your cart and place orders using your accumulated points."
      },
      {
        question: "Why is my sale still pending?",
        answer: "All sales need to be reviewed and approved by an administrator. This typically takes 1-2 business days. You'll receive a notification once your sale is processed."
      },
      {
        question: "How do I track my orders?",
        answer: "Visit the Orders section from the bottom navigation to see all your orders and their current status (pending, approved, shipped, delivered)."
      },
      {
        question: "Can I cancel an order?",
        answer: "Orders can only be cancelled while they're in 'pending' status. Once approved or shipped, cancellations need to be handled by customer support."
      },
      {
        question: "How do I update my profile information?",
        answer: "Go to your Profile from the top navigation and click 'Edit Profile' to update your personal information, store details, and notification preferences."
      },
      {
        question: "What should I do if I can't scan a barcode?",
        answer: "If the barcode scanner isn't working, you can manually enter the product serial number. Make sure your camera permissions are enabled and try cleaning your camera lens."
      },
      {
        question: "How do push notifications work?",
        answer: "Enable push notifications in your profile settings to receive real-time updates about sale approvals, order status changes, and new promotions."
      }
    ]
  end

  def contact
    @support_ticket = SupportTicket.new
  end

  def submit_question
    begin
      question_params = params.require(:question).permit(:subject, :message, :category)
    rescue ActionController::ParameterMissing
      @support_ticket = current_user.support_tickets.build
      flash[:alert] = "There was an error submitting your question. Please try again."
      render :contact and return
    end

    @support_ticket = current_user.support_tickets.build(question_params)
    @support_ticket.status = "open"
    @support_ticket.priority = "normal"

    if @support_ticket.save
      if @support_ticket.admin_handled?
        SupportTicketMailer.new_admin_ticket(@support_ticket).deliver_now
        flash[:notice] = "Your question has been submitted to our support team. You'll receive a response within 24 hours."
      else
        SupportTicketMailer.auto_response(@support_ticket).deliver_now
        flash[:notice] = "Thank you for your question! We've sent some helpful information to your email."
      end
      redirect_to help_index_path
    else
      flash[:alert] = "There was an error submitting your question. Please try again."
      render :contact
    end
  end

  private

  def question_params
    params.require(:question).permit(:subject, :message, :category)
  end
end
