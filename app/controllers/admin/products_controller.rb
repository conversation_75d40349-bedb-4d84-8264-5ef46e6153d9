# frozen_string_literal: true

class Admin::ProductsController < Admin::BaseController
  before_action :set_product, only: [:show, :edit, :update, :destroy, :activate, :deactivate]

  def index
    @q = Product.includes(:category, :product_country_data, :promotions).ransack(params[:q])
    @pagy, @products = pagy(@q.result(distinct: true).order(:name), items: 20)

    # Product analytics using cached values
    @product_metrics = {
      total_products: Product.count,
      active_products: Product.active.count,
      inactive_products: Product.inactive.count,
      total_sales: Product.sum(:sales_count),
      total_orders: Product.sum(:orders_count),
      total_quantity_sold: Product.sum(:total_quantity_sold),
      total_points_earned: Product.sum(:total_points_earned),
      products_in_promotions: Product.where("promotion_products_count > 0").count
    }
  end

  def show
    @product_country_data = @product.product_country_data.includes(:country)
    @active_promotions = @product.promotions.current.active

    # Product performance metrics using cached values
    @product_stats = {
      sales_count: @product.sales_count,
      orders_count: @product.orders_count,
      line_items_count: @product.line_items_count,
      total_points_earned: @product.total_points_earned,
      total_quantity_sold: @product.total_quantity_sold,
      promotion_products_count: @product.promotion_products_count
    }
  end

  def new
    @product = Product.new
    @product.product_country_data.build
    load_form_data
  end

  def create
    @product = Product.new(product_params)

    if @product.save
      redirect_to admin_product_path(@product), notice: "Product was successfully created."
    else
      load_form_data
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @product.product_country_data.build if @product.product_country_data.empty?
    load_form_data
  end

  def update
    if @product.update(product_params)
      redirect_to admin_product_path(@product), notice: "Product was successfully updated."
    else
      load_form_data
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @product.destroy
      redirect_to admin_products_path, notice: "Product was successfully deleted."
    else
      redirect_to admin_product_path(@product), alert: "Cannot delete product: it may be referenced by orders or sales."
    end
  end

  def activate
    @product.update!(status: :active)
    redirect_to admin_products_path, notice: "Product activated."
  end

  def deactivate
    @product.update!(status: :inactive)
    redirect_to admin_products_path, notice: "Product deactivated."
  end

  def bulk_update
    product_ids = params[:product_ids] || []
    action = params[:bulk_action]

    if product_ids.empty?
      redirect_to admin_products_path, alert: "No products selected."
      return
    end

    products = Product.where(id: product_ids)

    case action
    when "activate"
      products.update_all(status: :active)
      redirect_to admin_products_path, notice: "#{products.count} products activated."
    when "deactivate"
      products.update_all(status: :inactive)
      redirect_to admin_products_path, notice: "#{products.count} products deactivated."
    when "delete"
      deleted_count = 0
      products.each do |product|
        if product.destroy
          deleted_count += 1
        end
      end
      redirect_to admin_products_path, notice: "#{deleted_count} products deleted."
    else
      redirect_to admin_products_path, alert: "Invalid bulk action."
    end
  end

  private

  def set_product
    @product = Product.find(params[:id])
  end

  def product_params
    params.require(:product).permit(:name, :sku, :description, :points_required, :status,
      :product_type, :category_id, :image,
      product_country_data_attributes: [:id, :country_id, :points_earned, :_destroy])
  end

  def load_form_data
    @categories = Category.includes(:brand).order(:name)
    @countries = Country.order(:name)
  end
end
