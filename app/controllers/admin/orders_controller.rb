class Admin::OrdersController < Admin::BaseController
  before_action :set_order, only: [:show, :approve, :reject, :ship, :deliver]

  def index
    @orders = Order.includes(:user, line_items: :product)
      .order(created_at: :desc)

    # Filter by status if specified
    if params[:status].present?
      @orders = @orders.where(status: params[:status])
    end

    # Filter by user if specified
    if params[:user_id].present?
      @orders = @orders.where(user_id: params[:user_id])
    end

    # Search by order ID or user name
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @orders = @orders.joins(:user).where(
        "orders.id::text ILIKE ? OR users.first_name ILIKE ? OR users.last_name ILIKE ? OR users.email ILIKE ?",
        search_term, search_term, search_term, search_term
      )
    end

    @pagy, @orders = pagy(@orders, items: 25)

    # Stats for dashboard
    @stats = {
      total: Order.count,
      pending: Order.where(status: "pending").count,
      approved: Order.where(status: "approved").count,
      shipped: Order.where(status: "shipped").count,
      delivered: Order.where(status: "delivered").count,
      rejected: Order.where(status: "rejected").count
    }
  end

  def show
    @order_history = @order.activities.order(created_at: :desc) if @order.respond_to?(:activities)
  end

  def approve
    if @order.pending?
      # Check if user has sufficient points
      if @order.user.wallet.points >= @order.total_points
        # Deduct points from user's wallet
        @order.user.wallet.debit(@order.total_points, context: @order)

        # Generate SAP ID and mark as processed
        sap_id = generate_sap_id(@order)
        @order.update!(
          status: "approved",
          approved_at: Time.current,
          approved_by: current_user.id,
          sap_id: sap_id,
          sap_processed_at: Time.current
        )

        # Send notification to user
        OrderApprovedNotifier.with(record: @order, approved_by: current_user).deliver(@order.user)

        # TODO: Send order to SAP system here
        # SapOrderService.new(@order).submit

        redirect_to admin_order_path(@order), notice: "Order ##{@order.id} approved successfully. SAP ID: #{sap_id}. #{@order.total_points} points deducted from user's wallet."
      else
        @order.update!(status: "rejected")
        redirect_to admin_order_path(@order), alert: "Order rejected: User has insufficient points (#{@order.user.wallet.points} available, #{@order.total_points} required)."
      end
    else
      redirect_to admin_order_path(@order), alert: "Order cannot be approved in its current status."
    end
  end

  def reject
    if @order.pending? || @order.approved?
      # If order was approved, refund points
      if @order.approved?
        @order.user.wallet.credit(@order.total_points, context: @order)
      end

      reason = params[:reason] # Allow optional rejection reason
      @order.update!(status: "rejected", rejected_at: Time.current, rejected_by: current_user.id)
      
      # Send notification to user
      OrderRejectedNotifier.with(record: @order, rejected_by: current_user, reason: reason).deliver(@order.user)
      
      redirect_to admin_order_path(@order), notice: "Order ##{@order.id} rejected successfully."
    else
      redirect_to admin_order_path(@order), alert: "Order cannot be rejected in its current status."
    end
  end

  def ship
    if @order.approved?
      @order.update!(status: "shipped", shipped_at: Time.current, shipped_by: current_user.id)
      
      # Send notification to user
      OrderShippedNotifier.with(record: @order, shipped_by: current_user).deliver(@order.user)
      
      redirect_to admin_order_path(@order), notice: "Order ##{@order.id} marked as shipped."
    else
      redirect_to admin_order_path(@order), alert: "Order must be approved before shipping."
    end
  end

  def deliver
    if @order.shipped?
      @order.update!(status: "delivered", delivered_at: Time.current)
      
      # Send notification to user
      OrderDeliveredNotifier.with(record: @order).deliver(@order.user)
      
      redirect_to admin_order_path(@order), notice: "Order ##{@order.id} marked as delivered."
    else
      redirect_to admin_order_path(@order), alert: "Order must be shipped before marking as delivered."
    end
  end

  private

  def set_order
    @order = Order.find(params[:id])
  end

  def generate_sap_id(order)
    # Generate a unique SAP ID using timestamp and order ID
    # Format: SAP-YYYYMMDD-HHMMSS-ORDER_ID
    timestamp = Time.current.strftime("%Y%m%d-%H%M%S")
    "SAP-#{timestamp}-#{order.id}"
  end
end
