# frozen_string_literal: true

class Admin::GiftCardBatchesController < Admin::BaseController
  before_action :set_batch, only: [:show, :download, :retry_upload, :process_batch]

  def index
    @q = GiftCardBatch.includes(:gift_card_orders).ransack(params[:q])
    @pagy, @batches = pagy(@q.result(distinct: true).order(batch_date: :desc), items: 20)
  end

  def show
    @gift_card_orders = @batch.gift_card_orders.includes(:order, :product)
  end

  def download
    if @batch.csv_file.attached?
      redirect_to rails_blob_path(@batch.csv_file, disposition: "attachment")
    else
      redirect_to admin_gift_card_batch_path(@batch), alert: "No CSV file available for download."
    end
  end

  def retry_upload
    if @batch.retry_upload!
      redirect_to admin_gift_card_batch_path(@batch), notice: "Batch upload retry successful."
    else
      redirect_to admin_gift_card_batch_path(@batch), alert: "Batch upload retry failed: #{@batch.upload_error}"
    end
  end

  def process_batch
    if @batch.pending?
      @batch.process_orders!
      redirect_to admin_gift_card_batch_path(@batch), notice: "Batch processed successfully."
    else
      redirect_to admin_gift_card_batch_path(@batch), alert: "Batch has already been processed."
    end
  end

  def process_today
    GiftCardBatchProcessorJob.perform_later(Date.current)
    redirect_to admin_gift_card_batches_path, notice: "Gift card batch processing started for today."
  end

  def test_sftp
    service = GiftCardSftpService.new
    if service.test_connection
      redirect_to admin_gift_card_batches_path, notice: "SFTP connection test successful."
    else
      redirect_to admin_gift_card_batches_path, alert: "SFTP connection test failed. Check logs for details."
    end
  rescue GiftCardSftpService::SftpError => e
    redirect_to admin_gift_card_batches_path, alert: "SFTP error: #{e.message}"
  end

  private

  def set_batch
    @batch = GiftCardBatch.find(params[:id])
  end
end
