class Admin::StoresController < Admin::BaseController
  before_action :set_store, only: [:show, :edit, :update, :activate, :approve, :deactivate, :update_chain]

  def index
    @q = Store.includes(:brand, :address).ransack(params[:q])
    @pagy, @stores = pagy(@q.result(distinct: true).order(created_at: :desc), items: 20)
  end

  def show
    @users = @store.users.includes(:wallet).order(created_at: :desc)
    @recent_sales = Sale.joins(:user).where(users: {store_id: @store.id})
      .includes(:user, :product).order(created_at: :desc).limit(10)

    # Store analytics using cached values
    @store_stats = {
      users_count: @store.users_count,
      sales_count: @store.sales_count,
      approved_sales_count: @store.approved_sales_count,
      pending_sales_count: @store.pending_sales_count,
      total_sales_points: @store.total_sales_points,
      orders_count: @store.orders_count,
      promotions_count: @store.promotions_count
    }
  end

  def edit
  end

  def update
    if @store.update(store_params)
      flash[:notice] = "Store updated successfully."
      redirect_to admin_store_path(@store)
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def approve
    @store.update!(status: :active)
    flash[:notice] = "Store approved and activated."
    redirect_to admin_stores_path
  end

  def activate
    @store.update!(status: :active)
    flash[:notice] = "Store activated successfully."
    redirect_to admin_stores_path
  end

  def deactivate
    @store.update!(status: :inactive)
    flash[:notice] = "Store deactivated successfully."
    redirect_to admin_stores_path
  end

  def update_chain
    store_chain_id = params[:store_chain_id]

    if store_chain_id.blank?
      # Remove from chain
      @store.update!(store_chain: nil)
      flash[:notice] = "Store removed from chain successfully."
    elsif params[:new_chain_name].present?
      # Add to existing chain or create new one
      chain = StoreChain.find_or_create_by(name: params[:new_chain_name].strip)
      @store.update!(store_chain: chain)
      flash[:notice] = "Store added to new chain '#{chain.name}' successfully."
    # Create new chain
    else
      # Add to existing chain
      chain = StoreChain.find(store_chain_id)
      @store.update!(store_chain: chain)
      flash[:notice] = "Store added to '#{chain.name}' chain successfully."
    end

    redirect_to admin_store_path(@store)
  rescue ActiveRecord::RecordNotFound
    flash[:alert] = "Store chain not found."
    redirect_to admin_store_path(@store)
  rescue => e
    flash[:alert] = "Failed to update store chain: #{e.message}"
    redirect_to admin_store_path(@store)
  end

  private

  def set_store
    @store = Store.find(params[:id])
  end

  def store_params
    params.require(:store).permit(:name, :phone_number, :status, :brand_id, :store_chain_id)
  end
end
