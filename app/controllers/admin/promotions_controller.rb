# frozen_string_literal: true

class Admin::PromotionsController < Admin::BaseController
  before_action :set_promotion, only: [:show, :edit, :update, :destroy, :activate, :deactivate]

  def index
    @q = Promotion.includes(:store, :region, :store_chain, :products).ransack(params[:q])
    @pagy, @promotions = pagy(@q.result(distinct: true).order(created_at: :desc), items: 20)

    # Promotion analytics using cached values
    @promotion_metrics = {
      total_promotions: Promotion.count,
      active_promotions: Promotion.active.count,
      inactive_promotions: Promotion.inactive.count,
      expired_promotions: Promotion.expired.count,
      total_sales: Promotion.sum(:sales_count),
      total_bonus_points: Promotion.sum(:total_bonus_points),
      total_products_in_promotions: Promotion.sum(:promotion_products_count),
      store_promotions: Promotion.where.not(store_id: nil).count,
      region_promotions: Promotion.where.not(region_id: nil).count,
      chain_promotions: Promotion.where.not(store_chain_id: nil).count
    }
  end

  def show
    # Promotion performance metrics using cached values
    @promotion_stats = {
      sales_count: @promotion.sales_count,
      total_bonus_points: @promotion.total_bonus_points,
      promotion_products_count: @promotion.promotion_products_count,
      average_bonus_per_sale: (@promotion.sales_count > 0) ? (@promotion.total_bonus_points.to_f / @promotion.sales_count).round(2) : 0
    }
  end

  def new
    @promotion = Promotion.new
    load_form_data
  end

  def create
    @promotion = Promotion.new(promotion_params)

    if @promotion.save
      redirect_to admin_promotion_path(@promotion), notice: "Promotion was successfully created."
    else
      load_form_data
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    load_form_data
  end

  def update
    if @promotion.update(promotion_params)
      redirect_to admin_promotion_path(@promotion), notice: "Promotion was successfully updated."
    else
      load_form_data
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @promotion.destroy!
    redirect_to admin_promotions_path, notice: "Promotion was successfully deleted."
  end

  def activate
    @promotion.update!(status: :active)
    redirect_to admin_promotions_path, notice: "Promotion activated."
  end

  def deactivate
    @promotion.update!(status: :inactive)
    redirect_to admin_promotions_path, notice: "Promotion deactivated."
  end

  private

  def set_promotion
    @promotion = Promotion.find(params[:id])
  end

  def promotion_params
    params.require(:promotion).permit(:name, :description, :bonus_points, :bonus_multiplier,
      :start_date, :end_date, :status, :store_id, :region_id,
      :store_chain_id, product_ids: [])
  end

  def load_form_data
    @stores = Store.active.includes(:address).order(:name)
    @regions = Region.includes(:states).order(:name)
    @store_chains = StoreChain.order(:name)
    @products = Product.includes(:category).order(:name)
  end
end
