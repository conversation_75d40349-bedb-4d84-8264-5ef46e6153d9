class Admin::DashboardController < Admin::BaseController
  def index
    @pending_sales_count = Sale.pending.count
    @recent_sales = Sale.includes(:user, :product).order(created_at: :desc).limit(10)

    # Use cached counter values for better performance
    @total_users = Store.sum(:users_count)
    @total_stores = Store.count
    @total_points_awarded = Store.sum(:total_sales_points)

    # Additional cached metrics
    @total_approved_sales = Store.sum(:approved_sales_count)
    @total_pending_sales = Store.sum(:pending_sales_count)
    @total_orders = Store.sum(:orders_count)

    # Sales by status for chart
    @sales_by_status = Sale.group(:status).count

    # Recent activity
    @recent_activity = Sale.includes(:user, :product, :approved_by_admin)
      .where.not(status: :pending)
      .order(updated_at: :desc)
      .limit(5)
  end
end
