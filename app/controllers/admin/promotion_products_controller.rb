# frozen_string_literal: true

class Admin::PromotionProductsController < Admin::BaseController
  def index
    @q = Product.includes(:category).ransack(params[:q])

    # Apply search if provided
    if params[:search].present?
      @q.name_or_sku_or_category_name_cont = params[:search]
    end

    # Get selected product IDs to mark them as checked
    selected_ids = params[:selected_ids].present? ? params[:selected_ids].split(",").map(&:to_i) : []

    # Paginate results
    page = params[:page]&.to_i || 1
    per_page = 20

    products = @q.result(distinct: true).order(:name)
    total_count = products.count

    @products = products.offset((page - 1) * per_page).limit(per_page)

    has_more = total_count > (page * per_page)

    respond_to do |format|
      format.json do
        html = render_to_string(
          partial: "admin/promotion_products/products_list",
          locals: {products: @products, selected_ids: selected_ids},
          formats: [:html]
        )

        render json: {
          html: html,
          has_more: has_more,
          total: total_count,
          page: page
        }
      end

      format.html do
        # Fallback for non-AJAX requests
        redirect_to admin_promotions_path
      end
    end
  end
end
