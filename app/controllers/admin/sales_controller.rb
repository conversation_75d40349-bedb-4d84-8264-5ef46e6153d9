# frozen_string_literal: true

class Admin::SalesController < Admin::BaseController
  before_action :set_sale, only: [:show, :approve, :reject]

  def index
    @q = Sale.includes(:user, :product, :approved_by_admin).ransack(params[:q])
    @pagy, @sales = pagy(@q.result(distinct: true).order(created_at: :desc), items: 20)

    # Sales analytics using cached values
    @sales_metrics = {
      total_sales: Sale.count,
      pending_sales: Sale.pending.count,
      approved_sales: Sale.approved.count,
      rejected_sales: Sale.rejected.count,
      total_points_awarded: Sale.approved.sum(:points),
      average_sale_points: Sale.approved.average(:points)&.round(2) || 0,
      top_performing_stores: Store.order(approved_sales_count: :desc).limit(5),
      top_performing_users: User.order(approved_sales_count: :desc).limit(5),
      top_selling_products: Product.order(sales_count: :desc).limit(5)
    }
  end

  def show
  end

  def approve
    if @sale.pending?
      @sale.update!(status: :approved, approved_by: current_user.id, approved_at: Time.current)

      # Credit base points
      @sale.user.wallet.credit(@sale.points, context: @sale)

      # Apply promotion bonuses
      @sale.apply_promotion_bonuses!

      # Notify user of approval
      SaleApprovedNotifier.with(record: @sale, approved_by: current_user).deliver(@sale.user)

      total_points = @sale.total_points_with_promotions
      bonus_points = total_points - @sale.points

      flash[:notice] = if bonus_points > 0
        "Sale approved! #{@sale.points} base points + #{bonus_points} bonus points (#{total_points} total) credited to #{@sale.user.email}. User has been notified."
      else
        "Sale approved and #{@sale.points} points credited to #{@sale.user.email}. User has been notified."
      end
    else
      flash[:alert] = "Sale is not pending and cannot be approved."
    end
    redirect_to admin_sales_path
  end

  def reject
    if @sale.pending?
      rejection_reason = params[:reason] || params[:sale]&.dig(:rejection_reason)
      @sale.update!(status: :rejected, approved_by: current_user.id, approved_at: Time.current)

      # Notify user of rejection
      SaleRejectedNotifier.with(
        record: @sale,
        rejected_by: current_user,
        reason: rejection_reason
      ).deliver(@sale.user)

      flash[:notice] = "Sale rejected and user has been notified."
    else
      flash[:alert] = "Sale is not pending and cannot be rejected."
    end
    redirect_to admin_sales_path
  end

  private

  def set_sale
    @sale = Sale.find(params[:id])
  end
end
