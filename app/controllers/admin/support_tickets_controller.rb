class Admin::SupportTicketsController < Admin::BaseController
  before_action :set_support_ticket, only: [:show, :update, :respond]

  def index
    @support_tickets = SupportTicket.includes(:user, :admin_user)
      .recent

    # Filter by status if provided
    @support_tickets = @support_tickets.by_status(params[:status]) if params[:status].present?

    # Filter by category if provided
    @support_tickets = @support_tickets.where(category: params[:category]) if params[:category].present?

    # Filter by priority if provided
    @support_tickets = @support_tickets.by_priority(params[:priority]) if params[:priority].present?

    # Paginate results
    @pagy, @support_tickets = pagy(@support_tickets, items: 25)

    @status_counts = {
      all: SupportTicket.count,
      open: SupportTicket.open.count,
      in_progress: SupportTicket.in_progress.count,
      resolved: SupportTicket.resolved.count
    }
  end

  def show
    @response_form = ResponseForm.new
  end

  def update
    if @support_ticket.update(ticket_params)
      flash[:notice] = "Support ticket updated successfully."
    else
      flash[:alert] = "Failed to update support ticket."
    end

    redirect_to admin_support_ticket_path(@support_ticket)
  end

  def respond
    @support_ticket.assign_attributes(response_params)
    @support_ticket.admin_user = current_user
    @support_ticket.responded_at = Time.current
    @support_ticket.status = "resolved" if @support_ticket.status == "open"

    @response_form = ResponseForm.new(response_params)
    if @response_form.valid? && @support_ticket.save
      # Send email notification to user
      if Rails.env.test?
        SupportTicketMailer.admin_response(@support_ticket).deliver_now
      else
        SupportTicketMailer.admin_response(@support_ticket).deliver_later
      end

      flash[:notice] = "Response sent successfully."
      redirect_to admin_support_ticket_path(@support_ticket)
    else
      flash.now[:alert] = "Failed to send response."
      render :show, status: :unprocessable_entity
    end
  end

  private

  def set_support_ticket
    @support_ticket = SupportTicket.find(params[:id])
  end

  def ticket_params
    params.require(:support_ticket).permit(:status, :priority)
  end

  def response_params
    params.require(:support_ticket).permit(:admin_response)
  end

  # Simple form object for response form
  class ResponseForm
    include ActiveModel::Model
    attr_accessor :admin_response
    validates :admin_response, presence: true, length: {minimum: 10}
  end
end
