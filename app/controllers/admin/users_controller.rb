class Admin::UsersController < Admin::BaseController
  before_action :set_user, only: [:show, :edit, :update, :activate, :deactivate, :credit_wallet, :debit_wallet]

  def index
    @q = User.includes(:store, :wallet).ransack(params[:q])
    @pagy, @users = pagy(@q.result(distinct: true).order(created_at: :desc), items: 20)
  end

  def show
    @recent_sales = @user.sales.includes(:product).order(created_at: :desc).limit(10)
    @recent_orders = @user.orders.includes(:product).order(created_at: :desc).limit(10)
    @wallet_activities = @user.wallet.activities.order(created_at: :desc).limit(20)

    # User analytics using cached values
    @user_stats = {
      sales_count: @user.sales_count,
      approved_sales_count: @user.approved_sales_count,
      pending_sales_count: @user.pending_sales_count,
      orders_count: @user.orders_count,
      total_points_earned: @user.total_points_earned
    }
  end

  def edit
  end

  def update
    result = @user.update(user_params)
    if result
      flash[:notice] = "User updated successfully."
      redirect_to admin_user_path(@user)
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def activate
    @user.update!(status: :active)
    flash[:notice] = "User activated successfully."
    redirect_to admin_users_path
  end

  def deactivate
    @user.update!(status: :inactive)
    flash[:notice] = "User deactivated successfully."
    redirect_to admin_users_path
  end

  def credit_wallet
    amount = params[:amount].to_i
    reason = params[:reason]

    if amount <= 0
      redirect_to admin_user_path(@user), alert: "Amount must be greater than 0."
      return
    end

    if reason.blank?
      redirect_to admin_user_path(@user), alert: "Reason is required for wallet adjustments."
      return
    end

    begin
      # Create a custom admin credit activity with reason
      @user.wallet.create_activity(
        :admin_credit,
        owner: @user,
        parameters: {
          amount: amount,
          reason: reason,
          admin_user_id: current_user.id,
          admin_user_name: "#{current_user.first_name} #{current_user.last_name}".strip
        }
      )

      # Update points directly for admin actions (bypassing normal credit method)
      @user.wallet.increment!(:points, amount)

      redirect_to admin_user_path(@user), notice: "Successfully credited #{amount} points to #{@user.first_name}'s wallet."
    rescue => e
      redirect_to admin_user_path(@user), alert: "Failed to credit wallet: #{e.message}"
    end
  end

  def debit_wallet
    amount = params[:amount].to_i
    reason = params[:reason]

    if amount <= 0
      redirect_to admin_user_path(@user), alert: "Amount must be greater than 0."
      return
    end

    if reason.blank?
      redirect_to admin_user_path(@user), alert: "Reason is required for wallet adjustments."
      return
    end

    if @user.wallet.points < amount
      redirect_to admin_user_path(@user), alert: "Insufficient points. User has #{@user.wallet.points} points, cannot debit #{amount}."
      return
    end

    begin
      # Create a custom admin debit activity with reason
      @user.wallet.create_activity(
        :admin_debit,
        owner: @user,
        parameters: {
          amount: amount,
          reason: reason,
          admin_user_id: current_user.id,
          admin_user_name: "#{current_user.first_name} #{current_user.last_name}".strip
        }
      )

      # Update points directly for admin actions (bypassing normal debit method)
      @user.wallet.decrement!(:points, amount)

      redirect_to admin_user_path(@user), notice: "Successfully debited #{amount} points from #{@user.first_name}'s wallet."
    rescue => e
      redirect_to admin_user_path(@user), alert: "Failed to debit wallet: #{e.message}"
    end
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def user_params
    params.require(:user).permit(:email, :role, :status, :store_id)
  end
end
