class ProductsController < ApplicationController
  before_action :authenticate_user!

  def index
    # Only show products from the user's store brand
    user_brand = current_user.store&.brand
    redirect_to root_path, alert: "No store or brand associated with your account." and return unless user_brand

    @products = Product.joins(:category, :product_country_data)
      .where(status: "active", categories: {brand_id: user_brand.id})
      .includes(:category, :product_country_data, image_attachment: :blob)
      .order(:name)

    # Filter by category if specified (only categories from user's brand)
    if params[:category_id].present?
      category = user_brand.categories.find_by(id: params[:category_id])
      @products = @products.where(category_id: category.id) if category
    end

    # Search functionality
    if params[:search].present?
      @products = @products.where("products.name ILIKE ? OR products.description ILIKE ?",
        "%#{params[:search]}%", "%#{params[:search]}%")
    end

    # Paginate products
    @pagy, @products = pagy(@products, items: 12)

    # Only show categories from user's brand that have active products
    @categories = user_brand.categories
      .joins(:products)
      .where(products: {status: "active"})
      .distinct
      .order(:name)
    @current_category = @categories.find_by(id: params[:category_id]) if params[:category_id].present?
    @user_brand = user_brand
  end

  def show
    # Only allow viewing products from the user's store brand
    user_brand = current_user.store&.brand
    redirect_to root_path, alert: "No store or brand associated with your account." and return unless user_brand

    @product = Product.joins(:category)
      .where(categories: {brand_id: user_brand.id})
      .find(params[:id])
    @user_country = current_user.store&.address&.country || Country.find_by(code: "US")
  rescue ActiveRecord::RecordNotFound
    redirect_to products_path, alert: "Product not found or not available for your store."
  end
end
