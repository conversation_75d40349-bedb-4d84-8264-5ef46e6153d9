class OrdersController < ApplicationController
  include Pagy::Backend
  before_action :authenticate_user!

  def index
    @pagy, @orders = pagy(current_user.orders.includes(line_items: :product)
      .order(created_at: :desc), items: 10)
    @products = if current_user.store&.brand.present?
      Product.joins(:category).where(categories: {brand_id: current_user.store.brand.id})
    else
      []
    end
  end

  def new
    @cart = current_cart
    if @cart.line_items.empty?
      redirect_to cart_path(@cart), alert: "Your cart is empty."
    else
      @order = Order.new
    end
  end

  def create
    @cart = current_cart
    if @cart.line_items.empty?
      redirect_to cart_path(@cart), alert: "Your cart is empty."
      return
    end
    @order = current_user.orders.build(order_params)
    @order.status = :pending
    @order.points = @cart.total_points
    if @order.save
      @cart.line_items.find_each do |item|
        item.update!(order: @order, cart: nil)
      end
      @cart.destroy
      session[:cart_id] = nil
      NewOrderNotifier.with(record: @order).deliver_later(recipient: current_user)
      redirect_to orders_path, notice: "Order placed and pending approval."
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def order_params
    params.require(:order).permit(:shipping_type, :shipping_address)
  end
end
