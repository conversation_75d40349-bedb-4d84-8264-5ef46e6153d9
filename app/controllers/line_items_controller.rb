class LineItemsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_cart
  before_action :set_line_item, only: %i[update destroy]

  def create
    # Only allow adding products from the user's store brand
    user_brand = current_user.store&.brand
    unless user_brand
      redirect_to root_path, alert: "No store or brand associated with your account."
      return
    end

    product = Product.joins(:category)
      .where(categories: {brand_id: user_brand.id})
      .find(params[:product_id])

    @line_item = @cart.line_items.find_by(product: product)
    if @line_item
      @line_item.quantity += 1
    else
      @line_item = @cart.line_items.build(product: product, quantity: 1)
    end
    if @line_item.save
      redirect_to cart_path(@cart), notice: "Product added to cart."
    else
      redirect_to cart_path(@cart), alert: "Could not add product."
    end
  rescue ActiveRecord::RecordNotFound
    render plain: "Product not found", status: :not_found
  end

  def update
    if @line_item.update(line_item_params)
      redirect_to cart_path(@cart), notice: "Cart updated."
    else
      redirect_to cart_path(@cart), alert: "Could not update cart."
    end
  end

  def destroy
    @line_item.destroy
    redirect_to cart_path(@cart), notice: "Item removed from cart."
  end

  private

  def set_cart
    @cart = current_cart
  end

  def set_line_item
    @line_item = @cart.line_items.find(params[:id])
  end

  def line_item_params
    params.require(:line_item).permit(:quantity, :price)
  end
end
