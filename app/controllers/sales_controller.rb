class SalesController < ApplicationController
  before_action :authenticate_user!

  def new
    @store = current_user.store
    if @store.nil? && !current_user.admin?
      flash[:alert] = "You must be assigned to a store to record a sale."
      redirect_to root_path and return
    end
    # Get the brand for the user's store (brand is now associated with store directly)
    @brand = @store&.brand || Brand.first # fallback if not associated
    @products = @brand.categories.flat_map(&:products)
    @sale = Sale.new

    respond_to do |format|
      format.html
      format.turbo_stream { render turbo_stream: turbo_stream.replace("modal", partial: "sales/form", locals: {sale: @sale, products: @products, brand: @brand}) }
    end
  end

  def create
    @store = current_user.store
    @brand = @store&.brand || Brand.first
    @products = @brand.categories.flat_map(&:products)
    @sale = Sale.new(sale_params)
    @sale.user = current_user
    # Set points from product data for user's country
    country = current_user.address&.country || Country.find_by(code: "CA")
    if @sale.product && country
      datum = @sale.product.product_country_data.find_by(country_id: country.id)
      @sale.points = datum&.points_earned
    end

    if @sale.save
      # Notify admins of new sale
      NewSaleNotifier.with(record: @sale).deliver

      flash[:notice] = "Sale recorded successfully and submitted for approval."
      redirect_to root_path
    else
      render :new, status: :unprocessable_entity
    end
  end

  # Returns points for a product and user's country as JSON
  def points
    product = Product.find_by(id: params[:product_id])
    country = current_user.address&.country || Country.find_by(code: "CA") # fallback to Canada

    if product && country
      datum = product.product_country_data.find_by(country_id: country.id)
      base_points = datum&.points_earned || 0

      # Create a temporary sale to check for applicable promotions
      temp_sale = Sale.new(product: product, user: current_user)
      applicable_promotions = Promotion.applicable_for_sale(temp_sale)

      promotion_info = applicable_promotions.map do |promotion|
        bonus_points = promotion.calculate_bonus_points(base_points)
        {
          name: promotion.name,
          bonus_points: bonus_points,
          description: promotion.description,
          end_date: promotion.end_date.strftime("%m/%d/%Y")
        }
      end

      total_bonus = applicable_promotions.sum { |promo| promo.calculate_bonus_points(base_points) }
      total_points = base_points + total_bonus

      render json: {
        base_points: base_points,
        bonus_points: total_bonus,
        total_points: total_points,
        promotions: promotion_info,
        points: (total_points > 0) ? total_points : "-"
      }
    else
      render json: {points: "-", promotions: []}
    end
  end

  private

  def sale_params
    params.require(:sale).permit(:product_id, :serial_number, :sold_at, :notes, :points)
  end
end
