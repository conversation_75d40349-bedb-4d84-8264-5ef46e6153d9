module IconHelper
  # Helper method to render Lucide icons consistently
  # Usage: <%= lucide_icon "home", class: "w-5 h-5" %>
  def lucide_icon(name, options = {})
    css_class = options[:class] || "w-5 h-5"
    content_tag :i, "", "data-lucide": name, class: css_class
  end

  # Common icon shortcuts for frequently used icons
  def dashboard_icon(options = {})
    lucide_icon("layout-dashboard", options)
  end

  def users_icon(options = {})
    lucide_icon("users", options)
  end

  def sales_icon(options = {})
    lucide_icon("clipboard-list", options)
  end

  def orders_icon(options = {})
    lucide_icon("shopping-cart", options)
  end

  def products_icon(options = {})
    lucide_icon("package", options)
  end

  def stores_icon(options = {})
    lucide_icon("store", options)
  end

  def promotions_icon(options = {})
    lucide_icon("percent", options)
  end

  def gift_icon(options = {})
    lucide_icon("gift", options)
  end

  def support_icon(options = {})
    lucide_icon("message-circle-question", options)
  end

  def help_icon(options = {})
    lucide_icon("help-circle", options)
  end

  def search_icon(options = {})
    lucide_icon("search", options)
  end

  def edit_icon(options = {})
    lucide_icon("edit", options)
  end

  def delete_icon(options = {})
    lucide_icon("trash-2", options)
  end

  def add_icon(options = {})
    lucide_icon("plus", options)
  end

  def back_icon(options = {})
    lucide_icon("arrow-left", options)
  end

  def send_icon(options = {})
    lucide_icon("send", options)
  end

  def check_icon(options = {})
    lucide_icon("check", options)
  end

  def x_icon(options = {})
    lucide_icon("x", options)
  end
end
