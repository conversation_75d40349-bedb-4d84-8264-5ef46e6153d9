<!-- Mobile-first PWA Storefront -->
<div class="min-h-screen bg-gray-50">
  
  <%= render 'shared/top_navigation', 
      title: 'Shop Products', 
      subtitle: "#{@user_brand.name} products for your store",
      show_back_button: true,
      back_path: root_path %>

  <!-- Main Content -->
  <main class="pb-20">
    
    <!-- Search and Filter Section -->
    <section class="bg-white border-b border-gray-200 sticky top-0 z-10">
      <div class="px-4 py-4">
        <!-- Search Bar -->
        <%= form_with url: products_path, method: :get, local: true, class: "mb-4" do |f| %>
          <div class="relative">
            <%= f.text_field :search, 
                placeholder: "Search products...", 
                value: params[:search],
                class: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-zeiss-500 focus:border-transparent" %>
            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"></i>
            <%= f.hidden_field :category_id, value: params[:category_id] %>
          </div>
        <% end %>

        <!-- Category Filter -->
        <div class="flex space-x-2 overflow-x-auto pb-2">
          <%= link_to products_path(search: params[:search]), 
              class: "flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-colors #{params[:category_id].blank? ? 'bg-zeiss-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}" do %>
            All Products
          <% end %>
          
          <% @categories.each do |category| %>
            <%= link_to products_path(category_id: category.id, search: params[:search]), 
                class: "flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-colors #{params[:category_id] == category.id.to_s ? 'bg-zeiss-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}" do %>
              <%= category.name %>
            <% end %>
          <% end %>
        </div>
      </div>
    </section>

    <!-- Brand Info Banner -->
    <section class="bg-zeiss-50 border-b border-zeiss-200">
      <div class="px-4 py-4">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-zeiss-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold text-sm"><%= @user_brand.name.first.upcase %></span>
          </div>
          <div>
            <h2 class="font-semibold text-zeiss-900"><%= @user_brand.name %> Products</h2>
            <p class="text-sm text-zeiss-700">Exclusive products for your store</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Products Grid -->
    <section class="px-4 py-6">
      <% if @current_category %>
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-900"><%= @current_category.name %></h2>
          <p class="text-gray-600 text-sm mt-1">Browse <%= @current_category.name.downcase %> products from <%= @user_brand.name %></p>
        </div>
      <% end %>

      <% if @products.any? %>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          <% @products.each do |product| %>
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <!-- Product Image -->
              <div class="aspect-square bg-gray-100 relative">
                <%= product_image_tag(product, class: "w-full h-full object-cover") %>
                
                <!-- Points Badge -->
                <div class="absolute top-2 right-2">
                  <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-zeiss-600 text-white">
                    <%= product.points_required %> pts
                  </span>
                </div>
              </div>

              <!-- Product Info -->
              <div class="p-3 lg:p-4">
                <h3 class="font-semibold text-gray-900 text-sm mb-1 line-clamp-2">
                  <%= product.name %>
                </h3>
                
                <% if product.description.present? %>
                  <p class="text-xs text-gray-600 mb-2 line-clamp-2 hidden sm:block">
                    <%= product.description %>
                  </p>
                <% end %>

                <div class="mb-3">
                  <span class="text-xs text-gray-500 block sm:hidden"><%= product.category.name %></span>
                  <div class="hidden sm:flex items-center justify-between">
                    <div>
                      <span class="text-xs font-medium text-gray-500">Category:</span>
                      <span class="text-xs text-gray-900"><%= product.category.name %></span>
                    </div>
                  </div>
                </div>
                
                <div class="flex flex-col sm:flex-row gap-2">
                  <%= link_to product_path(product), 
                      class: "flex-1 inline-flex items-center justify-center px-2 py-1.5 border border-gray-300 rounded-lg text-xs font-medium text-gray-700 hover:bg-gray-50 transition-colors" do %>
                    <i data-lucide="eye" class="w-3 h-3 sm:mr-1"></i>
                    <span class="hidden sm:inline">View</span>
                  <% end %>
                  
                  <%= button_to line_items_path, 
                      params: { product_id: product.id }, 
                      method: :post,
                      class: "flex-1 inline-flex items-center justify-center px-2 py-1.5 bg-zeiss-600 hover:bg-zeiss-700 text-white rounded-lg text-xs font-medium transition-colors" do %>
                    <i data-lucide="shopping-cart" class="w-3 h-3 sm:mr-1"></i>
                    <span class="hidden sm:inline">Add</span>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Load More / Pagination placeholder -->
        <% if @products.count >= 20 %>
          <div class="mt-8 text-center">
            <p class="text-gray-500 text-sm">Showing <%= @products.count %> products</p>
          </div>
        <% end %>

      <% else %>
        <!-- Empty State -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
          <i data-lucide="package" class="mx-auto h-16 w-16 text-gray-300 mb-4"></i>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
          <p class="text-gray-600 mb-6">
            <% if params[:search].present? || params[:category_id].present? %>
              Try adjusting your search or filter criteria.
            <% else %>
              No products are currently available for redemption.
            <% end %>
          </p>
          <% if params[:search].present? || params[:category_id].present? %>
            <%= link_to products_path, 
                class: "inline-flex items-center px-4 py-2 bg-zeiss-600 hover:bg-zeiss-700 text-white font-medium rounded-lg transition-colors" do %>
              <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
              View All Products
            <% end %>
          <% end %>
        </div>
      <% end %>
    </section>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <section class="px-4 py-6">
        <div class="flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <% if @pagy.prev %>
              <%= link_to "Previous", pagy_url_for(@pagy, @pagy.prev), class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
            <% end %>
            <% if @pagy.next %>
              <%= link_to "Next", pagy_url_for(@pagy, @pagy.next), class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
            <% end %>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-center">
            <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
          </div>
        </div>
      </section>
    <% end %>
  </main>

  <%= render 'shared/bottom_navigation', active_tab: 'shop' %>
  <%= render 'shared/floating_cart' %>
</div>