<!-- Mobile-first PWA Product Detail -->
<div class="min-h-screen bg-gray-50">
  
  <%= render 'shared/top_navigation', 
      title: @product.name, 
      subtitle: @product.category.name,
      show_back_button: true,
      back_path: products_path %>

  <!-- Main Content -->
  <main class="pb-20">
    
    <!-- Product Image -->
    <section class="bg-white">
      <div class="aspect-square bg-gray-100 relative">
        <%= product_image_tag(@product, class: "w-full h-full object-cover") %>
        
        <!-- Points Badge -->
        <div class="absolute top-4 right-4">
          <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-bold bg-zeiss-600 text-white shadow-lg">
            <%= @product.points_required(@user_country&.code) %> pts
          </span>
        </div>
      </div>
    </section>

    <!-- Product Details -->
    <section class="bg-white border-b border-gray-200">
      <div class="px-4 py-6">
        <div class="mb-4">
          <h1 class="text-2xl font-bold text-gray-900 mb-2"><%= @product.name %></h1>
          <div class="flex items-center space-x-4 text-sm text-gray-600">
            <span class="inline-flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
              </svg>
              <%= @product.category.name %>
            </span>
            <span class="inline-flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              SKU: <%= @product.sku %>
            </span>
          </div>
        </div>

        <% if @product.description.present? %>
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
            <p class="text-gray-700 leading-relaxed"><%= simple_format(@product.description) %></p>
          </div>
        <% end %>

        <!-- Product Specifications -->
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-semibold text-gray-700 mb-1">Points Required</h4>
            <p class="text-xl font-bold text-zeiss-600"><%= @product.points_required(@user_country&.code) %> pts</p>
          </div>
          
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-semibold text-gray-700 mb-1">Retail Value</h4>
            <p class="text-xl font-bold text-gray-900">$<%= number_with_precision(@product.price(@user_country&.code), precision: 2) %></p>
          </div>
        </div>

        <!-- User's Points Balance -->
        <div class="bg-blue-50 rounded-lg p-4 mb-6">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-semibold text-blue-900">Your Points Balance</h4>
              <p class="text-lg font-bold text-blue-600"><%= current_user.wallet.points %> pts</p>
            </div>
            <div class="text-right">
              <% points_needed = @product.points_required(@user_country&.code) %>
              <% user_points = current_user.wallet.points %>
              <% if user_points >= points_needed %>
                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  Sufficient Points
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800">
                  Need <%= points_needed - user_points %> more pts
                </span>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Action Buttons -->
    <section class="bg-white">
      <div class="px-4 py-6">
        <div class="space-y-3">
          <% if current_user.wallet.points >= @product.points_required(@user_country&.code) %>
            <%= button_to line_items_path, 
                params: { product_id: @product.id }, 
                method: :post,
                class: "w-full flex items-center justify-center px-6 py-4 bg-zeiss-600 hover:bg-zeiss-700 text-white font-semibold rounded-xl transition-colors shadow-sm" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24">
                <circle cx="8" cy="21" r="1"/>
                <circle cx="19" cy="21" r="1"/>
                <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/>
              </svg>
              Add to Cart
            <% end %>
          <% else %>
            <button disabled 
                    class="w-full flex items-center justify-center px-6 py-4 bg-gray-300 text-gray-500 font-semibold rounded-xl cursor-not-allowed">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
              Insufficient Points
            </button>
          <% end %>

          <%= link_to cart_path(current_cart), 
              class: "w-full flex items-center justify-center px-6 py-4 border-2 border-zeiss-600 text-zeiss-600 hover:bg-zeiss-50 font-semibold rounded-xl transition-colors" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24">
              <circle cx="8" cy="21" r="1"/>
              <circle cx="19" cy="21" r="1"/>
              <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/>
            </svg>
            View Cart (<%= current_cart.line_items.sum(:quantity) %>)
          <% end %>
        </div>

        <!-- Additional Actions -->
        <div class="mt-6 pt-6 border-t border-gray-200">
          <div class="grid grid-cols-2 gap-3">
            <%= link_to products_path, 
                class: "flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 hover:bg-gray-50 font-medium rounded-lg transition-colors" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
              </svg>
              Continue Shopping
            <% end %>

            <%= link_to new_sale_path, 
                class: "flex items-center justify-center px-4 py-3 border border-green-300 text-green-700 hover:bg-green-50 font-medium rounded-lg transition-colors" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Earn More Points
            <% end %>
          </div>
        </div>
      </div>
    </section>
  </main>

  <%= render 'shared/bottom_navigation', active_tab: 'shop' %>
  <%= render 'shared/floating_cart' %>
</div>