<!-- User Profile Page -->
<div class="min-h-screen bg-gray-50">
  <%= render 'shared/top_navigation',
      title: 'My Profile',
      subtitle: "#{current_user.email.split('@').first.capitalize}'s Account" %>

  <!-- Main Content -->
  <main class="pb-20">
    
    <!-- Profile Header -->
    <section class="bg-white border-b border-gray-200">
      <div class="px-4 py-6">
        <div class="flex items-center space-x-4">
          <!-- Avatar -->
          <div class="flex-shrink-0 h-20 w-20">
            <div class="h-20 w-20 rounded-full bg-zeiss-600 flex items-center justify-center">
              <span class="text-2xl font-bold text-white">
                <%= current_user.email.first.upcase %>
              </span>
            </div>
          </div>
          
          <!-- User Info -->
          <div class="flex-1 min-w-0">
            <h1 class="text-xl font-bold text-gray-900">
              <%= current_user.email.split('@').first.capitalize %>
            </h1>
            <p class="text-sm text-gray-500"><%= current_user.email %></p>
            <% if current_user.store %>
              <p class="text-sm text-gray-600 mt-1">
                <i data-lucide="building" class="w-4 h-4 inline mr-1"></i>
                <%= current_user.store.name %>
              </p>
            <% end %>
          </div>
          
          <!-- Edit Button -->
          <div class="flex-shrink-0">
            <%= link_to edit_profile_path,
                class: "inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
              <i data-lucide="edit" class="w-4 h-4 mr-1"></i>
              Edit
            <% end %>
          </div>
        </div>
      </div>
    </section>

    <!-- Points Overview -->
    <section class="px-4 py-6">
      <div class="bg-gradient-to-r from-zeiss-600 to-zeiss-700 rounded-xl shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-semibold">Current Balance</h2>
            <p class="text-3xl font-bold mt-2"><%= @wallet.points %> <span class="text-lg font-normal">points</span></p>
          </div>
          <div class="text-right">
            <i data-lucide="coins" class="w-12 h-12 text-white opacity-80"></i>
          </div>
        </div>
        
        <!-- Points Stats -->
        <div class="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-white/20">
          <div class="text-center">
            <p class="text-2xl font-bold">+<%= @total_points_earned %></p>
            <p class="text-sm opacity-80">Total Earned</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold">-<%= @total_points_spent %></p>
            <p class="text-sm opacity-80">Total Spent</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold"><%= @pending_points %></p>
            <p class="text-sm opacity-80">Pending</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Account Information -->
    <section class="px-4 py-6 space-y-6">
      
      <!-- Account Details -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-4 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Account Information</h3>
        </div>
        <div class="px-4 py-4 space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Email</label>
              <p class="mt-1 text-sm text-gray-900"><%= current_user.email %></p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Status</label>
              <p class="mt-1">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                             <%= current_user.active? ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                  <%= current_user.status.humanize %>
                </span>
              </p>
            </div>
          </div>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Member Since</label>
              <p class="mt-1 text-sm text-gray-900"><%= current_user.created_at.strftime('%B %d, %Y') %></p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Last Sign In</label>
              <p class="mt-1 text-sm text-gray-900">
                <%= current_user.last_sign_in_at&.strftime('%B %d, %Y at %I:%M %p') || 'Never' %>
              </p>
            </div>
          </div>

          <% if current_user.address %>
            <div>
              <label class="block text-sm font-medium text-gray-500">Address</label>
              <div class="mt-1 text-sm text-gray-900">
                <p><%= current_user.address.street %></p>
                <p><%= current_user.address.city %>, <%= current_user.address.state&.name %> <%= current_user.address.postal_code %></p>
                <p><%= current_user.address.country&.name %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Wallet Activity -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-4 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Recent Wallet Activity</h3>
            <span class="text-sm text-gray-500"><%= @wallet_activities.count %> recent transactions</span>
          </div>
        </div>

        <div class="divide-y divide-gray-100">
          <% if @wallet_activities.any? %>
            <% @wallet_activities.each do |activity| %>
              <div class="px-4 py-4 hover:bg-gray-50 transition-colors">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <!-- Activity Icon -->
                    <div class="flex-shrink-0">
                      <% case activity.key %>
                      <% when 'credit', 'admin_credit' %>
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <i data-lucide="plus" class="w-4 h-4 text-green-600"></i>
                        </div>
                      <% when 'debit', 'admin_debit' %>
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                          <i data-lucide="minus" class="w-4 h-4 text-red-600"></i>
                        </div>
                      <% else %>
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <i data-lucide="zap" class="w-4 h-4 text-blue-600"></i>
                        </div>
                      <% end %>
                    </div>
                    
                    <!-- Activity Details -->
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900">
                        <% case activity.key %>
                        <% when 'credit' %>
                          Points Earned
                        <% when 'debit' %>
                          Points Spent
                        <% when 'admin_credit' %>
                          Admin Credit
                        <% when 'admin_debit' %>
                          Admin Debit
                        <% else %>
                          <%= activity.key.humanize %>
                        <% end %>
                      </p>
                      <p class="text-xs text-gray-500">
                        <%= activity.created_at.strftime('%b %d, %Y at %I:%M %p') %>
                      </p>
                      
                      <% if activity.parameters&.dig('reason') %>
                        <p class="text-xs text-gray-600 mt-1">
                          <%= activity.parameters['reason'] %>
                        </p>
                      <% end %>
                      
                      <% if activity.parameters&.dig('context_type') %>
                        <p class="text-xs text-gray-600 mt-1">
                          Related to <%= activity.parameters['context_type'] %> #<%= activity.parameters['context_id'] %>
                        </p>
                      <% end %>
                    </div>
                  </div>
                  
                  <!-- Amount -->
                  <div class="flex-shrink-0 text-right">
                    <% amount = activity.parameters&.dig('amount') || 0 %>
                    <p class="text-sm font-semibold <%= activity.key.in?(['credit', 'admin_credit']) ? 'text-green-600' : 'text-red-600' %>">
                      <%= activity.key.in?(['credit', 'admin_credit']) ? '+' : '-' %><%= amount %> pts
                    </p>
                  </div>
                </div>
              </div>
            <% end %>
            
            <% if @wallet_activities.count >= 20 %>
              <div class="px-4 py-4 text-center bg-gray-50">
                <p class="text-sm text-gray-500">Showing last 20 transactions</p>
              </div>
            <% end %>
          <% else %>
            <div class="px-4 py-8 text-center">
              <i data-lucide="coins" class="w-12 h-12 mx-auto text-gray-300 mb-3"></i>
              <p class="text-gray-500 text-sm">No wallet activity yet</p>
              <p class="text-gray-400 text-xs mt-1">Start earning points by making sales</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Recent Sales Summary -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-4 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Recent Sales</h3>
            <%= link_to new_sale_path, class: "text-sm text-zeiss-600 hover:text-zeiss-700 font-medium" do %>
              Add Sale →
            <% end %>
          </div>
        </div>

        <div class="divide-y divide-gray-100">
          <% if @recent_sales.any? %>
            <% @recent_sales.first(5).each do |sale| %>
              <div class="px-4 py-3 hover:bg-gray-50 transition-colors">
                <div class="flex items-center justify-between">
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate"><%= sale.product.name %></p>
                    <p class="text-xs text-gray-500">
                      <%= sale.sold_at.strftime('%b %d, %Y') %> • Serial: <%= sale.serial_number %>
                    </p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                 <%= sale.status == 'approved' ? 'bg-green-100 text-green-800' :
                                     sale.status == 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                     'bg-red-100 text-red-800' %>">
                      <%= sale.status.capitalize %>
                    </span>
                    <span class="text-sm font-semibold text-zeiss-600">+<%= sale.points %> pts</span>
                  </div>
                </div>
              </div>
            <% end %>
            
            <% if @recent_sales.count > 5 %>
              <div class="px-4 py-3 text-center bg-gray-50">
                <p class="text-sm text-gray-500">
                  Showing 5 of <%= @recent_sales.count %> recent sales
                </p>
              </div>
            <% end %>
          <% else %>
            <div class="px-4 py-8 text-center">
              <i data-lucide="clipboard-list" class="w-12 h-12 mx-auto text-gray-300 mb-3"></i>
              <p class="text-gray-500 text-sm">No sales recorded yet</p>
              <p class="text-gray-400 text-xs mt-1">Start by adding your first sale</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Recent Orders Summary -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-4 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Recent Orders</h3>
            <%= link_to products_path, class: "text-sm text-zeiss-600 hover:text-zeiss-700 font-medium" do %>
              Shop Products →
            <% end %>
          </div>
        </div>

        <div class="divide-y divide-gray-100">
          <% if @recent_orders.any? %>
            <% @recent_orders.first(5).each do |order| %>
              <div class="px-4 py-3 hover:bg-gray-50 transition-colors">
                <div class="flex items-center justify-between">
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900">
                      <% if order.line_items.count == 1 %>
                        <%= order.line_items.first.product.name %>
                      <% else %>
                        Order #<%= order.id %> (<%= pluralize(order.line_items.count, 'item') %>)
                      <% end %>
                    </p>
                    <p class="text-xs text-gray-500">
                      <%= order.created_at.strftime('%b %d, %Y') %> • <%= order.shipping_type.humanize %>
                    </p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                 <%= order.status == 'approved' ? 'bg-green-100 text-green-800' :
                                     order.status == 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                     'bg-red-100 text-red-800' %>">
                      <%= order.status.capitalize %>
                    </span>
                    <span class="text-sm font-semibold text-red-600">-<%= order.points %> pts</span>
                  </div>
                </div>
              </div>
            <% end %>
            
            <% if @recent_orders.count > 5 %>
              <div class="px-4 py-3 text-center bg-gray-50">
                <p class="text-sm text-gray-500">
                  Showing 5 of <%= @recent_orders.count %> recent orders
                </p>
              </div>
            <% end %>
          <% else %>
            <div class="px-4 py-8 text-center">
              <i data-lucide="shopping-bag" class="w-12 h-12 mx-auto text-gray-300 mb-3"></i>
              <p class="text-gray-500 text-sm">No orders placed yet</p>
              <p class="text-gray-400 text-xs mt-1">Browse products to redeem your points</p>
            </div>
          <% end %>
        </div>
      </div>
    </section>
  </main>

  <%= render 'shared/bottom_navigation', active_tab: 'profile' %>
  <%= render 'shared/floating_cart' %>
</div>