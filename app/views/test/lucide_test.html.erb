<!-- Test page for Lucide icons -->
<div class="p-8">
  <h1 class="text-2xl font-bold mb-6">Lucide Icons Test</h1>
  
  <div class="grid grid-cols-4 gap-4 mb-8">
    <div class="flex items-center space-x-2">
      <i data-lucide="home" class="w-6 h-6"></i>
      <span>Home</span>
    </div>
    
    <div class="flex items-center space-x-2">
      <i data-lucide="user" class="w-6 h-6"></i>
      <span>User</span>
    </div>
    
    <div class="flex items-center space-x-2">
      <i data-lucide="search" class="w-6 h-6"></i>
      <span>Search</span>
    </div>
    
    <div class="flex items-center space-x-2">
      <i data-lucide="heart" class="w-6 h-6"></i>
      <span>Heart</span>
    </div>
  </div>
  
  <div class="bg-gray-100 p-4 rounded">
    <h2 class="font-semibold mb-2">Debug Info:</h2>
    <p>Lucide loaded: <span id="lucide-status">Checking...</span></p>
    <p>Icons found: <span id="icons-count">Checking...</span></p>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const statusEl = document.getElementById('lucide-status')
    const countEl = document.getElementById('icons-count')
    
    if (window.lucide) {
      statusEl.textContent = 'Yes ✓'
      statusEl.className = 'text-green-600 font-semibold'
    } else {
      statusEl.textContent = 'No ✗'
      statusEl.className = 'text-red-600 font-semibold'
    }
    
    const iconElements = document.querySelectorAll('[data-lucide]')
    countEl.textContent = iconElements.length
  })
</script>