<!-- Mobile-first PWA Dashboard -->
<div class="min-h-screen bg-gray-50">

  <%= render 'shared/top_navigation',
      title: 'Zeiss Points',
      subtitle: "Welcome back, #{current_user.email.split('@').first.capitalize}" %>

  <!-- Main Content -->
  <main class="pb-20">

    <!-- Quick Actions Section -->
    <section class="bg-white border-b border-gray-200">
      <div class="px-4 py-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-2 gap-3">
          <%= link_to new_sale_path,
              class: "flex flex-col items-center p-4 bg-zeiss-600 hover:bg-zeiss-700
                     text-white rounded-xl transition-colors duration-200 shadow-sm" do %>
            <i data-lucide="plus" class="w-6 h-6 mb-2"></i>
            <span class="text-sm font-semibold">Add Sale</span>
          <% end %>

          <%= link_to products_path,
              class: "flex flex-col items-center p-4 bg-green-600 hover:bg-green-700
                     text-white rounded-xl transition-colors duration-200 shadow-sm" do %>
            <i data-lucide="shopping-bag" class="w-6 h-6 mb-2"></i>
            <span class="text-sm font-semibold">Shop Products</span>
          <% end %>
        </div>

        <!-- Secondary Actions -->
        <div class="grid grid-cols-2 gap-3 mt-3">
          <%= link_to orders_path,
              class: "flex flex-col items-center p-3 bg-gray-100 hover:bg-gray-200
                     text-gray-700 rounded-lg transition-colors duration-200" do %>
            <i data-lucide="clipboard-list" class="w-5 h-5 mb-1"></i>
            <span class="text-xs font-medium">View Orders</span>
          <% end %>

          <%= link_to cart_path(current_cart),
              class: "flex flex-col items-center p-3 bg-gray-100 hover:bg-gray-200
                     text-gray-700 rounded-lg transition-colors duration-200 relative" do %>
            <i data-lucide="shopping-cart" class="w-5 h-5 mb-1"></i>
            <span class="text-xs font-medium">Cart</span>
            <% if current_cart.line_items.any? %>
              <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                <%= current_cart.line_items.sum(:quantity) %>
              </span>
            <% end %>
          <% end %>
        </div>
      </div>
    </section>

    <!-- Recent Activity -->
    <section class="px-4 py-6 space-y-6">

      <!-- Recent Sales -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-4 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Recent Sales</h3>
            <span class="text-sm text-gray-500"><%= @recent_sales.count %> of <%= current_user.sales.count %></span>
          </div>
        </div>

        <div class="divide-y divide-gray-100">
          <% if @recent_sales.any? %>
            <% @recent_sales.each do |sale| %>
              <div class="px-4 py-4 hover:bg-gray-50 transition-colors">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h4 class="font-semibold text-gray-900 text-sm"><%= sale.product.name %></h4>
                    <p class="text-xs text-gray-500 mt-1">
                      Serial: <%= sale.serial_number %>
                    </p>
                    <p class="text-xs text-gray-500">
                      Sold: <%= sale.sold_at.strftime('%b %d, %Y at %I:%M %p') %>
                    </p>
                  </div>
                  <div class="text-right ml-4">
                    <div class="flex items-center space-x-2">
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                   <%= sale.status == 'approved' ? 'bg-green-100 text-green-800' :
                                       sale.status == 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                       'bg-red-100 text-red-800' %>">
                        <%= sale.status.capitalize %>
                      </span>
                    </div>
                    <p class="text-sm font-semibold text-zeiss-600 mt-1">+<%= sale.points %> pts</p>
                  </div>
                </div>
              </div>
            <% end %>
          <% else %>
            <div class="px-4 py-8 text-center">
              <svg class="w-12 h-12 mx-auto text-gray-300 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              <p class="text-gray-500 text-sm">No sales recorded yet</p>
              <p class="text-gray-400 text-xs mt-1">Start by adding your first sale</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Recent Orders -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-4 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Recent Orders</h3>
            <span class="text-sm text-gray-500"><%= @recent_orders.count %> of <%= current_user.orders.count %></span>
          </div>
        </div>

        <div class="divide-y divide-gray-100">
          <% if @recent_orders.any? %>
            <% @recent_orders.each do |order| %>
              <div class="px-4 py-4 hover:bg-gray-50 transition-colors">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h4 class="font-semibold text-gray-900 text-sm">
                      <% if order.line_items.count == 1 %>
                        <%= order.line_items.first.product.name %>
                      <% else %>
                        Order #<%= order.id %> (<%= pluralize(order.line_items.count, 'item') %>)
                      <% end %>
                    </h4>
                    <p class="text-xs text-gray-500 mt-1">
                      Shipping: <%= order.shipping_type.humanize %>
                    </p>
                    <p class="text-xs text-gray-500">
                      Ordered: <%= order.created_at.strftime('%b %d, %Y at %I:%M %p') %>
                    </p>
                  </div>
                  <div class="text-right ml-4">
                    <div class="flex items-center space-x-2">
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                   <%= order.status == 'approved' ? 'bg-green-100 text-green-800' :
                                       order.status == 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                       'bg-red-100 text-red-800' %>">
                        <%= order.status.capitalize %>
                      </span>
                    </div>
                    <p class="text-sm font-semibold text-red-600 mt-1">-<%= order.points %> pts</p>
                  </div>
                </div>
              </div>
            <% end %>
          <% else %>
            <div class="px-4 py-8 text-center">
              <svg class="w-12 h-12 mx-auto text-gray-300 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
              </svg>
              <p class="text-gray-500 text-sm">No orders placed yet</p>
              <p class="text-gray-400 text-xs mt-1">Browse products to redeem your points</p>
            </div>
          <% end %>
        </div>
      </div>
    </section>
  </main>

  <%= render 'shared/bottom_navigation', active_tab: 'dashboard' %>
  <%= render 'shared/floating_cart' %>
</div>
