<!-- Admin Support Ticket Detail -->
<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <%= link_to admin_support_tickets_path, class: "text-gray-400 hover:text-gray-600" do %>
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            <% end %>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">Support Ticket #<%= @support_ticket.id %></h1>
              <p class="mt-1 text-sm text-gray-500"><%= @support_ticket.subject %></p>
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <%= @support_ticket.status_color %>">
              <%= @support_ticket.status.humanize %>
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <%= @support_ticket.priority_color %>">
              <%= @support_ticket.priority.humanize %>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="px-4 sm:px-6 lg:px-8 py-6">
    <% if flash[:notice] %>
      <div class="mb-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
        <%= flash[:notice] %>
      </div>
    <% end %>
    <% if flash[:alert] %>
      <div class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        <%= flash[:alert] %>
      </div>
    <% end %>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">

      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">

        <!-- Original Message -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Original Message</h3>
          </div>
          <div class="px-6 py-4">
            <div class="prose max-w-none">
              <%= simple_format(@support_ticket.message) %>
            </div>
          </div>
        </div>

        <!-- Admin Response -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
              <% if @support_ticket.admin_response.present? %>
                Your Response
              <% else %>
                Send Response
              <% end %>
            </h3>
          </div>
          <div class="px-6 py-4">
            <% if @support_ticket.admin_response.present? %>
              <!-- Existing Response -->
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                  <div class="ml-3 flex-1">
                    <p class="text-sm text-blue-800 font-medium">
                      Response by <%= @support_ticket.admin_user&.email || 'Admin' %>
                    </p>
                    <p class="text-xs text-blue-600 mt-1">
                      <%= @support_ticket.responded_at&.strftime('%B %d, %Y at %I:%M %p') %>
                    </p>
                  </div>
                </div>
                <div class="mt-3 prose prose-sm max-w-none text-blue-900">
                  <%= simple_format(@support_ticket.admin_response) %>
                </div>
              </div>
            <% end %>

            <!-- Response Form -->
            <%= form_with model: @support_ticket, url: respond_admin_support_ticket_path(@support_ticket), local: true do |form| %>
              <div class="space-y-4">
                <% if @response_form && @response_form.errors.any? %>
                  <div class="mb-2 text-red-700 bg-red-50 border border-red-200 rounded px-4 py-2">
                    <ul class="list-disc pl-5">
                      <% @response_form.errors.full_messages.each do |msg| %>
                        <li><%= msg %></li>
                      <% end %>
                    </ul>
                  </div>
                <% end %>
                <div>
                  <label for="support_ticket_admin_response" class="block text-sm font-medium text-gray-700">
                    <% if @support_ticket.admin_response.present? %>
                      Update Response
                    <% else %>
                      Your Response
                    <% end %>
                  </label>
                  <div class="mt-1">
                    <%= form.text_area :admin_response,
                        rows: 6,
                        class: "shadow-sm focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md",
                        placeholder: "Type your response to the user here..." %>
                  </div>
                </div>

                <div class="flex justify-end">
                  <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    Send Response
                  </button>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">

        <!-- User Information -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">User Information</h3>
          </div>
          <div class="px-6 py-4 space-y-3">
            <div>
              <dt class="text-sm font-medium text-gray-500">Email</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @support_ticket.user.email %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">User ID</dt>
              <dd class="mt-1 text-sm text-gray-900">#<%= @support_ticket.user.id %></dd>
            </div>
            <% if @support_ticket.user.store %>
              <div>
                <dt class="text-sm font-medium text-gray-500">Store</dt>
                <dd class="mt-1 text-sm text-gray-900"><%= @support_ticket.user.store.name %></dd>
              </div>
            <% end %>
            <div>
              <dt class="text-sm font-medium text-gray-500">Points Balance</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @support_ticket.user.wallet&.points || 0 %> points</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Account Status</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @support_ticket.user.status.humanize %></dd>
            </div>
          </div>
        </div>

        <!-- Ticket Details -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Ticket Details</h3>
          </div>
          <div class="px-6 py-4 space-y-4">

            <!-- Status Update -->
            <%= form_with model: @support_ticket, url: admin_support_ticket_path(@support_ticket), local: true do |form| %>
              <div>
                <label for="support_ticket_status" class="block text-sm font-medium text-gray-700">Status</label>
                <div class="mt-1">
                  <%= form.select :status,
                      options_for_select(SupportTicket.statuses.map { |k, v| [k.humanize, k] }, @support_ticket.status),
                      {},
                      { class: "shadow-sm focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" } %>
                </div>
              </div>

              <div>
                <label for="support_ticket_priority" class="block text-sm font-medium text-gray-700">Priority</label>
                <div class="mt-1">
                  <%= form.select :priority,
                      options_for_select(SupportTicket.priorities.map { |k, v| [k.humanize, k] }, @support_ticket.priority),
                      {},
                      { class: "shadow-sm focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" } %>
                </div>
              </div>

              <div class="flex justify-end">
                <button type="submit" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500">
                  Update
                </button>
              </div>
            <% end %>

            <hr class="border-gray-200">

            <!-- Metadata -->
            <div class="space-y-3 text-sm">
              <div>
                <dt class="font-medium text-gray-500">Category</dt>
                <dd class="mt-1 text-gray-900"><%= @support_ticket.category_name %></dd>
              </div>
              <div>
                <dt class="font-medium text-gray-500">Created</dt>
                <dd class="mt-1 text-gray-900"><%= @support_ticket.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
              </div>
              <% if @support_ticket.responded_at %>
                <div>
                  <dt class="font-medium text-gray-500">Last Response</dt>
                  <dd class="mt-1 text-gray-900"><%= @support_ticket.responded_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>