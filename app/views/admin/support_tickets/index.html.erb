<!-- Admin Support Tickets -->
<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Support Tickets</h1>
            <p class="mt-1 text-sm text-gray-500">Manage user questions and support requests</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Stats -->
  <div class="px-4 sm:px-6 lg:px-8 py-6">
    <!-- Status Filter Tabs -->
    <div class="border-b border-gray-200 mb-6">
      <nav class="-mb-px flex space-x-8">
        <%= link_to admin_support_tickets_path,
            class: "#{params[:status].blank? ? 'border-zeiss-500 text-zeiss-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" do %>
          All Tickets
          <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><%= @status_counts[:all] %></span>
        <% end %>

        <%= link_to admin_support_tickets_path(status: 'open'),
            class: "#{params[:status] == 'open' ? 'border-yellow-500 text-yellow-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" do %>
          Open
          <span class="ml-2 bg-yellow-100 text-yellow-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><%= @status_counts[:open] %></span>
        <% end %>

        <%= link_to admin_support_tickets_path(status: 'in_progress'),
            class: "#{params[:status] == 'in_progress' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" do %>
          In Progress
          <span class="ml-2 bg-blue-100 text-blue-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><%= @status_counts[:in_progress] %></span>
        <% end %>

        <%= link_to admin_support_tickets_path(status: 'resolved'),
            class: "#{params[:status] == 'resolved' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" do %>
          Resolved
          <span class="ml-2 bg-green-100 text-green-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><%= @status_counts[:resolved] %></span>
        <% end %>
      </nav>
    </div>

    <!-- Tickets List -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <% if @support_tickets.any? %>
        <ul class="divide-y divide-gray-200">
          <% @support_tickets.each do |ticket| %>
            <li>
              <%= link_to admin_support_ticket_path(ticket), class: "block hover:bg-gray-50" do %>
                <div class="px-4 py-4 sm:px-6">
                  <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center space-x-3">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          #<%= ticket.id %> - <%= ticket.subject %>
                        </p>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= ticket.status_color %>">
                          <%= ticket.status.humanize %>
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= ticket.priority_color %>">
                          <%= ticket.priority.humanize %>
                        </span>
                      </div>
                      <div class="mt-2 flex items-center text-sm text-gray-500 space-x-4">
                        <div class="flex items-center">
                          <svg class="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                          </svg>
                          <%= ticket.user.email %>
                        </div>
                        <div class="flex items-center">
                          <svg class="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                          </svg>
                          <%= ticket.category_name %>
                        </div>
                        <div class="flex items-center">
                          <svg class="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          <%= ticket.created_at.strftime('%b %d, %Y at %I:%M %p') %>
                        </div>
                      </div>
                    </div>
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    </div>
                  </div>
                </div>
              <% end %>
            </li>
          <% end %>
        </ul>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No support tickets</h3>
          <p class="mt-1 text-sm text-gray-500">No support tickets match your current filters.</p>
        </div>
      <% end %>
    </div>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <% if @pagy.prev %>
            <%= link_to "Previous", pagy_url_for(@pagy, @pagy.prev), class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
          <% end %>
          <% if @pagy.next %>
            <%= link_to "Next", pagy_url_for(@pagy, @pagy.next), class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
          <% end %>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Showing <span class="font-medium"><%= @pagy.from %></span> to <span class="font-medium"><%= @pagy.to %></span> of <span class="font-medium"><%= @pagy.count %></span> tickets
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <% if @pagy.prev %>
                <%= link_to pagy_url_for(@pagy, @pagy.prev), class: "relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" do %>
                  <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                <% end %>
              <% end %>
              
              <% pagy_nav(@pagy).scan(/<a[^>]*>(\d+)<\/a>|<span[^>]*>(\d+)<\/span>/).each do |match| %>
                <% if match[0] # Link %>
                  <%= link_to match[0], pagy_url_for(@pagy, match[0].to_i), class: "relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50" %>
                <% else # Current page %>
                  <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-zeiss-50 text-sm font-medium text-zeiss-600">
                    <%= match[1] %>
                  </span>
                <% end %>
              <% end %>
              
              <% if @pagy.next %>
                <%= link_to pagy_url_for(@pagy, @pagy.next), class: "relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" do %>
                  <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>
              <% end %>
            </nav>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>