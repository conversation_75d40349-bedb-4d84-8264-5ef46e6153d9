<!-- Admin Users Management -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        User Management
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Manage user accounts, roles, and access
      </p>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
    <%= search_form_for @q, url: admin_users_path, method: :get, 
        html: { class: "space-y-4" } do |f| %>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Email Search -->
        <%= render 'shared/form_inputs/text_field',
            form: f,
            field: :email_cont,
            label: "Email",
            placeholder: "Search by email" %>

        <!-- Role Filter -->
        <%= render 'shared/form_inputs/select_field',
            form: f,
            field: :role_eq,
            label: "Role",
            options: options_for_select([
              ['All Roles', ''],
              ['Regular', 'regular'],
              ['Admin', 'admin'],
              ['Super Admin', 'super_admin']
            ], params.dig(:q, :role_eq)) %>

        <!-- Status Filter -->
        <%= render 'shared/form_inputs/select_field',
            form: f,
            field: :status_eq,
            label: "Status",
            options: options_for_select([
              ['All Statuses', ''],
              ['Active', 'active'],
              ['Inactive', 'inactive'],
              ['Deleted', 'deleted']
            ], params.dig(:q, :status_eq)) %>

        <!-- Store Filter -->
        <%= render 'shared/form_inputs/text_field',
            form: f,
            field: :store_name_cont,
            label: "Store",
            placeholder: "Search by store" %>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <%= render 'shared/form_inputs/button',
              text: "Search",
              variant: "primary" %>
          <%= link_to admin_users_path, 
              class: "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-200 bg-white hover:bg-gray-100 hover:text-gray-900 focus-visible:ring-gray-500 h-10 px-4 py-2" do %>
            Clear
          <% end %>
        </div>
        
        <div class="text-sm text-gray-500">
          <%= pluralize(@pagy.count, 'user') %> found
        </div>
      </div>
    <% end %>
  </div>

  <!-- Users Table -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              User
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Store
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Role
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Activity
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Points
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @users.each do |user| %>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                      <span class="text-sm font-medium text-gray-700">
                        <%= user.email.first.upcase %>
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      <%= user.email %>
                    </div>
                    <div class="text-sm text-gray-500">
                      Joined <%= user.created_at.strftime('%b %d, %Y') %>
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <% if user.store %>
                  <div class="text-sm text-gray-900">
                    <div class="font-medium"><%= user.store.name %></div>
                    <div class="text-gray-500">
                      <%= user.store.address&.city %>, <%= user.store.address&.state&.name %>
                    </div>
                  </div>
                <% else %>
                  <span class="text-sm text-gray-400">No store assigned</span>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                             <%= user.role == 'super_admin' ? 'bg-purple-100 text-purple-800' : 
                                 user.role == 'admin' ? 'bg-red-100 text-red-800' : 
                                 'bg-gray-100 text-gray-800' %>">
                  <%= user.role.humanize %>
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                             <%= user.status == 'active' ? 'bg-green-100 text-green-800' : 
                                 user.status == 'inactive' ? 'bg-yellow-100 text-yellow-800' : 
                                 'bg-red-100 text-red-800' %>">
                  <%= user.status.humanize %>
                </span>
                <% unless user.confirmed? %>
                  <div class="text-xs text-orange-600 mt-1">Unconfirmed</div>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div><%= pluralize(user.sign_in_count, 'sign-in') %></div>
                <% if user.last_sign_in_at %>
                  <div class="text-xs">
                    Last: <%= user.last_sign_in_at.strftime('%b %d, %Y') %>
                  </div>
                <% else %>
                  <div class="text-xs text-gray-400">Never signed in</div>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                <%= user.wallet&.points || 0 %> pts
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <%= link_to admin_user_path(user), 
                    class: "text-blue-600 hover:text-blue-900" do %>
                  View
                <% end %>
                
                <%= link_to edit_admin_user_path(user), 
                    class: "text-indigo-600 hover:text-indigo-900" do %>
                  Edit
                <% end %>
                
                <% if user.inactive? %>
                  <%= button_to activate_admin_user_path(user), 
                      method: :post,
                      class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",
                      data: { confirm: "Are you sure you want to activate this user?" } do %>
                    Activate
                  <% end %>
                <% elsif user.active? %>
                  <%= button_to deactivate_admin_user_path(user), 
                      method: :post,
                      class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500",
                      data: { confirm: "Are you sure you want to deactivate this user?" } do %>
                    Deactivate
                  <% end %>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <% if @pagy.prev %>
            <%= link_to "Previous", admin_users_path(page: @pagy.prev), 
                class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
          <% else %>
            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 pointer-events-none">Previous</span>
          <% end %>
          
          <% if @pagy.next %>
            <%= link_to "Next", admin_users_path(page: @pagy.next), 
                class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
          <% else %>
            <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 pointer-events-none">Next</span>
          <% end %>
        </div>
        
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Showing
              <span class="font-medium"><%= @pagy.from %></span>
              to
              <span class="font-medium"><%= @pagy.to %></span>
              of
              <span class="font-medium"><%= @pagy.count %></span>
              results
            </p>
          </div>
          <div>
            <%== pagy_nav(@pagy, link_extra: 'class="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"') %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Empty State -->
  <% if @users.empty? %>
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No users found</h3>
        <p class="mt-1 text-sm text-gray-500">
          No users match your current search criteria.
        </p>
      </div>
    </div>
  <% end %>
</div>