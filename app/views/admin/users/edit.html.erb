<!-- Admin User Edit View -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <%= link_to admin_users_path, class: "text-gray-400 hover:text-gray-500" do %>
              <span>Users</span>
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <%= link_to admin_user_path(@user), class: "ml-4 text-gray-400 hover:text-gray-500" do %>
                <span><%= @user.email %></span>
              <% end %>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-4 text-sm font-medium text-gray-500">Edit</span>
            </div>
          </li>
        </ol>
      </nav>
      <h2 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Edit User
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Update user information, role, and store assignment
      </p>
    </div>
  </div>

  <!-- Error Messages -->
  <% if @user.errors.any? %>
    <div class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            Please fix the following errors:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% @user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Edit Form -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-5 border-b border-gray-200">
      <h3 class="text-lg leading-6 font-semibold text-gray-900">User Information</h3>
      <p class="mt-1 text-sm text-gray-500">
        Update the user's basic information and permissions.
      </p>
    </div>
    
    <div class="px-6 py-5">
      <%= form_with model: [:admin, @user], local: true, class: "space-y-6" do |f| %>
        
        <!-- Email Field -->
        <div>
          <%= f.label :email, "Email Address", class: "block text-sm font-medium text-gray-700" %>
          <%= f.email_field :email, 
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" %>
          <p class="mt-2 text-sm text-gray-500">
            The user's email address and login credential.
          </p>
        </div>

        <!-- Role Selection -->
        <div>
          <%= f.label :role, "Role", class: "block text-sm font-medium text-gray-700" %>
          <%= f.select :role, 
              options_for_select([
                ['Regular User', 'regular'],
                ['Admin', 'admin'],
                ['Super Admin', 'super_admin']
              ], @user.role),
              {},
              { class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" } %>
          <p class="mt-2 text-sm text-gray-500">
            <span class="font-medium">Regular:</span> Standard user access. 
            <span class="font-medium">Admin:</span> Can manage sales and users. 
            <span class="font-medium">Super Admin:</span> Full system access.
          </p>
        </div>

        <!-- Status Selection -->
        <%= render 'shared/form_inputs/select_field',
            form: f,
            field: :status,
            label: "Status",
            options: options_for_select([
              ['Active', 'active'],
              ['Inactive', 'inactive'],
              ['Deleted', 'deleted']
            ], @user.status),
            help_text: "<strong>Active:</strong> User can sign in and use the system. <strong>Inactive:</strong> User cannot sign in. <strong>Deleted:</strong> User is marked as deleted.".html_safe %>

        <!-- Store Assignment -->
        <div>
          <%= f.label :store_id, "Store Assignment", class: "block text-sm font-medium text-gray-700" %>
          <%= f.collection_select :store_id, 
              Store.active.order(:name), 
              :id, 
              :name, 
              { prompt: 'Select a store', selected: @user.store_id },
              { class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" } %>
          <p class="mt-2 text-sm text-gray-500">
            The store this user is assigned to. Users can only record sales for their assigned store.
          </p>
        </div>

        <!-- Current Status Display -->
        <div class="bg-gray-50 rounded-md p-4">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Current Status</h4>
          <dl class="grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500">Email Confirmed</dt>
              <dd class="text-sm text-gray-900">
                <% if @user.confirmed? %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Confirmed
                  </span>
                  <span class="text-gray-500 ml-2">(<%= @user.confirmed_at.strftime('%b %d, %Y') %>)</span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Unconfirmed
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Sign-in Count</dt>
              <dd class="text-sm text-gray-900"><%= @user.sign_in_count %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Last Sign-in</dt>
              <dd class="text-sm text-gray-900">
                <% if @user.last_sign_in_at %>
                  <%= @user.last_sign_in_at.strftime('%B %d, %Y at %I:%M %p') %>
                <% else %>
                  <span class="text-gray-400">Never</span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Points Balance</dt>
              <dd class="text-sm text-gray-900">
                <span class="font-medium text-blue-600"><%= @user.wallet&.points || 0 %> points</span>
              </dd>
            </div>
          </dl>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <%= link_to admin_user_path(@user), 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
            Cancel
          <% end %>
          
          <%= f.submit "Update User", 
              class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Danger Zone -->
  <div class="bg-white shadow-sm rounded-lg border border-red-200">
    <div class="px-6 py-5 border-b border-red-200">
      <h3 class="text-lg leading-6 font-semibold text-red-900">Danger Zone</h3>
      <p class="mt-1 text-sm text-red-600">
        These actions are irreversible. Please be certain before proceeding.
      </p>
    </div>
    
    <div class="px-6 py-5">
      <div class="space-y-4">
        <!-- Activate/Deactivate User -->
        <div class="flex items-center justify-between">
          <div>
            <h4 class="text-sm font-medium text-gray-900">
              <% if @user.active? %>
                Deactivate User
              <% else %>
                Activate User
              <% end %>
            </h4>
            <p class="text-sm text-gray-500">
              <% if @user.active? %>
                Prevent this user from signing in and using the system.
              <% else %>
                Allow this user to sign in and use the system.
              <% end %>
            </p>
          </div>
          <div>
            <% if @user.active? %>
              <%= button_to deactivate_admin_user_path(@user), 
                  method: :post,
                  class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500",
                  data: { confirm: "Are you sure you want to deactivate this user? They will not be able to sign in." } do %>
                Deactivate User
              <% end %>
            <% else %>
              <%= button_to activate_admin_user_path(@user), 
                  method: :post,
                  class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",
                  data: { confirm: "Are you sure you want to activate this user?" } do %>
                Activate User
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>