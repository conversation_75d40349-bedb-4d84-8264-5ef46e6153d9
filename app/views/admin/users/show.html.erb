<!-- Admin User Detail View -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <%= link_to admin_users_path, class: "text-gray-400 hover:text-gray-500" do %>
              <span>Users</span>
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-4 text-sm font-medium text-gray-500"><%= @user.email %></span>
            </div>
          </li>
        </ol>
      </nav>
      <h2 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        User Details
      </h2>
    </div>
    <div class="mt-4 flex md:mt-0 md:ml-4">
      <%= link_to edit_admin_user_path(@user), 
          class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        Edit User
      <% end %>
    </div>
  </div>

  <!-- User Overview Cards -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- User Info Card -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0 h-16 w-16">
          <div class="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
            <span class="text-xl font-medium text-gray-700">
              <%= @user.email.first.upcase %>
            </span>
          </div>
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-medium text-gray-900"><%= @user.email %></h3>
          <div class="flex items-center space-x-2 mt-1">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                         <%= @user.role == 'super_admin' ? 'bg-purple-100 text-purple-800' : 
                             @user.role == 'admin' ? 'bg-red-100 text-red-800' : 
                             'bg-gray-100 text-gray-800' %>">
              <%= @user.role.humanize %>
            </span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                         <%= @user.status == 'active' ? 'bg-green-100 text-green-800' : 
                             @user.status == 'inactive' ? 'bg-yellow-100 text-yellow-800' : 
                             'bg-red-100 text-red-800' %>">
              <%= @user.status.humanize %>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Points Card -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Total Points</p>
          <p class="text-2xl font-bold text-gray-900"><%= @user.wallet&.points || 0 %></p>
        </div>
      </div>
    </div>

    <!-- Activity Card -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Sign-ins</p>
          <p class="text-2xl font-bold text-gray-900"><%= @user.sign_in_count %></p>
          <% if @user.last_sign_in_at %>
            <p class="text-xs text-gray-500">Last: <%= @user.last_sign_in_at.strftime('%b %d, %Y') %></p>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Information -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- User Details -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">User Information</h3>
      </div>
      <div class="px-6 py-5">
        <dl class="space-y-4">
          <div>
            <dt class="text-sm font-medium text-gray-500">Email</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @user.email %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Role</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @user.role.humanize %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Status</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @user.status.humanize %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Email Confirmed</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <% if @user.confirmed? %>
                <span class="text-green-600">Yes</span>
                <span class="text-gray-500">(<%= @user.confirmed_at.strftime('%b %d, %Y') %>)</span>
              <% else %>
                <span class="text-red-600">No</span>
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Joined</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @user.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @user.updated_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Store Information -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Store Assignment</h3>
      </div>
      <div class="px-6 py-5">
        <% if @user.store %>
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">Store Name</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @user.store.name %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Phone</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @user.store.phone_number %></dd>
            </div>
            <% if @user.store.address %>
              <div>
                <dt class="text-sm font-medium text-gray-500">Address</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <%= @user.store.address.street %><br>
                  <%= @user.store.address.city %>, <%= @user.store.address.state&.name %> <%= @user.store.address.postal_code %>
                </dd>
              </div>
            <% end %>
            <div>
              <dt class="text-sm font-medium text-gray-500">Store Status</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                             <%= @user.store.status == 'active' ? 'bg-green-100 text-green-800' : 
                                 @user.store.status == 'requested' ? 'bg-yellow-100 text-yellow-800' : 
                                 'bg-red-100 text-red-800' %>">
                  <%= @user.store.status.humanize %>
                </span>
              </dd>
            </div>
          </dl>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No store assigned</h3>
            <p class="mt-1 text-sm text-gray-500">This user is not assigned to any store.</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Sales -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Recent Sales</h3>
      </div>
      <div class="px-6 py-5">
        <% if @recent_sales.any? %>
          <div class="space-y-4">
            <% @recent_sales.each do |sale| %>
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100">
                <div class="flex-1">
                  <p class="text-sm font-semibold text-gray-900"><%= sale.product.name %></p>
                  <p class="text-xs text-gray-500 mt-1">
                    Serial: <%= sale.serial_number %>
                  </p>
                  <p class="text-xs text-gray-500">
                    <%= sale.created_at.strftime('%b %d, %Y') %>
                  </p>
                </div>
                <div class="text-right ml-4">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               <%= sale.status == 'approved' ? 'bg-green-100 text-green-800' : 
                                   sale.status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                   'bg-red-100 text-red-800' %>">
                    <%= sale.status.capitalize %>
                  </span>
                  <p class="text-sm font-bold text-blue-600 mt-1">
                    <%= sale.points %> pts
                  </p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No sales yet</h3>
            <p class="mt-1 text-sm text-gray-500">This user hasn't recorded any sales.</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Recent Orders -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Recent Orders</h3>
      </div>
      <div class="px-6 py-5">
        <% if @recent_orders.any? %>
          <div class="space-y-4">
            <% @recent_orders.each do |order| %>
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100">
                <div class="flex-1">
                  <p class="text-sm font-semibold text-gray-900"><%= order.product.name %></p>
                  <p class="text-xs text-gray-500 mt-1">
                    Shipping: <%= order.shipping_type.humanize %>
                  </p>
                  <p class="text-xs text-gray-500">
                    <%= order.created_at.strftime('%b %d, %Y') %>
                  </p>
                </div>
                <div class="text-right ml-4">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               <%= order.status == 'approved' ? 'bg-green-100 text-green-800' : 
                                   order.status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                   'bg-red-100 text-red-800' %>">
                    <%= order.status.capitalize %>
                  </span>
                  <p class="text-sm font-bold text-red-600 mt-1">
                    -<%= order.points %> pts
                  </p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No orders yet</h3>
            <p class="mt-1 text-sm text-gray-500">This user hasn't placed any orders.</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Wallet Management Section -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    
    <!-- Wallet Transactions -->
    <div class="lg:col-span-2">
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-5 border-b border-gray-200">
          <h3 class="text-lg leading-6 font-semibold text-gray-900">Wallet Transactions</h3>
          <p class="mt-1 text-sm text-gray-500">Recent points transactions and activities</p>
        </div>
        <div class="px-6 py-5">
          <% if @wallet_activities.any? %>
            <div class="space-y-4">
              <% @wallet_activities.each do |activity| %>
                <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div class="flex items-center space-x-3">
                    <!-- Activity Icon -->
                    <div class="flex-shrink-0">
                      <% case activity.key %>
                      <% when 'credit', 'admin_credit' %>
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                          </svg>
                        </div>
                      <% when 'debit', 'admin_debit' %>
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                          <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                          </svg>
                        </div>
                      <% else %>
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                          </svg>
                        </div>
                      <% end %>
                    </div>
                    
                    <!-- Activity Details -->
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center space-x-2">
                        <p class="text-sm font-medium text-gray-900">
                          <% case activity.key %>
                          <% when 'credit' %>
                            Points Earned
                          <% when 'debit' %>
                            Points Spent
                          <% when 'admin_credit' %>
                            Admin Credit
                          <% when 'admin_debit' %>
                            Admin Debit
                          <% else %>
                            <%= activity.key.humanize %>
                          <% end %>
                        </p>
                        
                        <% if activity.key.in?(['admin_credit', 'admin_debit']) %>
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                            Admin Action
                          </span>
                        <% end %>
                      </div>
                      
                      <div class="mt-1">
                        <p class="text-xs text-gray-500">
                          <%= activity.created_at.strftime("%B %d, %Y at %I:%M %p") %>
                          <% if activity.parameters&.dig('admin_user_name') %>
                            - by <%= activity.parameters['admin_user_name'] %>
                          <% end %>
                        </p>
                        
                        <% if activity.parameters&.dig('reason') %>
                          <p class="text-xs text-gray-600 mt-1">
                            <strong>Reason:</strong> <%= activity.parameters['reason'] %>
                          </p>
                        <% end %>
                        
                        <% if activity.parameters&.dig('context_type') %>
                          <p class="text-xs text-gray-600 mt-1">
                            <strong>Related to:</strong> <%= activity.parameters['context_type'] %> #<%= activity.parameters['context_id'] %>
                          </p>
                        <% end %>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Amount -->
                  <div class="flex-shrink-0 text-right">
                    <% amount = activity.parameters&.dig('amount') || 0 %>
                    <p class="text-sm font-semibold <%= activity.key.in?(['credit', 'admin_credit']) ? 'text-green-600' : 'text-red-600' %>">
                      <%= activity.key.in?(['credit', 'admin_credit']) ? '+' : '-' %><%= amount %> pts
                    </p>
                  </div>
                </div>
              <% end %>
            </div>
            
            <% if @wallet_activities.count >= 20 %>
              <div class="mt-6 text-center">
                <p class="text-sm text-gray-500">Showing last 20 transactions</p>
              </div>
            <% end %>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions yet</h3>
              <p class="mt-1 text-sm text-gray-500">This user hasn't had any wallet activity.</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Wallet Management Actions -->
    <div class="space-y-6">
      
      <!-- Current Balance -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-5 border-b border-gray-200">
          <h3 class="text-lg leading-6 font-semibold text-gray-900">Wallet Balance</h3>
        </div>
        <div class="px-6 py-5">
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600"><%= @user.wallet.points %></div>
            <div class="text-sm text-gray-500 mt-1">Available Points</div>
          </div>
        </div>
      </div>

      <!-- Wallet Adjustment -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-5 border-b border-gray-200">
          <h3 class="text-lg leading-6 font-semibold text-gray-900">Adjust Wallet</h3>
          <p class="mt-1 text-sm text-gray-500">Credit or debit points from user's wallet</p>
        </div>
        <div class="px-6 py-5">
          <%= form_with url: "#", method: :post, local: true, class: "space-y-6", id: "wallet-adjustment-form" do |f| %>
            
            <!-- Transaction Type -->
            <%= render 'shared/form_inputs/radio_group',
                label: "Transaction Type",
                name: "transaction_type",
                selected: "credit",
                onchange: "updateFormForTransactionType()",
                options: [
                  {
                    value: "credit",
                    label: "Credit Points",
                    description: "Add points to wallet"
                  },
                  {
                    value: "debit", 
                    label: "Debit Points",
                    description: "Remove points from wallet"
                  }
                ] %>
            
            <!-- Amount -->
            <%= render 'shared/form_inputs/text_field',
                form: f,
                field: :amount,
                type: :number,
                label: "Amount",
                placeholder: "Enter points amount",
                required: true,
                help_text: "Enter the number of points to credit",
                wrapper_class: "amount-field-wrapper" %>
            
            <!-- Reason -->
            <%= render 'shared/form_inputs/textarea_field',
                form: f,
                field: :reason,
                label: "Reason",
                placeholder: "Required: Explain the reason for this adjustment...",
                required: true,
                rows: 3,
                help_text: "This reason will be recorded in the transaction history" %>
            
            <!-- Submit Button -->
            <%= render 'shared/form_inputs/button',
                text: "Adjust Wallet",
                variant: "primary",
                class: "w-full",
                data: { turbo_confirm: "Are you sure you want to make this wallet adjustment?" } %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for dynamic form behavior -->
<script>
  function updateFormForTransactionType() {
    const creditRadio = document.querySelector('input[value="credit"]');
    const debitRadio = document.querySelector('input[value="debit"]');
    const form = document.getElementById('wallet-adjustment-form');
    const amountField = document.querySelector('#amount');
    const amountHelp = document.querySelector('.amount-field-wrapper p');
    const submitButton = document.querySelector('button[type="submit"]');
    
    if (creditRadio.checked) {
      // Credit mode
      form.action = '<%= credit_wallet_admin_user_path(@user) %>';
      amountField.removeAttribute('max');
      amountHelp.textContent = 'Enter the number of points to credit';
      submitButton.textContent = 'Credit Points';
      submitButton.className = submitButton.className.replace(/bg-red-\d+/, 'bg-green-600').replace(/hover:bg-red-\d+/, 'hover:bg-green-700');
      submitButton.setAttribute('data-turbo-confirm', 'Are you sure you want to credit points to this user\'s wallet?');
    } else if (debitRadio.checked) {
      // Debit mode
      form.action = '<%= debit_wallet_admin_user_path(@user) %>';
      amountField.setAttribute('max', '<%= @user.wallet.points %>');
      amountHelp.textContent = 'Enter the number of points to debit (Maximum: <%= @user.wallet.points %> points)';
      submitButton.textContent = 'Debit Points';
      submitButton.className = submitButton.className.replace(/bg-green-\d+/, 'bg-red-600').replace(/hover:bg-green-\d+/, 'hover:bg-red-700');
      submitButton.setAttribute('data-turbo-confirm', 'Are you sure you want to debit points from this user\'s wallet?');
    }
  }
  
  // Initialize form on page load
  document.addEventListener('DOMContentLoaded', function() {
    updateFormForTransactionType();
  });
</script>