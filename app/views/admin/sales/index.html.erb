<% content_for :title, "Sales Management" %>

<!-- Admin Sales Management -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Sales Management
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Review and approve sales submissions
      </p>
    </div>
  </div>

  <!-- Sales Analytics Dashboard (Cached Values) -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
    <!-- Total Sales -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Sales</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@sales_metrics[:total_sales]) %></dd>
              <dd class="text-xs text-gray-500">All time</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Pending Sales -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Pending Sales</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@sales_metrics[:pending_sales]) %></dd>
              <dd class="text-xs text-gray-500">Awaiting approval</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Approved Sales -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Approved Sales</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@sales_metrics[:approved_sales]) %></dd>
              <dd class="text-xs text-gray-500">
                <%= @sales_metrics[:rejected_sales] %> rejected
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Points Awarded -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Points Awarded</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@sales_metrics[:total_points_awarded]) %></dd>
              <dd class="text-xs text-gray-500">
                Avg: <%= @sales_metrics[:average_sale_points] %> per sale
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Performers Section -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Top Stores -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Top Performing Stores</h3>
      <div class="space-y-3">
        <% @sales_metrics[:top_performing_stores].each_with_index do |store, index| %>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="text-sm font-medium text-gray-500 w-6"><%= index + 1 %>.</span>
              <span class="text-sm font-medium text-gray-900 ml-2"><%= store.name %></span>
            </div>
            <span class="text-sm text-gray-500"><%= store.approved_sales_count %> sales</span>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Top Users -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Top Performing Users</h3>
      <div class="space-y-3">
        <% @sales_metrics[:top_performing_users].each_with_index do |user, index| %>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="text-sm font-medium text-gray-500 w-6"><%= index + 1 %>.</span>
              <span class="text-sm font-medium text-gray-900 ml-2"><%= user.email %></span>
            </div>
            <span class="text-sm text-gray-500"><%= user.approved_sales_count %> sales</span>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Top Products -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Top Selling Products</h3>
      <div class="space-y-3">
        <% @sales_metrics[:top_selling_products].each_with_index do |product, index| %>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="text-sm font-medium text-gray-500 w-6"><%= index + 1 %>.</span>
              <span class="text-sm font-medium text-gray-900 ml-2"><%= truncate(product.name, length: 25) %></span>
            </div>
            <span class="text-sm text-gray-500"><%= product.sales_count %> sales</span>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
    <%= search_form_for @q, url: admin_sales_path, local: true, class: "space-y-4" do |f| %>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <%= render "shared/form_inputs/select_field",
            form: f,
            field: :status_eq,
            label: "Status",
            options: [
              ['All Statuses', ''],
              ['Pending', 'pending'],
              ['Approved', 'approved'],
              ['Rejected', 'rejected']
            ],
            selected: params.dig(:q, :status_eq) %>

        <%= render "shared/form_inputs/text_field",
            form: f,
            field: :user_email_cont,
            label: "User Email",
            placeholder: "Search by email..." %>

        <%= render "shared/form_inputs/text_field",
            form: f,
            field: :product_name_cont,
            label: "Product Name",
            placeholder: "Search by product..." %>

        <div class="flex items-end">
          <%= render "shared/form_inputs/button",
              text: "Search",
              type: "submit",
              variant: "primary",
              class: "w-full" %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Sales Table -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Sales List</h3>
      <p class="mt-1 text-sm text-gray-500">
        <%= pluralize(@pagy.count, 'sale') %> found
      </p>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Sale Details
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              User
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Product
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Points
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Date
            </th>
            <th scope="col" class="relative px-6 py-3">
              <span class="sr-only">Actions</span>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @sales.each do |sale| %>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">#<%= sale.id %></div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div>
                    <div class="text-sm font-medium text-gray-900"><%= sale.user.email %></div>
                    <div class="text-sm text-gray-500"><%= sale.user.store&.name %></div>
                  </div>
                </div>
              </td>

              <td class="px-6 py-4">
                <div class="text-sm font-medium text-gray-900"><%= truncate(sale.product.name, length: 30) %></div>
                <div class="text-sm text-gray-500">SKU: <%= sale.product.sku %></div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900"><%= sale.points %></div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap">
                <% case sale.status %>
                <% when 'pending' %>
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                <% when 'approved' %>
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Approved
                  </span>
                <% when 'rejected' %>
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    Rejected
                  </span>
                <% end %>
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div><%= sale.created_at.strftime("%b %d, %Y") %></div>
                <div class="text-xs"><%= sale.created_at.strftime("%I:%M %p") %></div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <%= link_to "View", admin_sale_path(sale),
                    class: "text-blue-600 hover:text-blue-900" %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <%== pagy_nav(@pagy) if respond_to?(:pagy_nav) %>
      </div>
    <% end %>
  </div>
</div>