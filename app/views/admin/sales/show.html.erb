<% content_for :title, "Sale ##{@sale.id}" %>

<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <%= link_to admin_sales_path, class: "text-gray-400 hover:text-gray-500" do %>
              <span>Sales</span>
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-4 text-sm font-medium text-gray-500">Sale #<%= @sale.id %></span>
            </div>
          </li>
        </ol>
      </nav>
      <h2 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Sale Details
      </h2>
    </div>
  </div>

  <!-- Sale Status Card -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-medium text-gray-900">Sale #<%= @sale.id %></h3>
        <p class="mt-1 text-sm text-gray-500">
          Submitted on <%= @sale.created_at.strftime("%B %d, %Y at %I:%M %p") %>
        </p>
      </div>
      <div class="flex items-center space-x-4">
        <!-- Status Badge -->
        <% case @sale.status %>
        <% when 'pending' %>
          <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">
            Pending Approval
          </span>
        <% when 'approved' %>
          <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">
            Approved
          </span>
        <% when 'rejected' %>
          <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800">
            Rejected
          </span>
        <% end %>

        <!-- Action Buttons -->
        <% if @sale.pending? %>
          <div class="flex space-x-2">
            <%= button_to "Approve", approve_admin_sale_path(@sale),
                method: :patch,
                class: "bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium",
                confirm: "Are you sure you want to approve this sale?" %>
            <%= button_to "Reject", reject_admin_sale_path(@sale),
                method: :patch,
                class: "bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium",
                confirm: "Are you sure you want to reject this sale?" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Sale Information Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Customer Information -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-gray-500">Email</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= link_to @sale.user.email, admin_user_path(@sale.user),
                class: "text-blue-600 hover:text-blue-800" %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500">Name</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= [@sale.user.first_name, @sale.user.last_name].compact.join(' ').presence || 'Not provided' %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500">Store</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <% if @sale.user.store %>
              <%= link_to @sale.user.store.name, admin_store_path(@sale.user.store),
                  class: "text-blue-600 hover:text-blue-800" %>
            <% else %>
              <span class="text-gray-400">No store assigned</span>
            <% end %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500">Current Points Balance</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= number_with_delimiter(@sale.user.wallet.points) %> points
          </dd>
        </div>
      </dl>
    </div>

    <!-- Product Information -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Product Information</h3>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-gray-500">Product Name</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= link_to @sale.product.name, admin_product_path(@sale.product),
                class: "text-blue-600 hover:text-blue-800" %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500">SKU</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @sale.product.sku %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500">Category</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @sale.product.category.name %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500">Points Awarded</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <span class="text-lg font-semibold text-green-600"><%= @sale.points %></span> points
          </dd>
        </div>
      </dl>
    </div>
  </div>

  <!-- Sale Details -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Sale Details</h3>
    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
      <% if @sale.sold_at.present? %>
        <div>
          <dt class="text-sm font-medium text-gray-500">Sale Date</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @sale.sold_at.strftime("%B %d, %Y") %></dd>
        </div>
      <% end %>

      <div>
        <dt class="text-sm font-medium text-gray-500">Submission Date</dt>
        <dd class="mt-1 text-sm text-gray-900"><%= @sale.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
      </div>

      <% if @sale.approved_at.present? %>
        <div>
          <dt class="text-sm font-medium text-gray-500">Approved Date</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @sale.approved_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
        </div>
      <% end %>

      <% if @sale.approved_by_admin.present? %>
        <div>
          <dt class="text-sm font-medium text-gray-500">Approved By</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= link_to @sale.approved_by_admin.email, admin_user_path(@sale.approved_by_admin),
              class: "text-blue-600 hover:text-blue-800" %>
          </dd>
        </div>
      <% end %>

      <% if @sale.notes.present? %>
        <div class="sm:col-span-2">
          <dt class="text-sm font-medium text-gray-500">Notes</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= simple_format(@sale.notes) %></dd>
        </div>
      <% end %>
    </dl>
  </div>

  <!-- Promotion Bonuses -->
  <% if @sale.sale_promotion_bonuses.any? %>
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Promotion Bonuses</h3>
      <div class="space-y-3">
        <% @sale.sale_promotion_bonuses.includes(:promotion).each do |bonus| %>
          <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div>
              <h4 class="text-sm font-medium text-gray-900"><%= bonus.promotion.name %></h4>
              <p class="text-sm text-gray-500"><%= bonus.promotion.description %></p>
            </div>
            <div class="text-right">
              <span class="text-sm font-medium text-green-600">+<%= bonus.bonus_points %> bonus points</span>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Admin Actions -->
  <% if @sale.pending? %>
    <div class="bg-gray-50 shadow-sm rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Admin Actions</h3>
      <div class="space-y-4">
        <div class="flex space-x-4">
          <%= button_to "Approve Sale", approve_admin_sale_path(@sale),
            method: :patch,
            class: "bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md text-sm font-medium",
            confirm: "Are you sure you want to approve this sale? This will award #{@sale.points} points to the user." %>

          <%= button_to "Reject Sale", reject_admin_sale_path(@sale),
            method: :patch,
            class: "bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md text-sm font-medium",
            confirm: "Are you sure you want to reject this sale?" %>
        </div>

        <p class="text-sm text-gray-500">
          <strong>Note:</strong> Approving this sale will immediately award <%= @sale.points %> points to <%= @sale.user.email %>'s account.
          Rejecting will send a notification to the user.
        </p>
      </div>
    </div>
  <% end %>
</div>