<!-- Admin Order Detail -->
<div class="min-h-screen bg-gray-50">
  
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to admin_orders_path, 
              class: "p-2 rounded-full hover:bg-gray-100 transition-colors",
              title: "Back to Orders" do %>
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
            </svg>
          <% end %>
          
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Order #<%= @order.id %></h1>
            <p class="text-gray-600 text-sm mt-1">
              Placed on <%= @order.created_at.strftime("%B %d, %Y at %I:%M %p") %>
            </p>
          </div>
        </div>
        
        <!-- Status Badge -->
        <div>
          <% case @order.status %>
          <% when 'pending' %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
              </svg>
              Pending Approval
            </span>
          <% when 'approved' %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              Approved
            </span>
          <% when 'shipped' %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM9 9a1 1 0 112 0v4a1 1 0 11-2 0V9z" clip-rule="evenodd"></path>
              </svg>
              Shipped
            </span>
          <% when 'delivered' %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Delivered
            </span>
          <% when 'rejected' %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              Rejected
            </span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="px-4 sm:px-6 lg:px-8 py-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      
      <!-- Left Column: Order Details -->
      <div class="lg:col-span-2 space-y-6">
        
        <!-- Order Items -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Order Items</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <% @order.line_items.includes(:product).each do |item| %>
                <div class="flex items-center space-x-4 py-4 border-b border-gray-100 last:border-b-0">
                  <!-- Product Image -->
                  <div class="w-16 h-16 bg-gray-100 rounded-lg flex-shrink-0 overflow-hidden">
                    <%= product_image_tag(item.product, class: "w-full h-full object-cover") %>
                  </div>
                  
                  <!-- Product Details -->
                  <div class="flex-1 min-w-0">
                    <h4 class="font-medium text-gray-900"><%= item.product.name %></h4>
                    <p class="text-sm text-gray-500"><%= item.product.category.name %></p>
                    <p class="text-sm text-gray-600 mt-1">SKU: <%= item.product.sku %></p>
                  </div>
                  
                  <!-- Quantity and Points -->
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900">Qty: <%= item.quantity %></div>
                    <div class="text-sm text-gray-500"><%= item.product.points_required %> pts each</div>
                    <div class="text-sm font-semibold text-blue-600"><%= item.total_points %> pts total</div>
                  </div>
                </div>
              <% end %>
            </div>
            
            <!-- Order Total -->
            <div class="mt-6 pt-4 border-t border-gray-200">
              <div class="flex justify-between items-center">
                <span class="text-lg font-semibold text-gray-900">Total Points</span>
                <span class="text-xl font-bold text-blue-600"><%= @order.total_points %> pts</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Shipping Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Shipping Information</h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 class="font-medium text-gray-900 mb-2">Shipping Method</h4>
                <% if @order.shipping_type == 'store' %>
                  <div class="flex items-center text-green-600">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                    </svg>
                    Ship to Store
                  </div>
                  <div class="mt-2 text-sm text-gray-600">
                    <div class="font-medium"><%= @order.user.store.name %></div>
                    <% if @order.user.store.address %>
                      <div><%= @order.user.store.address.street_address %></div>
                      <div><%= @order.user.store.address.city %>, <%= @order.user.store.address.state&.name %></div>
                    <% end %>
                  </div>
                <% else %>
                  <div class="flex items-center text-blue-600">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Ship to Address
                  </div>
                  <% if @order.shipping_address.present? %>
                    <div class="mt-2 text-sm text-gray-600">
                      <%= simple_format(@order.shipping_address) %>
                    </div>
                  <% else %>
                    <div class="mt-2 text-sm text-red-600">No shipping address provided</div>
                  <% end %>
                <% end %>
              </div>
              
              <div>
                <h4 class="font-medium text-gray-900 mb-2">Tracking Information</h4>
                <% if @order.shipped? || @order.delivered? %>
                  <div class="text-sm text-gray-600">
                    <div>Shipped: <%= @order.shipped_at&.strftime("%b %d, %Y at %I:%M %p") %></div>
                    <% if @order.delivered? %>
                      <div>Delivered: <%= @order.delivered_at&.strftime("%b %d, %Y at %I:%M %p") %></div>
                    <% end %>
                  </div>
                <% else %>
                  <div class="text-sm text-gray-500">Not yet shipped</div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: Customer & Actions -->
      <div class="space-y-6">
        
        <!-- SAP Integration -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">SAP Integration</h3>
          </div>
          <div class="p-6">
            <% if @order.sap_processed? %>
              <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                  <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div>
                  <div class="font-medium text-gray-900">Processed in SAP</div>
                  <div class="text-sm text-gray-500">Order has been sent to SAP system</div>
                </div>
              </div>
              
              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-500">SAP ID:</span>
                  <span class="font-medium font-mono"><%= @order.sap_id %></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">Processed At:</span>
                  <span class="font-medium"><%= @order.sap_processed_at.strftime("%B %d, %Y at %I:%M %p") %></span>
                </div>
              </div>
            <% else %>
              <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center">
                  <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div>
                  <div class="font-medium text-gray-900">Pending SAP Processing</div>
                  <div class="text-sm text-gray-500">Order will be sent to SAP when approved</div>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Customer Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Customer</h3>
          </div>
          <div class="p-6">
            <div class="flex items-center space-x-4 mb-4">
              <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                <span class="text-lg font-medium text-gray-600">
                  <%= @order.user.first_name&.first&.upcase || 'U' %>
                </span>
              </div>
              <div>
                <div class="font-medium text-gray-900">
                  <%= @order.user.first_name %> <%= @order.user.last_name %>
                </div>
                <div class="text-sm text-gray-500"><%= @order.user.email %></div>
              </div>
            </div>
            
            <div class="space-y-3 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-500">Store:</span>
                <span class="font-medium"><%= @order.user.store.name %></span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">Points Balance:</span>
                <span class="font-medium text-blue-600"><%= @order.user.wallet.points %> pts</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">Status:</span>
                <span class="font-medium capitalize">
                  <% case @order.user.status %>
                  <% when 'active' %>
                    <span class="text-green-600">Active</span>
                  <% when 'inactive' %>
                    <span class="text-red-600">Inactive</span>
                  <% when 'pending' %>
                    <span class="text-yellow-600">Pending</span>
                  <% end %>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Actions</h3>
          </div>
          <div class="p-6 space-y-3">
            <% if @order.pending? %>
              <%= button_to approve_admin_order_path(@order), 
                  method: :post,
                  class: "w-full flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors",
                  data: { turbo_confirm: "Approve this order? #{@order.total_points} points will be deducted from the user's wallet." } do %>
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Approve Order
              <% end %>
              
              <%= button_to reject_admin_order_path(@order), 
                  method: :post,
                  class: "w-full flex items-center justify-center px-4 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors",
                  data: { turbo_confirm: "Reject this order?" } do %>
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Reject Order
              <% end %>
              
            <% elsif @order.approved? %>
              <%= button_to ship_admin_order_path(@order), 
                  method: :post,
                  class: "w-full flex items-center justify-center px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors",
                  data: { turbo_confirm: "Mark this order as shipped?" } do %>
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                Mark as Shipped
              <% end %>
              
              <%= button_to reject_admin_order_path(@order), 
                  method: :post,
                  class: "w-full flex items-center justify-center px-4 py-3 border border-red-300 text-red-700 hover:bg-red-50 font-medium rounded-lg transition-colors",
                  data: { turbo_confirm: "Reject this order? Points will be refunded to the user." } do %>
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Reject & Refund
              <% end %>
              
            <% elsif @order.shipped? %>
              <%= button_to deliver_admin_order_path(@order), 
                  method: :post,
                  class: "w-full flex items-center justify-center px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors",
                  data: { turbo_confirm: "Mark this order as delivered?" } do %>
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Mark as Delivered
              <% end %>
              
            <% elsif @order.delivered? %>
              <div class="text-center py-4">
                <svg class="mx-auto h-12 w-12 text-green-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p class="text-sm text-gray-600">Order completed successfully</p>
              </div>
              
            <% elsif @order.rejected? %>
              <div class="text-center py-4">
                <svg class="mx-auto h-12 w-12 text-red-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <p class="text-sm text-gray-600">Order was rejected</p>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Order Timeline -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Order Timeline</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <!-- Order Placed -->
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <div class="text-sm font-medium text-gray-900">Order Placed</div>
                  <div class="text-xs text-gray-500"><%= @order.created_at.strftime("%b %d, %Y at %I:%M %p") %></div>
                </div>
              </div>
              
              <!-- Approved -->
              <% if @order.approved_at %>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">Order Approved</div>
                    <div class="text-xs text-gray-500"><%= @order.approved_at.strftime("%b %d, %Y at %I:%M %p") %></div>
                  </div>
                </div>
              <% end %>
              
              <!-- Shipped -->
              <% if @order.shipped_at %>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">Order Shipped</div>
                    <div class="text-xs text-gray-500"><%= @order.shipped_at.strftime("%b %d, %Y at %I:%M %p") %></div>
                  </div>
                </div>
              <% end %>
              
              <!-- Delivered -->
              <% if @order.delivered_at %>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">Order Delivered</div>
                    <div class="text-xs text-gray-500"><%= @order.delivered_at.strftime("%b %d, %Y at %I:%M %p") %></div>
                  </div>
                </div>
              <% end %>
              
              <!-- Rejected -->
              <% if @order.rejected_at %>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">Order Rejected</div>
                    <div class="text-xs text-gray-500"><%= @order.rejected_at.strftime("%b %d, %Y at %I:%M %p") %></div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>