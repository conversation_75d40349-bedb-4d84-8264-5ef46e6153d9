<!-- Admin Orders Management -->
<div class="min-h-screen bg-gray-50">
  
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Order Management</h1>
          <p class="text-gray-600 text-sm mt-1">Manage and approve customer orders</p>
        </div>
        
        <!-- Quick Stats -->
        <div class="hidden md:flex space-x-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600"><%= @stats[:pending] %></div>
            <div class="text-xs text-gray-500">Pending</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600"><%= @stats[:approved] %></div>
            <div class="text-xs text-gray-500">Approved</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600"><%= @stats[:shipped] %></div>
            <div class="text-xs text-gray-500">Shipped</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="bg-white border-b border-gray-200">
    <div class="px-4 sm:px-6 lg:px-8 py-4">
      <%= form_with url: admin_orders_path, method: :get, local: true, class: "flex flex-col sm:flex-row gap-4" do |f| %>
        <!-- Search -->
        <div class="flex-1">
          <%= f.text_field :search, 
              placeholder: "Search by order ID, customer name, or email...", 
              value: params[:search],
              class: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" %>
        </div>
        
        <!-- Status Filter -->
        <div class="sm:w-48">
          <%= f.select :status, 
              options_for_select([
                ['All Statuses', ''],
                ['Pending', 'pending'],
                ['Approved', 'approved'],
                ['Shipped', 'shipped'],
                ['Delivered', 'delivered'],
                ['Rejected', 'rejected']
              ], params[:status]),
              {},
              class: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" %>
        </div>
        
        <!-- Submit -->
        <div class="sm:w-auto">
          <%= f.submit 'Filter', class: "w-full sm:w-auto px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors" %>
        </div>
        
        <!-- Clear -->
        <% if params[:search].present? || params[:status].present? %>
          <div class="sm:w-auto">
            <%= link_to admin_orders_path, class: "w-full sm:w-auto px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 font-medium rounded-lg transition-colors text-center block" do %>
              Clear
            <% end %>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Orders Table -->
  <div class="px-4 sm:px-6 lg:px-8 py-6">
    <% if @orders.any? %>
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SAP ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @orders.each do |order| %>
                <tr class="hover:bg-gray-50">
                  <!-- Order ID -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">#<%= order.id %></div>
                    <div class="text-xs text-gray-500">
                      <%= order.shipping_type == 'store' ? 'Ship to Store' : 'Ship to Address' %>
                    </div>
                  </td>
                  
                  <!-- Customer -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                          <span class="text-xs font-medium text-gray-600">
                            <%= order.user.first_name&.first&.upcase || 'U' %>
                          </span>
                        </div>
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">
                          <%= order.user.first_name %> <%= order.user.last_name %>
                        </div>
                        <div class="text-xs text-gray-500"><%= order.user.email %></div>
                      </div>
                    </div>
                  </td>
                  
                  <!-- Items -->
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900">
                      <%= pluralize(order.line_items.sum(:quantity), 'item') %>
                    </div>
                    <div class="text-xs text-gray-500">
                      <% order.line_items.limit(2).each do |item| %>
                        <div><%= item.product.name %> (×<%= item.quantity %>)</div>
                      <% end %>
                      <% if order.line_items.count > 2 %>
                        <div class="text-gray-400">+<%= order.line_items.count - 2 %> more...</div>
                      <% end %>
                    </div>
                  </td>
                  
                  <!-- Points -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900"><%= order.total_points %> pts</div>
                    <div class="text-xs text-gray-500">
                      User has: <%= order.user.wallet.points %> pts
                    </div>
                  </td>
                  
                  <!-- SAP ID -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% if order.sap_processed? %>
                      <div class="text-sm font-medium text-gray-900"><%= order.sap_id %></div>
                      <div class="text-xs text-gray-500">
                        Processed: <%= order.sap_processed_at.strftime("%m/%d %H:%M") %>
                      </div>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Pending
                      </span>
                    <% end %>
                  </td>
                  
                  <!-- Status -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% case order.status %>
                    <% when 'pending' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        Pending
                      </span>
                    <% when 'approved' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Approved
                      </span>
                    <% when 'shipped' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM9 9a1 1 0 112 0v4a1 1 0 11-2 0V9z" clip-rule="evenodd"></path>
                        </svg>
                        Shipped
                      </span>
                    <% when 'delivered' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        Delivered
                      </span>
                    <% when 'rejected' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                        Rejected
                      </span>
                    <% end %>
                  </td>
                  
                  <!-- Date -->
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div><%= order.created_at.strftime("%b %d, %Y") %></div>
                    <div class="text-xs"><%= order.created_at.strftime("%I:%M %p") %></div>
                  </td>
                  
                  <!-- Actions -->
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <%= link_to admin_order_path(order), 
                          class: "text-blue-600 hover:text-blue-900 transition-colors",
                          title: "View Details" do %>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                      <% end %>
                      
                      <% if order.pending? %>
                        <%= button_to approve_admin_order_path(order), 
                            method: :post,
                            class: "text-green-600 hover:text-green-900 transition-colors",
                            title: "Approve Order",
                            data: { turbo_confirm: "Approve this order? Points will be deducted from user's wallet." } do %>
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                        <% end %>
                        
                        <%= button_to reject_admin_order_path(order), 
                            method: :post,
                            class: "text-red-600 hover:text-red-900 transition-colors",
                            title: "Reject Order",
                            data: { turbo_confirm: "Reject this order?" } do %>
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                          </svg>
                        <% end %>
                      <% elsif order.approved? %>
                        <%= button_to ship_admin_order_path(order), 
                            method: :post,
                            class: "text-blue-600 hover:text-blue-900 transition-colors",
                            title: "Mark as Shipped",
                            data: { turbo_confirm: "Mark this order as shipped?" } do %>
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                          </svg>
                        <% end %>
                      <% elsif order.shipped? %>
                        <%= button_to deliver_admin_order_path(order), 
                            method: :post,
                            class: "text-purple-600 hover:text-purple-900 transition-colors",
                            title: "Mark as Delivered",
                            data: { turbo_confirm: "Mark this order as delivered?" } do %>
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                        <% end %>
                      <% end %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Pagination -->
      <% if @orders.respond_to?(:total_pages) && @orders.total_pages > 1 %>
        <div class="mt-6 flex justify-center">
          <%= paginate @orders %>
        </div>
      <% end %>

    <% else %>
      <!-- Empty State -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
        <svg class="mx-auto h-16 w-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
        </svg>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">No orders found</h3>
        <p class="text-gray-600 mb-6">
          <% if params[:search].present? || params[:status].present? %>
            No orders match your current filters. Try adjusting your search criteria.
          <% else %>
            No orders have been placed yet.
          <% end %>
        </p>
        <% if params[:search].present? || params[:status].present? %>
          <%= link_to admin_orders_path, 
              class: "inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            View All Orders
          <% end %>
        <% end %>
      </div>
    <% end %>
  </div>
</div>