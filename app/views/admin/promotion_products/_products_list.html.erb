<% products.each do |product| %>
  <div class="flex items-start p-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 transition-colors">
    <input type="checkbox"
           value="<%= product.id %>"
           <%= 'checked' if selected_ids.include?(product.id) %>
           data-action="change->product-selector#toggleProduct"
           data-product-name="<%= product.name %>"
           data-product-sku="<%= product.sku %>"
           data-product-category="<%= product.category&.name %>"
           class="mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">

    <label class="ml-3 flex-1 cursor-pointer">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="text-sm font-medium text-gray-900 mb-1"><%= product.name %></div>
          <div class="flex items-center space-x-3 text-xs text-gray-500">
            <span class="font-mono bg-gray-100 px-2 py-1 rounded">SKU: <%= product.sku %></span>
            <% if product.category %>
              <span class="bg-blue-100 text-blue-700 px-2 py-1 rounded"><%= product.category.name %></span>
            <% end %>
            <% if product.points_required > 0 %>
              <span class="bg-green-100 text-green-700 px-2 py-1 rounded"><%= product.points_required %> pts</span>
            <% end %>
          </div>
          <% if product.description.present? %>
            <div class="text-xs text-gray-400 mt-1 line-clamp-2"><%= truncate(product.description, length: 100) %></div>
          <% end %>
        </div>
        <% if product.image.attached? %>
          <div class="ml-3 flex-shrink-0">
            <%= image_tag product.image, class: "w-12 h-12 object-cover rounded border border-gray-200" %>
          </div>
        <% end %>
      </div>
    </label>
  </div>
<% end %>