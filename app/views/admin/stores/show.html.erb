<!-- Admin Store Detail View -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <%= link_to admin_stores_path, class: "text-gray-400 hover:text-gray-500" do %>
              <span>Stores</span>
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-4 text-sm font-medium text-gray-500"><%= @store.name %></span>
            </div>
          </li>
        </ol>
      </nav>
      <h2 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Store Details
      </h2>
    </div>
    <div class="mt-4 flex md:mt-0 md:ml-4">
      <%= link_to edit_admin_store_path(@store),
          class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        Edit Store
      <% end %>
    </div>
  </div>

  <!-- Store Performance Metrics (Cached Values) -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
    <!-- Users Count -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Users</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@store_stats[:users_count]) %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Sales Count -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Sales</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@store_stats[:sales_count]) %></dd>
              <dd class="text-xs text-gray-500">
                <%= @store_stats[:approved_sales_count] %> approved, 
                <%= @store_stats[:pending_sales_count] %> pending
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Orders Count -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Orders</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@store_stats[:orders_count]) %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Points Awarded -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Points Awarded</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@store_stats[:total_sales_points]) %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Store Overview Cards -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Store Info Card -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0 h-16 w-16">
          <div class="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-medium text-gray-900"><%= @store.name %></h3>
          <div class="flex items-center space-x-2 mt-1">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                         <%= @store.status == 'active' ? 'bg-green-100 text-green-800' :
                             @store.status == 'requested' ? 'bg-yellow-100 text-yellow-800' :
                             'bg-red-100 text-red-800' %>">
              <%= @store.status.humanize %>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Users Card -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Assigned Users</p>
          <p class="text-2xl font-bold text-gray-900"><%= @store_stats[:users_count] %></p>
        </div>
      </div>
    </div>

    <!-- Sales Activity Card -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Recent Sales</p>
          <p class="text-2xl font-bold text-gray-900"><%= @store_stats[:sales_count] %></p>
          <p class="text-xs text-gray-500">Last 30 days</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Information -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Store Details -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Store Information</h3>
      </div>
      <div class="px-6 py-5">
        <dl class="space-y-4">
          <div>
            <dt class="text-sm font-medium text-gray-500">Store Name</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.name %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.phone_number || 'Not provided' %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Status</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.status.humanize %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Brand</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <% if @store.brand %>
                <%= @store.brand.name %>
              <% else %>
                <span class="text-gray-400">No brand</span>
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Store Chain</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <% if @store.store_chain %>
                <div class="flex items-center justify-between">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                    <%= @store.store_chain.name %>
                  </span>
                </div>
              <% else %>
                <span class="text-gray-400">No chain assigned</span>
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Added</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.updated_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Address Information -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Address</h3>
      </div>
      <div class="px-6 py-5">
        <% if @store.address %>
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">Street Address</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.street %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">City</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.city %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">State</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.state&.name %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Postal Code</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.postal_code %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Country</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.country&.name %></dd>
            </div>
          </dl>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No address provided</h3>
            <p class="mt-1 text-sm text-gray-500">This store doesn't have an address on file.</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Store Chain Management -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-5 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Store Chain Management</h3>
        <% if @store.store_chain %>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
            <%= @store.store_chain.name %>
          </span>
        <% end %>
      </div>
    </div>
    <div class="px-6 py-5">
      <%= form_with url: update_chain_admin_store_path(@store), method: :patch, local: true, class: "space-y-4" do |f| %>
        
        <!-- Chain Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-900 mb-3">Chain Assignment</label>
          
          <div class="space-y-3">
            <!-- Remove from chain option -->
            <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
              <%= radio_button_tag :chain_action, 'remove', @store.store_chain.nil?, 
                  class: "aspect-square h-4 w-4 rounded-full border border-gray-300 text-red-600 shadow focus:ring-2 focus:ring-red-500 focus:ring-offset-2",
                  onchange: "toggleChainOptions()" %>
              <div class="flex-1">
                <span class="text-sm font-medium text-red-600">Remove from chain</span>
                <p class="text-xs text-gray-500 mt-1">
                  <% if @store.store_chain %>
                    Unlink from "<%= @store.store_chain.name %>"
                  <% else %>
                    Keep unassigned
                  <% end %>
                </p>
              </div>
            </label>

            <!-- Existing chain option -->
            <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
              <%= radio_button_tag :chain_action, 'existing', @store.store_chain.present?, 
                  class: "aspect-square h-4 w-4 rounded-full border border-gray-300 text-blue-600 shadow focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                  onchange: "toggleChainOptions()" %>
              <div class="flex-1">
                <span class="text-sm font-medium text-blue-600">Assign to existing chain</span>
                <p class="text-xs text-gray-500 mt-1">Choose from available chains</p>
              </div>
            </label>

            <!-- New chain option -->
            <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
              <%= radio_button_tag :chain_action, 'new', false, 
                  class: "aspect-square h-4 w-4 rounded-full border border-gray-300 text-green-600 shadow focus:ring-2 focus:ring-green-500 focus:ring-offset-2",
                  onchange: "toggleChainOptions()" %>
              <div class="flex-1">
                <span class="text-sm font-medium text-green-600">Create new chain</span>
                <p class="text-xs text-gray-500 mt-1">Create and assign to a new chain</p>
              </div>
            </label>
          </div>
        </div>

        <!-- Existing Chain Selection -->
        <div id="existing-chain-section" class="hidden">
          <label for="store_chain_id" class="block text-sm font-medium text-gray-900 mb-2">Select Chain</label>
          <%= select_tag :store_chain_id, 
              options_from_collection_for_select(StoreChain.order(:name), :id, :name, @store.store_chain_id),
              { prompt: 'Choose a chain...', class: "flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" } %>
        </div>

        <!-- New Chain Creation -->
        <div id="new-chain-section" class="hidden">
          <label for="new_chain_name" class="block text-sm font-medium text-gray-900 mb-2">New Chain Name</label>
          <%= text_field_tag :new_chain_name, '', 
              placeholder: "Enter new chain name...",
              class: "flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" %>
          <p class="mt-2 text-xs text-gray-500">A new chain will be created with this name</p>
        </div>

        <!-- Submit Button -->
        <div class="pt-4">
          <%= submit_tag "Update Chain Assignment", 
              class: "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2",
              data: { turbo_confirm: "Are you sure you want to update this store's chain assignment?" } %>
        </div>
      <% end %>

      <!-- Chain Statistics -->
      <% if StoreChain.any? %>
        <div class="mt-8 pt-6 border-t border-gray-200">
          <h4 class="text-sm font-medium text-gray-900 mb-4">Existing Chains</h4>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <% StoreChain.includes(:stores).order(:name).each do |chain| %>
              <div class="p-3 border border-gray-200 rounded-lg <%= @store.store_chain == chain ? 'bg-blue-50 border-blue-200' : 'bg-gray-50' %>">
                <div class="flex items-center justify-between">
                  <h5 class="text-sm font-medium text-gray-900"><%= chain.name %></h5>
                  <span class="text-xs text-gray-500"><%= pluralize(chain.stores.count, 'store') %></span>
                </div>
                <% if @store.store_chain == chain %>
                  <p class="text-xs text-blue-600 mt-1">Current chain</p>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Users and Activity -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Assigned Users -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Assigned Users</h3>
      </div>
      <div class="px-6 py-5">
        <% if @users.any? %>
          <div class="space-y-4">
            <% @users.each do |user| %>
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-8 w-8">
                    <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                      <span class="text-xs font-medium text-gray-700">
                        <%= user.email.first.upcase %>
                      </span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-semibold text-gray-900"><%= user.email %></p>
                    <div class="flex items-center space-x-2 mt-1">
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                   <%= user.role == 'admin' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800' %>">
                        <%= user.role.humanize %>
                      </span>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                   <%= user.status == 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                        <%= user.status.humanize %>
                      </span>
                    </div>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-sm font-medium text-blue-600"><%= user.wallet&.points || 0 %> pts</p>
                  <%= link_to admin_user_path(user),
                      class: "text-xs text-blue-600 hover:text-blue-800" do %>
                    View →
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No users assigned</h3>
            <p class="mt-1 text-sm text-gray-500">No users are currently assigned to this store.</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Recent Sales -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Recent Sales</h3>
      </div>
      <div class="px-6 py-5">
        <% if @recent_sales.any? %>
          <div class="space-y-4">
            <% @recent_sales.each do |sale| %>
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100">
                <div class="flex-1">
                  <p class="text-sm font-semibold text-gray-900"><%= sale.product.name %></p>
                  <p class="text-xs text-gray-500 mt-1">
                    By <%= sale.user.email %> • <%= sale.serial_number %>
                  </p>
                  <p class="text-xs text-gray-500">
                    <%= sale.created_at.strftime('%b %d, %Y') %>
                  </p>
                </div>
                <div class="text-right ml-4">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               <%= sale.status == 'approved' ? 'bg-green-100 text-green-800' :
                                   sale.status == 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                   'bg-red-100 text-red-800' %>">
                    <%= sale.status.capitalize %>
                  </span>
                  <p class="text-sm font-bold text-blue-600 mt-1">
                    <%= sale.points %> pts
                  </p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No sales yet</h3>
            <p class="mt-1 text-sm text-gray-500">No sales have been recorded for this store.</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for chain management form -->
<script>
  function toggleChainOptions() {
    const removeRadio = document.querySelector('input[value="remove"]');
    const existingRadio = document.querySelector('input[value="existing"]');
    const newRadio = document.querySelector('input[value="new"]');
    
    const existingSection = document.getElementById('existing-chain-section');
    const newSection = document.getElementById('new-chain-section');
    
    // Hide all sections first
    existingSection.classList.add('hidden');
    newSection.classList.add('hidden');
    
    // Show appropriate section based on selection
    if (existingRadio.checked) {
      existingSection.classList.remove('hidden');
    } else if (newRadio.checked) {
      newSection.classList.remove('hidden');
    }
  }
  
  // Initialize form on page load
  document.addEventListener('DOMContentLoaded', function() {
    toggleChainOptions();
  });
</script>