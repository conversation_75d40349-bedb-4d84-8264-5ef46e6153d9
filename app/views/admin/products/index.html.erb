<% content_for :title, "Products" %>

<div class="flex justify-between items-center mb-6">
  <h1 class="text-2xl font-bold text-gray-900">Products</h1>
  <%= link_to "New Product", new_admin_product_path, class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
</div>

<!-- Product Analytics Dashboard (Cached Values) -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
  <!-- Total Products -->
  <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
            <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@product_metrics[:total_products]) %></dd>
            <dd class="text-xs text-gray-500">
              <%= @product_metrics[:active_products] %> active, 
              <%= @product_metrics[:inactive_products] %> inactive
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Total Sales -->
  <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Total Sales</dt>
            <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@product_metrics[:total_sales]) %></dd>
            <dd class="text-xs text-gray-500">Across all products</dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Total Orders -->
  <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
            <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@product_metrics[:total_orders]) %></dd>
            <dd class="text-xs text-gray-500">
              <%= number_with_delimiter(@product_metrics[:total_quantity_sold]) %> items sold
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Points Earned -->
  <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Points Earned</dt>
            <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@product_metrics[:total_points_earned]) %></dd>
            <dd class="text-xs text-gray-500">
              <%= @product_metrics[:products_in_promotions] %> in promotions
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Search Form -->
<div class="bg-white shadow rounded-lg p-6 mb-6">
  <%= search_form_for @q, url: admin_products_path, method: :get, class: "space-y-4" do |f| %>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <%= render "shared/form_inputs/text_field", 
          form: f, 
          field: :name_or_sku_cont, 
          label: "Name or SKU", 
          placeholder: "Search by name or SKU..." %>
      
      <%= render "shared/form_inputs/select_field", 
          form: f, 
          field: :category_id_eq, 
          label: "Category", 
          options: options_from_collection_for_select(Category.order(:name), :id, :name, @q.category_id_eq),
          prompt: "All categories" %>
      
      <%= render "shared/form_inputs/select_field", 
          form: f, 
          field: :product_type_eq, 
          label: "Type", 
          options: options_for_select([["All", ""], ["Regular", "regular"], ["Gift Card", "gift_card"]], @q.product_type_eq) %>
      
      <%= render "shared/form_inputs/select_field", 
          form: f, 
          field: :status_eq, 
          label: "Status", 
          options: options_for_select([["All", ""], ["Active", "active"], ["Inactive", "inactive"]], @q.status_eq) %>
    </div>
    
    <div class="flex items-center space-x-2">
      <%= render "shared/form_inputs/button", text: "Search", type: "submit", variant: "secondary" %>
      <%= link_to admin_products_path do %>
        <%= render "shared/form_inputs/button", text: "Clear", type: "button", variant: "outline" %>
      <% end %>
    </div>
  <% end %>
</div>

<!-- Bulk Actions -->
<%= form_with url: bulk_update_admin_products_path, method: :patch, local: true, id: "bulk-form", class: "mb-4" do |f| %>
  <div class="bg-white shadow rounded-lg p-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <span class="text-sm font-medium text-gray-700">Bulk Actions:</span>
        <%= render "shared/form_inputs/select_field", 
            form: f, 
            field: :bulk_action, 
            label: "", 
            options: options_for_select([["Select action...", ""], ["Activate", "activate"], ["Deactivate", "deactivate"], ["Delete", "delete"]]),
            wrapper_class: "mb-0" %>
        <%= render "shared/form_inputs/button", 
            text: "Apply", 
            type: "submit", 
            variant: "secondary",
            data: { confirm: "Are you sure?" } %>
      </div>
      <div class="text-sm text-gray-500">
        <span id="selected-count">0</span> products selected
      </div>
    </div>
  </div>
<% end %>

<!-- Products Table -->
<div class="bg-white shadow rounded-lg overflow-hidden">
  <table class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
      <tr>
        <th class="px-6 py-3 text-left">
          <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
        </th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Promotions</th>
        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
      <% @products.each do |product| %>
        <tr class="hover:bg-gray-50">
          <td class="px-6 py-4 whitespace-nowrap">
            <input type="checkbox" name="product_ids[]" value="<%= product.id %>" 
                   class="product-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                   form="bulk-form">
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <% if product.image.attached? %>
                <div class="flex-shrink-0 h-10 w-10">
                  <%= image_tag product.image, class: "h-10 w-10 rounded object-cover" %>
                </div>
              <% else %>
                <div class="flex-shrink-0 h-10 w-10 bg-gray-200 rounded flex items-center justify-center">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
              <% end %>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900"><%= product.name %></div>
                <div class="text-sm text-gray-500">SKU: <%= product.sku %></div>
              </div>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <%= product.category&.name || '-' %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <% if product.gift_card? %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">Gift Card</span>
            <% else %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Regular</span>
            <% end %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <%= product.points_required %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <% if product.active? %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
            <% else %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
            <% end %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            <% if product.promotion_products_count > 0 %>
              <span class="text-green-600 font-medium"><%= product.promotion_products_count %> promotions</span>
            <% else %>
              <span class="text-gray-400">None</span>
            <% end %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
            <%= link_to "View", admin_product_path(product), class: "text-blue-600 hover:text-blue-900" %>
            <%= link_to "Edit", edit_admin_product_path(product), class: "text-blue-600 hover:text-blue-900" %>
            <% if product.active? %>
              <%= link_to "Deactivate", deactivate_admin_product_path(product), 
                  method: :post, 
                  class: "text-yellow-600 hover:text-yellow-900",
                  data: { confirm: "Deactivate this product?" } %>
            <% else %>
              <%= link_to "Activate", activate_admin_product_path(product), 
                  method: :post, 
                  class: "text-green-600 hover:text-green-900",
                  data: { confirm: "Activate this product?" } %>
            <% end %>
            <%= link_to "Delete", admin_product_path(product), 
                method: :delete, 
                class: "text-red-600 hover:text-red-900",
                data: { confirm: "Are you sure? This cannot be undone." } %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
  
  <% if @products.empty? %>
    <div class="text-center py-12">
      <p class="text-gray-500">No products found.</p>
      <%= link_to "Create your first product", new_admin_product_path, class: "mt-2 inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
    </div>
  <% end %>
</div>

<!-- Pagination -->
<% if respond_to?(:pagy_nav) %>
  <div class="mt-6">
    <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
  </div>
<% end %>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const selectAllCheckbox = document.getElementById('select-all');
  const productCheckboxes = document.querySelectorAll('.product-checkbox');
  const selectedCountSpan = document.getElementById('selected-count');

  function updateSelectedCount() {
    const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;
    selectedCountSpan.textContent = checkedCount;
  }

  selectAllCheckbox.addEventListener('change', function() {
    productCheckboxes.forEach(checkbox => {
      checkbox.checked = this.checked;
    });
    updateSelectedCount();
  });

  productCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedCount);
  });
});
</script>