<% content_for :title, "Edit Product" %>

<div class="mb-6">
  <nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-4">
      <li>
        <%= link_to admin_products_path, class: "text-gray-400 hover:text-gray-500" do %>
          Products
        <% end %>
      </li>
      <li>
        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
          <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
        </svg>
      </li>
      <li>
        <%= link_to admin_product_path(@product), class: "text-gray-400 hover:text-gray-500" do %>
          <%= @product.name %>
        <% end %>
      </li>
      <li>
        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
          <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
        </svg>
      </li>
      <li>
        <span class="text-gray-500">Edit</span>
      </li>
    </ol>
  </nav>
</div>

<div class="bg-white shadow rounded-lg">
  <div class="px-6 py-4 border-b border-gray-200">
    <h1 class="text-xl font-semibold text-gray-900">Edit Product: <%= @product.name %></h1>
  </div>
  
  <div class="p-6">
    <%= render "form" %>
  </div>
</div>