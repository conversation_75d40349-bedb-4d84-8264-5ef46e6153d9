<%= form_with model: [:admin, @product], local: true, class: "space-y-6" do |f| %>
  <% if @product.errors.any? %>
    <div class="bg-red-50 border border-red-200 rounded-md p-4">
      <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
      <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
        <% @product.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <%= render "shared/form_inputs/text_field", 
        form: f, 
        field: :name, 
        label: "Product Name", 
        required: true %>

    <%= render "shared/form_inputs/text_field", 
        form: f, 
        field: :sku, 
        label: "SKU", 
        required: true,
        help_text: "Unique product identifier" %>
  </div>

  <%= render "shared/form_inputs/textarea_field", 
      form: f, 
      field: :description, 
      label: "Description", 
      rows: 3,
      placeholder: "Product description..." %>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <%= render "shared/form_inputs/select_field", 
        form: f, 
        field: :product_type, 
        label: "Product Type", 
        options: options_for_select([["Regular Product", "regular"], ["Gift Card", "gift_card"]], @product.product_type) %>

    <%= render "shared/form_inputs/select_field", 
        form: f, 
        field: :category_id, 
        label: "Category", 
        options: options_from_collection_for_select(@categories, :id, :name, @product.category_id),
        prompt: "Select a category..." %>

    <%= render "shared/form_inputs/select_field", 
        form: f, 
        field: :status, 
        label: "Status", 
        options: options_for_select([["Active", "active"], ["Inactive", "inactive"]], @product.status) %>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <%= render "shared/form_inputs/text_field", 
        form: f, 
        field: :points_required, 
        label: "Points Required", 
        type: "number",
        help_text: "Points needed to redeem this product" %>

    <div>
      <%= f.label :image, "Product Image", class: "block text-sm font-medium text-gray-900 mb-2" %>
      <%= f.file_field :image, 
          accept: "image/*",
          class: "block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" %>
      <% if @product.image.attached? %>
        <div class="mt-2">
          <%= image_tag @product.image, class: "w-20 h-20 object-cover rounded border border-gray-200" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Points by Country Configuration -->
  <div class="border border-gray-200 rounded-lg p-4">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Points by Country</h3>
    <p class="text-sm text-gray-600 mb-4">Configure how many points users earn when selling this product in different countries.</p>
    
    <div id="country-points-container">
      <%= f.fields_for :product_country_data do |country_form| %>
        <div class="country-points-row grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 border border-gray-100 rounded-lg">
          <%= render "shared/form_inputs/select_field", 
              form: country_form, 
              field: :country_id, 
              label: "Country", 
              options: options_from_collection_for_select(@countries, :id, :name, country_form.object.country_id),
              prompt: "Select a country..." %>

          <%= render "shared/form_inputs/text_field", 
              form: country_form, 
              field: :points_earned, 
              label: "Points Earned", 
              type: "number",
              help_text: "Points earned per sale" %>

          <div class="flex items-end">
            <% unless country_form.object.new_record? %>
              <%= country_form.check_box :_destroy, class: "hidden" %>
              <button type="button" class="remove-country-points bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                Remove
              </button>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    
    <button type="button" id="add-country-points" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
      Add Country Points
    </button>
  </div>

  <div class="flex justify-end space-x-3">
    <%= link_to admin_products_path do %>
      <%= render "shared/form_inputs/button", text: "Cancel", type: "button", variant: "outline" %>
    <% end %>
    <%= render "shared/form_inputs/button", 
        text: @product.persisted? ? "Update Product" : "Create Product", 
        type: "submit", 
        variant: "primary" %>
  </div>
<% end %>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const container = document.getElementById('country-points-container');
  const addButton = document.getElementById('add-country-points');
  let fieldIndex = <%= @product.product_country_data.size %>;

  // Add new country points row
  addButton.addEventListener('click', function() {
    const template = `
      <div class="country-points-row grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 border border-gray-100 rounded-lg">
        <div>
          <label class="block text-sm font-medium text-gray-900 mb-2">Country</label>
          <select name="product[product_country_data_attributes][${fieldIndex}][country_id]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">Select a country...</option>
            <% @countries.each do |country| %>
              <option value="<%= country.id %>"><%= country.name %></option>
            <% end %>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-900 mb-2">Points Earned</label>
          <input type="number" name="product[product_country_data_attributes][${fieldIndex}][points_earned]" class="flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2">
          <p class="mt-2 text-xs text-gray-500">Points earned per sale</p>
        </div>
        <div class="flex items-end">
          <button type="button" class="remove-country-points bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium">
            Remove
          </button>
        </div>
      </div>
    `;
    
    container.insertAdjacentHTML('beforeend', template);
    fieldIndex++;
  });

  // Remove country points row
  container.addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-country-points')) {
      const row = e.target.closest('.country-points-row');
      const destroyField = row.querySelector('input[name*="_destroy"]');
      
      if (destroyField) {
        // Mark for destruction if it's an existing record
        destroyField.checked = true;
        row.style.display = 'none';
      } else {
        // Remove from DOM if it's a new record
        row.remove();
      }
    }
  });
});
</script>