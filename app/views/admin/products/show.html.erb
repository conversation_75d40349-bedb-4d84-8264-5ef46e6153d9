<% content_for :title, @product.name %>

<div class="mb-6">
  <nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-4">
      <li>
        <%= link_to admin_products_path, class: "text-gray-400 hover:text-gray-500" do %>
          Products
        <% end %>
      </li>
      <li>
        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
          <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
        </svg>
      </li>
      <li>
        <span class="text-gray-500"><%= @product.name %></span>
      </li>
    </ol>
  </nav>
</div>

<div class="flex justify-between items-center mb-6">
  <h1 class="text-2xl font-bold text-gray-900"><%= @product.name %></h1>
  <div class="space-x-2">
    <%= link_to "Edit", edit_admin_product_path(@product), class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
    <% if @product.active? %>
      <%= link_to "Deactivate", deactivate_admin_product_path(@product), 
          method: :post, 
          class: "bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium",
          data: { confirm: "Deactivate this product?" } %>
    <% else %>
      <%= link_to "Activate", activate_admin_product_path(@product), 
          method: :post, 
          class: "bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium",
          data: { confirm: "Activate this product?" } %>
    <% end %>
    <%= link_to "Delete", admin_product_path(@product), 
        method: :delete, 
        class: "bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium",
        data: { confirm: "Are you sure? This cannot be undone." } %>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Main Product Details -->
  <div class="lg:col-span-2 space-y-6">
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Product Details</h2>
      
      <div class="flex items-start space-x-6">
        <% if @product.image.attached? %>
          <div class="flex-shrink-0">
            <%= image_tag @product.image, class: "w-32 h-32 object-cover rounded-lg border border-gray-200" %>
          </div>
        <% end %>
        
        <div class="flex-1">
          <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">SKU</dt>
              <dd class="mt-1 text-sm text-gray-900 font-mono"><%= @product.sku %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd class="mt-1">
                <% if @product.active? %>
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                <% else %>
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                <% end %>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Type</dt>
              <dd class="mt-1">
                <% if @product.gift_card? %>
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">Gift Card</span>
                <% else %>
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Regular</span>
                <% end %>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Category</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @product.category&.name || 'None' %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Points Required</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @product.points_required %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Created</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @product.created_at.strftime('%m/%d/%Y') %></dd>
            </div>
          </dl>
          
          <% if @product.description.present? %>
            <div class="mt-6">
              <dt class="text-sm font-medium text-gray-500">Description</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= simple_format(@product.description) %></dd>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Points by Country -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Points by Country</h2>
      
      <% if @product_country_data.any? %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Country</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Points Earned</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <% @product_country_data.each do |data| %>
                <tr>
                  <td class="px-4 py-3 text-sm text-gray-900">
                    <%= data.country.name %>
                  </td>
                  <td class="px-4 py-3 text-sm text-gray-900">
                    <%= data.points_earned %> points
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <p class="text-gray-500 text-center py-4">No country-specific points configured.</p>
      <% end %>
    </div>

    <!-- Active Promotions -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Active Promotions</h2>
      
      <% if @active_promotions.any? %>
        <div class="space-y-3">
          <% @active_promotions.each do |promotion| %>
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="text-sm font-medium text-gray-900">
                    <%= link_to promotion.name, admin_promotion_path(promotion), class: "text-blue-600 hover:text-blue-900" %>
                  </h3>
                  <p class="text-sm text-gray-500 mt-1"><%= promotion.scope_description %></p>
                  <p class="text-xs text-gray-400 mt-1">
                    <%= promotion.active_period %>
                  </p>
                </div>
                <div class="text-right">
                  <% if promotion.bonus_points.present? %>
                    <span class="text-sm font-medium text-green-600">+<%= promotion.bonus_points %> pts</span>
                  <% elsif promotion.bonus_multiplier.present? %>
                    <span class="text-sm font-medium text-green-600"><%= promotion.bonus_multiplier %>x</span>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <p class="text-gray-500 text-center py-4">No active promotions for this product.</p>
      <% end %>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="space-y-6">
    <!-- Quick Stats -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Stats</h2>
      
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Total Sales</span>
          <span class="text-sm font-medium text-gray-900">
            <%= @product_stats[:sales_count] %>
          </span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Total Orders</span>
          <span class="text-sm font-medium text-gray-900">
            <%= @product_stats[:orders_count] %>
          </span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Quantity Sold</span>
          <span class="text-sm font-medium text-gray-900">
            <%= @product_stats[:total_quantity_sold] %>
          </span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Points Earned</span>
          <span class="text-sm font-medium text-gray-900">
            <%= number_with_delimiter(@product_stats[:total_points_earned]) %>
          </span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Promotions</span>
          <span class="text-sm font-medium text-gray-900">
            <%= @product_stats[:promotion_products_count] %>
          </span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Countries Configured</span>
          <span class="text-sm font-medium text-gray-900">
            <%= @product_country_data.count %>
          </span>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Sales</h2>
      
      <% recent_sales = Sale.where(product: @product).includes(:user).order(created_at: :desc).limit(5) %>
      <% if recent_sales.any? %>
        <div class="space-y-3">
          <% recent_sales.each do |sale| %>
            <div class="text-sm">
              <div class="font-medium text-gray-900"><%= sale.points %> points</div>
              <div class="text-gray-500">
                <%= sale.user.email %>
                <br>
                <%= time_ago_in_words(sale.created_at) %> ago
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <p class="text-gray-500 text-sm">No recent sales.</p>
      <% end %>
    </div>
  </div>
</div>