<!-- Admin Dashboard -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Admin Dashboard
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Overview of system activity and pending actions
      </p>
    </div>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
    <!-- Total Users (Cached) -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <%= users_icon class: "w-5 h-5 text-white" %>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@total_users) %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Stores -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <%= stores_icon class: "w-5 h-5 text-white" %>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Stores</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@total_stores) %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Orders (Cached) -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <%= orders_icon class: "w-5 h-5 text-white" %>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@total_orders) %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
    <!-- Pending Sales -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <%= lucide_icon "clock", class: "w-6 h-6 text-yellow-600" %>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Pending Sales</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= @pending_sales_count %></dd>
            </dl>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-5 py-3 border-t border-gray-200">
        <%= link_to admin_sales_path(q: { status_eq: 'pending' }),
            class: "text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors" do %>
          View pending sales →
        <% end %>
      </div>
    </div>

    <!-- Total Users -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <%= users_icon class: "w-6 h-6 text-blue-600" %>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= @total_users %></dd>
            </dl>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-5 py-3 border-t border-gray-200">
        <%= link_to admin_users_path,
            class: "text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors" do %>
          Manage users →
        <% end %>
      </div>
    </div>

    <!-- Total Stores -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <%= stores_icon class: "w-6 h-6 text-green-600" %>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Stores</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= @total_stores %></dd>
            </dl>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-5 py-3 border-t border-gray-200">
        <%= link_to admin_stores_path,
            class: "text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors" do %>
          Manage stores →
        <% end %>
      </div>
    </div>

    <!-- Points Awarded -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <%= lucide_icon "award", class: "w-6 h-6 text-purple-600" %>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Points Awarded</dt>
              <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@total_points_awarded) %></dd>
            </dl>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-5 py-3 border-t border-gray-200">
        <span class="text-sm font-medium text-gray-500">Total approved sales points</span>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Sales -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Recent Sales</h3>
      </div>
      <div class="px-6 py-5">
        <div class="space-y-4">
          <% @recent_sales.each do |sale| %>
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100">
              <div class="flex-1">
                <p class="text-sm font-semibold text-gray-900"><%= sale.product.name %></p>
                <p class="text-xs text-gray-500 mt-1">
                  <%= sale.user.email %> • <%= sale.serial_number %>
                </p>
                <p class="text-xs text-gray-500">
                  <%= sale.created_at.strftime('%b %d, %Y at %I:%M %p') %>
                </p>
              </div>
              <div class="text-right ml-4">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                             <%= sale.status == 'approved' ? 'bg-green-100 text-green-800' :
                                 sale.status == 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                 'bg-red-100 text-red-800' %>">
                  <%= sale.status.capitalize %>
                </span>
                <p class="text-sm font-bold text-blue-600 mt-1">
                  <%= sale.points %> pts
                </p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
        <%= link_to admin_sales_path,
            class: "text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors" do %>
          View all sales →
        <% end %>
      </div>
    </div>

    <!-- Recent Admin Activity -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Recent Admin Activity</h3>
      </div>
      <div class="px-6 py-5">
        <div class="space-y-4">
          <% if @recent_activity.any? %>
            <% @recent_activity.each do |sale| %>
              <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-100">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 <%= sale.status == 'approved' ? 'bg-green-100' : 'bg-red-100' %> rounded-full flex items-center justify-center">
                    <% if sale.status == 'approved' %>
                      <%= check_icon class: "w-5 h-5 text-green-600" %>
                    <% else %>
                      <%= x_icon class: "w-5 h-5 text-red-600" %>
                    <% end %>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-gray-900">
                    <span class="font-semibold"><%= sale.approved_by_admin&.email || 'System' %></span>
                    <%= sale.status == 'approved' ? 'approved' : 'rejected' %> sale
                    <span class="font-semibold"><%= sale.serial_number %></span>
                  </p>
                  <p class="text-xs text-gray-500 mt-1">
                    <%= sale.updated_at.strftime('%b %d, %Y at %I:%M %p') %>
                  </p>
                </div>
              </div>
            <% end %>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
              <p class="mt-1 text-sm text-gray-500">Admin actions will appear here.</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>