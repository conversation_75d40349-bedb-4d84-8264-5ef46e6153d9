<% content_for :title, "Promotions" %>

<div class="flex justify-between items-center mb-6">
  <h1 class="text-2xl font-bold text-gray-900">Promotions</h1>
  <%= link_to "New Promotion", new_admin_promotion_path, class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
</div>

<!-- Promotion Analytics Dashboard (Cached Values) -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
  <!-- Total Promotions -->
  <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Total Promotions</dt>
            <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@promotion_metrics[:total_promotions]) %></dd>
            <dd class="text-xs text-gray-500">
              <%= @promotion_metrics[:active_promotions] %> active, 
              <%= @promotion_metrics[:expired_promotions] %> expired
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Sales Generated -->
  <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Sales Generated</dt>
            <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@promotion_metrics[:total_sales]) %></dd>
            <dd class="text-xs text-gray-500">From promotions</dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Bonus Points -->
  <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Bonus Points</dt>
            <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@promotion_metrics[:total_bonus_points]) %></dd>
            <dd class="text-xs text-gray-500">Total awarded</dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Products in Promotions -->
  <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Products</dt>
            <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@promotion_metrics[:total_products_in_promotions]) %></dd>
            <dd class="text-xs text-gray-500">In promotions</dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Search Form -->
<div class="bg-white shadow rounded-lg p-6 mb-6">
  <%= search_form_for @q, url: admin_promotions_path, method: :get, class: "space-y-4" do |f| %>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <%= render "shared/form_inputs/text_field",
          form: f,
          field: :name_cont,
          label: "Name",
          placeholder: "Search by name..." %>

      <%= render "shared/form_inputs/select_field",
          form: f,
          field: :status_eq,
          label: "Status",
          options: options_for_select([["All", ""], ["Active", "active"], ["Inactive", "inactive"], ["Expired", "expired"]], @q.status_eq) %>

      <div class="flex items-end space-x-2">
        <%= render "shared/form_inputs/button", text: "Search", type: "submit", variant: "secondary" %>
        <%= link_to admin_promotions_path do %>
          <%= render "shared/form_inputs/button", text: "Clear", type: "button", variant: "outline" %>
        <% end %>
      </div>
    </div>
  <% end %>
</div>

<!-- Promotions Table -->
<div class="bg-white shadow rounded-lg overflow-hidden">
  <table class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
      <tr>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scope</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bonus</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Products</th>
        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
      <% @promotions.each do |promotion| %>
        <tr class="hover:bg-gray-50">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900"><%= promotion.name %></div>
            <% if promotion.description.present? %>
              <div class="text-sm text-gray-500"><%= truncate(promotion.description, length: 50) %></div>
            <% end %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <%= promotion.scope_description %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <% if promotion.bonus_points.present? %>
              +<%= promotion.bonus_points %> points
            <% elsif promotion.bonus_multiplier.present? %>
              <%= promotion.bonus_multiplier %>x multiplier
            <% end %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <%= promotion.active_period %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <% case promotion.status %>
            <% when 'active' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
            <% when 'inactive' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Inactive</span>
            <% when 'expired' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Expired</span>
            <% end %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <%= promotion.promotion_products_count %> products
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
            <%= link_to "View", admin_promotion_path(promotion), class: "text-blue-600 hover:text-blue-900" %>
            <%= link_to "Edit", edit_admin_promotion_path(promotion), class: "text-blue-600 hover:text-blue-900" %>
            <% if promotion.active? %>
              <%= link_to "Deactivate", deactivate_admin_promotion_path(promotion), method: :post, class: "text-yellow-600 hover:text-yellow-900", data: { confirm: "Are you sure?" } %>
            <% elsif promotion.inactive? %>
              <%= link_to "Activate", activate_admin_promotion_path(promotion), method: :post, class: "text-green-600 hover:text-green-900", data: { confirm: "Are you sure?" } %>
            <% end %>
            <%= link_to "Delete", admin_promotion_path(promotion), method: :delete, class: "text-red-600 hover:text-red-900", data: { confirm: "Are you sure?" } %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>

  <% if @promotions.empty? %>
    <div class="text-center py-12">
      <p class="text-gray-500">No promotions found.</p>
      <%= link_to "Create your first promotion", new_admin_promotion_path, class: "mt-2 inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
    </div>
  <% end %>
</div>

<!-- Pagination -->
<% if respond_to?(:pagy_nav) %>
  <div class="mt-6">
    <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
  </div>
<% end %>