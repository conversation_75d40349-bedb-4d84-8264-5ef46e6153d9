<%= form_with model: [:admin, @promotion], local: true, class: "space-y-6" do |f| %>
  <% if @promotion.errors.any? %>
    <div class="bg-red-50 border border-red-200 rounded-md p-4">
      <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
      <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
        <% @promotion.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <%= render "shared/form_inputs/text_field",
        form: f,
        field: :name,
        label: "Name",
        required: true %>

    <%= render "shared/form_inputs/select_field",
        form: f,
        field: :status,
        label: "Status",
        options: options_for_select([["Active", "active"], ["Inactive", "inactive"]], @promotion.status) %>
  </div>

  <%= render "shared/form_inputs/textarea_field",
      form: f,
      field: :description,
      label: "Description",
      rows: 3,
      placeholder: "Optional description of the promotion" %>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <%= render "shared/form_inputs/text_field",
        form: f,
        field: :start_date,
        label: "Start Date",
        type: "datetime-local",
        required: true %>

    <%= render "shared/form_inputs/text_field",
        form: f,
        field: :end_date,
        label: "End Date",
        type: "datetime-local",
        required: true %>
  </div>

  <!-- Bonus Configuration -->
  <div class="border border-gray-200 rounded-lg p-4">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Bonus Configuration</h3>
    <p class="text-sm text-gray-600 mb-4">Choose either fixed bonus points OR a multiplier (not both).</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <%= f.label :bonus_points, "Fixed Bonus Points", class: "block text-sm font-medium text-gray-900 mb-2" %>
        <%= f.number_field :bonus_points,
            min: 0,
            placeholder: "e.g., 50",
            class: "flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" %>
        <p class="mt-2 text-xs text-gray-500">Add this many points to each sale</p>
      </div>

      <div>
        <%= f.label :bonus_multiplier, "Points Multiplier", class: "block text-sm font-medium text-gray-900 mb-2" %>
        <%= f.number_field :bonus_multiplier,
            step: 0.01,
            min: 0.01,
            placeholder: "e.g., 1.5",
            class: "flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" %>
        <p class="mt-2 text-xs text-gray-500">Multiply base points by this amount (e.g., 1.5 for 50% bonus)</p>
      </div>
    </div>
  </div>

  <!-- Scope Configuration -->
  <div class="border border-gray-200 rounded-lg p-4">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Promotion Scope</h3>
    <p class="text-sm text-gray-600 mb-4">Select ONE scope where this promotion applies.</p>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <%= render "shared/form_inputs/select_field",
          form: f,
          field: :store_id,
          label: "Specific Store",
          options: options_from_collection_for_select(@stores, :id, :name, @promotion.store_id),
          prompt: "Select a store..." %>

      <%= render "shared/form_inputs/select_field",
          form: f,
          field: :region_id,
          label: "Entire Region",
          options: options_from_collection_for_select(@regions, :id, :name, @promotion.region_id),
          prompt: "Select a region..." %>

      <%= render "shared/form_inputs/select_field",
          form: f,
          field: :store_chain_id,
          label: "Store Chain",
          options: options_from_collection_for_select(@store_chains, :id, :name, @promotion.store_chain_id),
          prompt: "Select a chain..." %>
    </div>
  </div>

  <!-- Products Selection -->
  <div class="border border-gray-200 rounded-lg p-4"
       data-controller="product-selector"
       data-product-selector-url-value="<%= admin_promotion_products_path %>"
       data-product-selector-selected-ids-value="<%= @promotion.product_ids.to_json %>">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Eligible Products</h3>
    <p class="text-sm text-gray-600 mb-4">Select which products are eligible for this promotion.</p>

    <!-- Product Search -->
    <div class="mb-4">
      <input type="text"
             placeholder="Search by name, SKU, or category..."
             data-product-selector-target="searchInput"
             class="flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2">
    </div>

    <!-- Selected Products Summary -->
    <div class="mb-4 p-3 bg-blue-50 rounded-md">
      <div class="flex items-center justify-between">
        <span class="text-sm font-medium text-blue-900">
          <span data-product-selector-target="selectedCount"><%= @promotion.product_ids.length %></span> products selected
        </span>
        <button type="button"
                data-action="click->product-selector#clearAll"
          class="text-sm text-blue-600 hover:text-blue-800">
          Clear all
        </button>
      </div>
      <div data-product-selector-target="selectedProductsList" class="mt-2 flex flex-wrap gap-1">
        <% @promotion.products.each do |product| %>
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800" data-product-id="<%= product.id %>">
            <%= product.name %>
            <button type="button"
                    class="ml-1 text-blue-600 hover:text-blue-800"
                    data-action="click->product-selector#removeProduct"
              data-product-id="<%= product.id %>">x</button>
          </span>
        <% end %>
      </div>
    </div>

    <!-- Products List -->
    <div class="border border-gray-200 rounded-md">
      <div class="h-96 overflow-y-auto">
        <div data-product-selector-target="productsList">
          <!-- Products will be loaded here via JavaScript -->
        </div>
        <div data-product-selector-target="loadingIndicator" class="p-6 text-center text-gray-500 hidden">
          <div class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading products...
          </div>
        </div>
        <div data-product-selector-target="emptyIndicator" class="p-8 text-center text-gray-500 hidden">
          <div class="text-gray-400 mb-2">
            <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
            </svg>
          </div>
          <p class="text-sm">No products found matching your search.</p>
          <p class="text-xs text-gray-400 mt-1">Try adjusting your search terms.</p>
        </div>
      </div>

      <!-- Load More Button -->
      <div class="border-t border-gray-200 p-4 text-center bg-gray-50">
        <button type="button"
                data-product-selector-target="loadMoreButton"
                data-action="click->product-selector#loadMore"
          class="items-center px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors hidden">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
          Load more products
        </button>
      </div>
    </div>

    <!-- Hidden inputs for selected products -->
    <div data-product-selector-target="selectedProductInputs">
      <% @promotion.product_ids.each do |product_id| %>
        <input type="hidden" name="promotion[product_ids][]" value="<%= product_id %>" data-product-id="<%= product_id %>">
      <% end %>
    </div>
  </div>

  <div class="flex justify-end space-x-3">
    <%= link_to admin_promotions_path do %>
      <%= render "shared/form_inputs/button", text: "Cancel", type: "button", variant: "outline" %>
    <% end %>
    <%= render "shared/form_inputs/button",
        text: @promotion.persisted? ? "Update Promotion" : "Create Promotion",
        type: "submit",
        variant: "primary" %>
  </div>
<% end %>