<% content_for :title, @promotion.name %>

<div class="mb-6">
  <nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-4">
      <li>
        <%= link_to admin_promotions_path, class: "text-gray-400 hover:text-gray-500" do %>
          Promotions
        <% end %>
      </li>
      <li>
        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
          <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
        </svg>
      </li>
      <li>
        <span class="text-gray-500"><%= @promotion.name %></span>
      </li>
    </ol>
  </nav>
</div>

<div class="flex justify-between items-center mb-6">
  <h1 class="text-2xl font-bold text-gray-900"><%= @promotion.name %></h1>
  <div class="space-x-2">
    <%= link_to "Edit", edit_admin_promotion_path(@promotion), class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
    <% if @promotion.active? %>
      <%= link_to "Deactivate", deactivate_admin_promotion_path(@promotion), method: :post, class: "bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium", data: { confirm: "Are you sure?" } %>
    <% elsif @promotion.inactive? %>
      <%= link_to "Activate", activate_admin_promotion_path(@promotion), method: :post, class: "bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium", data: { confirm: "Are you sure?" } %>
    <% end %>
    <%= link_to "Delete", admin_promotion_path(@promotion), method: :delete, class: "bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium", data: { confirm: "Are you sure you want to delete this promotion?" } %>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Main Details -->
  <div class="lg:col-span-2 space-y-6">
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Promotion Details</h2>

      <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <dt class="text-sm font-medium text-gray-500">Status</dt>
          <dd class="mt-1">
            <% case @promotion.status %>
            <% when 'active' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
            <% when 'inactive' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Inactive</span>
            <% when 'expired' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Expired</span>
            <% end %>
          </dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">Scope</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @promotion.scope_description %></dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">Start Date</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @promotion.start_date.strftime('%B %d, %Y at %I:%M %p') %></dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">End Date</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @promotion.end_date.strftime('%B %d, %Y at %I:%M %p') %></dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">Bonus</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <% if @promotion.bonus_points.present? %>
              +<%= @promotion.bonus_points %> points
            <% elsif @promotion.bonus_multiplier.present? %>
              <%= @promotion.bonus_multiplier %>x multiplier
            <% end %>
          </dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">Products</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @promotion.products.count %> products</dd>
        </div>
      </dl>

      <% if @promotion.description.present? %>
        <div class="mt-6">
          <dt class="text-sm font-medium text-gray-500">Description</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= simple_format(@promotion.description) %></dd>
        </div>
      <% end %>
    </div>

    <!-- Eligible Products -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Eligible Products (<%= @promotion.products.count %>)</h2>

      <% if @promotion.products.any? %>
        <div class="space-y-3">
          <% @promotion.products.includes(:category).each do |product| %>
            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
              <div>
                <div class="text-sm font-medium text-gray-900"><%= product.name %></div>
                <div class="text-sm text-gray-500">
                  SKU: <%= product.sku %>
                  <% if product.category %>
                    • Category: <%= product.category.name %>
                  <% end %>
                </div>
              </div>
              <div class="text-sm text-gray-500">
                <%= product.points_required %> pts
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <p class="text-gray-500 text-center py-4">No products selected for this promotion.</p>
      <% end %>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="space-y-6">
    <!-- Quick Stats -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Stats</h2>

      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Total Sales</span>
          <span class="text-sm font-medium text-gray-900">
            <%= @promotion.sale_promotion_bonuses.count %>
          </span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Bonus Points Awarded</span>
          <span class="text-sm font-medium text-gray-900">
            <%= @promotion.sale_promotion_bonuses.sum(:bonus_points) %>
          </span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Days Remaining</span>
          <span class="text-sm font-medium text-gray-900">
            <% days_remaining = (@promotion.end_date.to_date - Date.current).to_i %>
            <% if days_remaining > 0 %>
              <%= days_remaining %> days
            <% elsif days_remaining == 0 %>
              Ends today
            <% else %>
              Expired
            <% end %>
          </span>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Bonus Awards</h2>

      <% recent_bonuses = @promotion.sale_promotion_bonuses.includes(sale: :user).order(created_at: :desc).limit(5) %>
      <% if recent_bonuses.any? %>
        <div class="space-y-3">
          <% recent_bonuses.each do |bonus| %>
            <div class="text-sm">
              <div class="font-medium text-gray-900">+<%= bonus.bonus_points %> points</div>
              <div class="text-gray-500">
                <%= bonus.sale.user.email %>
                <br>
                <%= time_ago_in_words(bonus.created_at) %> ago
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <p class="text-gray-500 text-sm">No bonus points awarded yet.</p>
      <% end %>
    </div>
  </div>
</div>