<% content_for :title, "Gift Card Batch - #{@batch.filename}" %>

<div class="mb-6">
  <nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-4">
      <li>
        <%= link_to admin_gift_card_batches_path, class: "text-gray-400 hover:text-gray-500" do %>
          Gift Card Batches
        <% end %>
      </li>
      <li>
        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
          <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
        </svg>
      </li>
      <li>
        <span class="text-gray-500"><%= @batch.filename %></span>
      </li>
    </ol>
  </nav>
</div>

<div class="flex justify-between items-center mb-6">
  <h1 class="text-2xl font-bold text-gray-900"><%= @batch.filename %></h1>
  <div class="space-x-2">
    <% if @batch.csv_file.attached? %>
      <%= link_to "Download CSV", download_admin_gift_card_batch_path(@batch),
          class: "bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
    <% end %>
    <% if @batch.pending? %>
      <%= link_to "Process Batch", process_batch_admin_gift_card_batch_path(@batch),
          method: :post,
          class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",
          data: { confirm: "Process this batch and generate CSV?" } %>
    <% elsif @batch.failed? %>
      <%= link_to "Retry Upload", retry_upload_admin_gift_card_batch_path(@batch),
          method: :post,
          class: "bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium",
          data: { confirm: "Retry SFTP upload for this batch?" } %>
    <% end %>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Batch Details -->
  <div class="lg:col-span-2 space-y-6">
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Batch Details</h2>

      <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <dt class="text-sm font-medium text-gray-500">Batch Date</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @batch.batch_date.strftime('%B %d, %Y') %></dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">Status</dt>
          <dd class="mt-1">
            <% case @batch.status %>
            <% when 'pending' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
            <% when 'generated' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Generated</span>
            <% when 'uploaded' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Uploaded</span>
            <% when 'failed' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Failed</span>
            <% end %>
          </dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">Total Orders</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @batch.total_orders %></dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
          <dd class="mt-1 text-sm text-gray-900">$<%= number_with_precision(@batch.total_amount, precision: 2) %></dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">Created</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @batch.created_at.strftime('%m/%d/%Y at %I:%M %p') %></dd>
        </div>

        <div>
          <dt class="text-sm font-medium text-gray-500">Uploaded</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= @batch.uploaded_at&.strftime('%m/%d/%Y at %I:%M %p') || 'Not uploaded' %>
          </dd>
        </div>
      </dl>

      <% if @batch.upload_error.present? %>
        <div class="mt-6">
          <dt class="text-sm font-medium text-gray-500">Upload Error</dt>
          <dd class="mt-1 text-sm text-red-600 bg-red-50 p-3 rounded-md">
            <%= simple_format(@batch.upload_error) %>
          </dd>
        </div>
      <% end %>
    </div>

    <!-- Gift Card Orders -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Gift Card Orders (<%= @gift_card_orders.count %>)</h2>

      <% if @gift_card_orders.any? %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Order</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Recipient</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <% @gift_card_orders.each do |gift_order| %>
                <tr>
                  <td class="px-4 py-3 text-sm text-gray-900">
                    <%= link_to "##{gift_order.order.id}", admin_order_path(gift_order.order), class: "text-blue-600 hover:text-blue-900" %>
                  </td>
                  <td class="px-4 py-3 text-sm text-gray-900">
                    <%= gift_order.recipient_first_name %> <%= gift_order.recipient_last_name %>
                    <div class="text-xs text-gray-500"><%= gift_order.recipient_email %></div>
                  </td>
                  <td class="px-4 py-3 text-sm text-gray-900">
                    $<%= number_with_precision(gift_order.amount, precision: 2) %>
                  </td>
                  <td class="px-4 py-3 text-sm">
                    <% case gift_order.status %>
                    <% when 'pending' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                    <% when 'batched' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Batched</span>
                    <% when 'uploaded' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Uploaded</span>
                    <% when 'failed' %>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Failed</span>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <p class="text-gray-500 text-center py-4">No gift card orders in this batch.</p>
      <% end %>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="space-y-6">
    <!-- Quick Stats -->
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Stats</h2>

      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Average Order Value</span>
          <span class="text-sm font-medium text-gray-900">
            <% if @batch.total_orders > 0 %>
              $<%= number_with_precision(@batch.total_amount / @batch.total_orders, precision: 2) %>
            <% else %>
              $0.00
            <% end %>
          </span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500">File Size</span>
          <span class="text-sm font-medium text-gray-900">
            <% if @batch.csv_file.attached? %>
              <%= number_to_human_size(@batch.csv_file.byte_size) %>
            <% else %>
              Not generated
            <% end %>
          </span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Processing Time</span>
          <span class="text-sm font-medium text-gray-900">
            <% if @batch.uploaded_at && @batch.created_at %>
              <%= time_ago_in_words(@batch.created_at, @batch.uploaded_at) %>
            <% else %>
              Pending
            <% end %>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>