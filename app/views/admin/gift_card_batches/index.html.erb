<% content_for :title, "Gift Card Batches" %>

<div class="flex justify-between items-center mb-6">
  <h1 class="text-2xl font-bold text-gray-900">Gift Card Batches</h1>
  <div class="space-x-2">
    <%= link_to "Process Today's Orders", process_today_admin_gift_card_batches_path,
        method: :post,
        class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",
        data: { confirm: "Process all unbatched gift card orders for today?" } %>
    <%= link_to "Test SFTP", test_sftp_admin_gift_card_batches_path,
        method: :post,
        class: "bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
  </div>
</div>

<!-- Search Form -->
<div class="bg-white shadow rounded-lg p-6 mb-6">
  <%= search_form_for @q, url: admin_gift_card_batches_path, method: :get, class: "space-y-4" do |f| %>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <%= render "shared/form_inputs/text_field",
          form: f,
          field: :filename_cont,
          label: "Filename",
          placeholder: "Search by filename..." %>

      <%= render "shared/form_inputs/select_field",
          form: f,
          field: :status_eq,
          label: "Status",
          options: options_for_select([["All", ""], ["Pending", "pending"], ["Generated", "generated"], ["Uploaded", "uploaded"], ["Failed", "failed"]], @q.status_eq) %>

      <div class="flex items-end space-x-2">
        <%= render "shared/form_inputs/button", text: "Search", type: "submit", variant: "secondary" %>
        <%= link_to admin_gift_card_batches_path do %>
          <%= render "shared/form_inputs/button", text: "Clear", type: "button", variant: "outline" %>
        <% end %>
      </div>
    </div>
  <% end %>
</div>

<!-- Batches Table -->
<div class="bg-white shadow rounded-lg overflow-hidden">
  <table class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
      <tr>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batch Date</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Filename</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Uploaded</th>
        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
      <% @batches.each do |batch| %>
        <tr class="hover:bg-gray-50">
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <%= batch.batch_date.strftime('%m/%d/%Y') %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
            <%= batch.filename %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <%= batch.total_orders %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            $<%= number_with_precision(batch.total_amount, precision: 2) %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <% case batch.status %>
            <% when 'pending' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
            <% when 'generated' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Generated</span>
            <% when 'uploaded' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Uploaded</span>
            <% when 'failed' %>
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Failed</span>
            <% end %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            <%= batch.uploaded_at&.strftime('%m/%d/%Y %I:%M %p') || '-' %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
            <%= link_to "View", admin_gift_card_batch_path(batch), class: "text-blue-600 hover:text-blue-900" %>
            <% if batch.csv_file.attached? %>
              <%= link_to "Download", download_admin_gift_card_batch_path(batch), class: "text-green-600 hover:text-green-900" %>
            <% end %>
            <% if batch.pending? %>
              <%= link_to "Process", process_batch_admin_gift_card_batch_path(batch),
                  method: :post,
                  class: "text-blue-600 hover:text-blue-900",
                  data: { confirm: "Process this batch?" } %>
            <% elsif batch.failed? %>
              <%= link_to "Retry", retry_upload_admin_gift_card_batch_path(batch),
                  method: :post,
                  class: "text-yellow-600 hover:text-yellow-900",
                  data: { confirm: "Retry upload for this batch?" } %>
            <% end %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>

  <% if @batches.empty? %>
    <div class="text-center py-12">
      <p class="text-gray-500">No gift card batches found.</p>
    </div>
  <% end %>
</div>

<!-- Pagination -->
<% if respond_to?(:pagy_nav) %>
  <div class="mt-6">
    <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
  </div>
<% end %>