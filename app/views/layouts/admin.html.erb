<!DOCTYPE html>
<html>
  <head>
    <title>Zeiss Points Admin</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>

    <!-- Ensure Lucide icons are initialized on admin pages -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        if (window.lucide && window.lucide.createIcons) {
          window.lucide.createIcons()
        }
      })
    </script>
  </head>

  <body class="bg-gray-100">
    <!-- Admin Top Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <%= image_tag 'zeisslogo.png', alt: 'Zeiss Logo', class: 'h-8 w-auto mr-3' %>
            <div>
              <h1 class="text-xl font-bold text-gray-900">Zeiss Points Admin</h1>
              <p class="text-xs text-gray-500">Administrative Dashboard</p>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <!-- Admin User Info -->
            <div class="flex items-center space-x-2">
              <div class="bg-red-100 px-3 py-1 rounded-full">
                <span class="text-sm font-semibold text-red-700">
                  <%= current_user.role.humanize %>
                </span>
              </div>
              <span class="text-sm text-gray-700"><%= current_user.email %></span>
            </div>

            <!-- Back to App -->
            <%= link_to root_path, class: "inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Back to App
            <% end %>

            <!-- Sign Out -->
            <%= link_to destroy_user_session_path, method: :delete,
                class: "inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700" do %>
              Sign Out
            <% end %>
          </div>
        </div>
      </div>
    </nav>

    <div class="flex">
      <!-- Admin Sidebar -->
      <nav class="w-64 bg-white shadow-sm min-h-screen">
        <div class="p-4">
          <ul class="space-y-2">
            <!-- Dashboard -->
            <li>
              <%= link_to admin_root_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path == admin_root_path ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3"></i>
                Dashboard
              <% end %>
            </li>

            <!-- Sales Management -->
            <li>
              <%= link_to admin_sales_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/sales') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <i data-lucide="clipboard-list" class="w-5 h-5 mr-3"></i>
                Sales
                <% if (pending_count = Sale.pending.count) > 0 %>
                  <span class="ml-auto bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                    <%= pending_count %>
                  </span>
                <% end %>
              <% end %>
            </li>

            <!-- Users Management -->
            <li>
              <%= link_to admin_users_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/users') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                Users
              <% end %>
            </li>

            <!-- Orders Management -->
            <li>
              <%= link_to admin_orders_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/orders') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                Orders
                <% if (pending_orders_count = Order.where(status: 'pending').count) > 0 %>
                  <span class="ml-auto bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                    <%= pending_orders_count %>
                  </span>
                <% end %>
              <% end %>
            </li>

            <!-- Stores Management -->
            <li>
              <%= link_to admin_stores_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/stores') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <i data-lucide="store" class="w-5 h-5 mr-3"></i>
                Stores
                <% if (requested_count = Store.requested.count) > 0 %>
                  <span class="ml-auto bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                    <%= requested_count %>
                  </span>
                <% end %>
              <% end %>
            </li>

            <!-- Promotions Management -->
            <li>
              <%= link_to admin_promotions_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/promotions') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <i data-lucide="percent" class="w-5 h-5 mr-3"></i>
                Promotions
                <% if (active_promotions_count = Promotion.current.active.count) > 0 %>
                  <span class="ml-auto bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                    <%= active_promotions_count %>
                  </span>
                <% end %>
              <% end %>
            </li>

            <!-- Products Management -->
            <li>
              <%= link_to admin_products_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/products') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <i data-lucide="package" class="w-5 h-5 mr-3"></i>
                Products
                <% if (inactive_products = Product.where(status: 'inactive').count) > 0 %>
                  <span class="ml-auto bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                    <%= inactive_products %>
                  </span>
                <% end %>
              <% end %>
            </li>

            <!-- Gift Card Batches -->
            <li>
              <%= link_to admin_gift_card_batches_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/gift_card_batches') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <i data-lucide="gift" class="w-5 h-5 mr-3"></i>
                Gift Card Batches
                <% if (pending_batches = GiftCardBatch.pending.count) > 0 %>
                  <span class="ml-auto bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                    <%= pending_batches %>
                  </span>
                <% end %>
              <% end %>
            </li>

            <!-- Support Tickets -->
            <li>
              <%= link_to admin_support_tickets_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/support_tickets') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <i data-lucide="message-circle-question" class="w-5 h-5 mr-3"></i>
                Support Tickets
                <% if (open_tickets = SupportTicket.open.admin_handled.count) > 0 %>
                  <span class="ml-auto bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded-full">
                    <%= open_tickets %>
                  </span>
                <% end %>
              <% end %>
            </li>
          </ul>
        </div>
      </nav>

      <!-- Main Content -->
      <main class="flex-1 p-6">
        <!-- Flash Messages -->
        <% if notice %>
          <div class="mb-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
            <%= notice %>
          </div>
        <% end %>

        <% if alert %>
          <div class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <%= alert %>
          </div>
        <% end %>

        <%= yield %>
      </main>
    </div>
  </body>
</html>