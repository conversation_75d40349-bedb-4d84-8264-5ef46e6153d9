<mjml>
  <mj-head>
    <mj-title>New Order #<%= @order.id %> Pending Approval</mj-title>
    <mj-preview>A new order has been placed by <%= @user.email %> and requires your approval</mj-preview>
    <mj-attributes>
      <mj-all font-family="Helvetica, Arial, sans-serif" />
      <mj-text font-size="14px" color="#374151" line-height="1.6" />
      <mj-section padding="0" />
    </mj-attributes>
  </mj-head>

  <mj-body background-color="#f3f4f6">
    <!-- Header -->
    <mj-section background-color="#1e40af" padding="30px 20px">
      <mj-column>
        <mj-text align="center" color="#ffffff" font-size="28px" font-weight="bold"> 🛒 New Order Pending Approval </mj-text>
        <mj-text align="center" color="#dbeafe" font-size="16px"> Zeiss Points Admin Notification </mj-text>
      </mj-column>
    </mj-section>

    <!-- Main Content -->
    <mj-section background-color="#ffffff" padding="40px 30px">
      <mj-column>
        <mj-text font-size="16px" color="#374151"> Hello <strong><%= @admin.email.split('@').first.capitalize %></strong>, </mj-text>

        <mj-text font-size="16px" color="#374151" padding-top="20px"> A new order has been placed and requires your approval: </mj-text>
      </mj-column>
    </mj-section>

    <!-- Order Summary Card -->
    <mj-section background-color="#ffffff" padding="0 30px 20px">
      <mj-column background-color="#f8fafc" border="1px solid #e5e7eb" border-radius="8px" padding="25px">
        <mj-text font-size="18px" font-weight="bold" color="#1f2937" align="center" padding-bottom="20px"> 📋 Order #<%= @order.id %> Details </mj-text>

        <mj-text padding="4px 0"> <strong>Customer:</strong> <%= @user.email %> </mj-text>

        <mj-text padding="4px 0"> <strong>Total Points:</strong> <%= @order.total_points %> </mj-text>

        <mj-text padding="4px 0"> <strong>Shipping Type:</strong> <%= @order.shipping_type.humanize %> </mj-text>

        <% if @order.shipping_address.present? %>
        <mj-text padding="4px 0"> <strong>Shipping Address:</strong> <%= @order.shipping_address %> </mj-text>
        <% end %>

        <mj-text padding="4px 0"> <strong>Order Date:</strong> <%= @order.created_at.strftime("%B %d, %Y at %I:%M %p") %> </mj-text>

        <mj-text padding="4px 0"> <strong>Customer Points Balance:</strong> <%= @user.wallet.points %> </mj-text>
      </mj-column>
    </mj-section>

    <!-- Order Items -->
    <mj-section background-color="#ffffff" padding="0 30px 20px">
      <mj-column background-color="#f0f9ff" border="1px solid #0ea5e9" border-radius="8px" padding="25px">
        <mj-text font-size="18px" font-weight="bold" color="#0c4a6e" align="center" padding-bottom="20px"> 📦 Order Items </mj-text>

        <% @line_items.each do |item| %>
        <mj-text padding="8px 0" color="#0c4a6e">
          <strong><%= item.product.name %></strong><br/>
          Quantity: <%= item.quantity %> × <%= item.product.points_required %> points = <%= item.total_points %> points
        </mj-text>
        <% end %>

        <mj-divider border-color="#0ea5e9" border-width="1px" padding="10px 0" />
        <mj-text font-size="16px" font-weight="bold" color="#0c4a6e" align="center"> Total: <%= @order.total_points %> points </mj-text>
      </mj-column>
    </mj-section>

    <!-- Action Button -->
    <mj-section background-color="#ffffff" padding="20px 30px 40px">
      <mj-column>
        <mj-button background-color="#1e40af" color="#ffffff" font-size="16px" font-weight="bold" border-radius="6px" padding="15px 30px" href="<%= admin_order_url(@order) %>"> Review Order → </mj-button>

        <mj-text align="center" font-size="14px" color="#6b7280" padding-top="15px"> Please review this order and approve or reject it as appropriate. </mj-text>
      </mj-column>
    </mj-section>
  </mj-body>
</mjml>