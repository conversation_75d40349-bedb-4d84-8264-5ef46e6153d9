<div data-controller="push-notifications" 
     data-push-notifications-vapid-public-key-value="<%= Rails.application.credentials.dig(:web_push, :vapid_public_key) %>"
     data-push-notifications-subscribe-url-value="<%= push_subscriptions_path %>"
     data-push-notifications-unsubscribe-url-value="<%= push_subscriptions_path %>"
     class="bg-white rounded-lg shadow p-6 mb-6">
  
  <h3 class="text-lg font-semibold text-gray-900 mb-4">Push Notifications</h3>
  
  <div data-push-notifications-target="status" class="mb-4"></div>
  
  <p class="text-gray-600 mb-4">
    Get notified about new promotions, order updates, and important announcements.
  </p>
  
  <div class="flex gap-3">
    <button data-push-notifications-target="subscribeButton"
            data-action="click->push-notifications#subscribe"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
      Enable Notifications
    </button>
    
    <button data-push-notifications-target="unsubscribeButton"
            data-action="click->push-notifications#unsubscribe"
            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
            style="display: none;">
      Disable Notifications
    </button>
  </div>
  
  <div class="mt-4 text-sm text-gray-500">
    <p>💡 <strong>Tip:</strong> You can manage notifications for each of your devices separately.</p>
  </div>
</div>