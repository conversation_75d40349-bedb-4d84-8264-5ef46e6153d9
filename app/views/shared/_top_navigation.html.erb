<!-- Top Navigation Bar -->
<nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
  <div class="px-4 py-3 sm:px-6 lg:px-8">
    <div class="flex items-center justify-between">
      <!-- Left Side: Back Button or Logo -->
      <div class="flex items-center space-x-3">
        <% if local_assigns[:show_back_button] %>
          <%= link_to (local_assigns[:back_path] || root_path),
              class: "p-2 -ml-2 rounded-full hover:bg-gray-100 transition-colors" do %>
            <i data-lucide="arrow-left" class="w-5 h-5 text-gray-600"></i>
          <% end %>
        <% else %>
          <%= image_tag 'zeisslogo.png', alt: 'Zeiss Logo', class: 'h-8 w-auto' %>
        <% end %>

        <div>
          <h1 class="text-lg font-bold text-gray-900">
            <%= local_assigns[:title] || 'ZeissPoints' %>
          </h1>
          <% if local_assigns[:subtitle] %>
            <p class="text-xs text-gray-500"><%= local_assigns[:subtitle] %></p>
          <% end %>
        </div>
      </div>

      <!-- Right Side: Points and Profile -->
      <div class="flex items-center space-x-2">
        <!-- Cart Icon with Badge (Desktop Only) -->
        <% if user_signed_in? %>
          <%= link_to cart_path(current_cart),
              class: "relative p-2 rounded-full hover:bg-gray-100 transition-colors hidden md:flex",
              title: "View Cart" do %>
            <i data-lucide="shopping-cart" class="w-5 h-5 text-gray-600"></i>
            <% if current_cart.line_items.any? %>
              <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                <%= current_cart.line_items.sum(:quantity) %>
              </span>
            <% end %>
          <% end %>
        <% end %>

        <!-- Points Display -->
        <div class="bg-zeiss-50 px-2 py-1 sm:px-3 rounded-full">
          <span class="text-xs sm:text-sm font-semibold text-zeiss-700">
            <%= current_user&.wallet&.points || 0 %> pts
          </span>
        </div>

        <!-- Admin Access (if admin) -->
        <% if current_user&.admin? || current_user&.super_admin? %>
          <%= link_to admin_root_path,
              class: "p-2 rounded-full bg-red-100 hover:bg-red-200 transition-colors hidden sm:flex",
              title: "Admin Dashboard" do %>
            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          <% end %>
        <% end %>

        <!-- Profile Menu -->
        <div class="relative">
          <%= link_to profile_path,
              class: "inline-flex p-1.5 sm:p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",
              title: "My Profile" do %>
            <i data-lucide="user" class="w-4 h-4 sm:w-5 sm:h-5 text-gray-600"></i>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</nav>