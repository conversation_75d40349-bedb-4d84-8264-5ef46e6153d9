<%# 
  Shadcn-style radio button group
  
  Parameters:
  - form: form builder object (optional, for form_with usage)
  - field: field name (symbol, required if using form)
  - name: name attribute (required if not using form)
  - options: array of hashes with keys: value, label, description (optional)
  - label: group label text (optional)
  - required: whether field is required (optional, default: false)
  - selected: currently selected value (optional)
  - onchange: onchange handler (optional)
  - wrapper_class: additional classes for wrapper (optional)
  
  Example options:
  [
    { value: 'option1', label: 'Option 1', description: 'Description for option 1' },
    { value: 'option2', label: 'Option 2', description: 'Description for option 2' }
  ]
%>

<%
  label_text = local_assigns[:label]
  required = local_assigns[:required] || false
  options = local_assigns[:options] || []
  selected_value = local_assigns[:selected]
  onchange_handler = local_assigns[:onchange]
  wrapper_class = local_assigns[:wrapper_class] || ""
  
  # Determine if we're using form builder or standalone
  using_form = local_assigns[:form].present? && local_assigns[:field].present?
  field_name = using_form ? field : local_assigns[:name]
  
  # Get current value from form object for better retention (when using form builder)
  if using_form && form.object.respond_to?(field)
    current_value = form.object.send(field)
    selected_value = current_value if selected_value.nil?
  end
  
  # Check for validation errors (when using form builder)
  has_errors = using_form && form.object&.errors&.key?(field)
  error_messages = using_form ? (form.object&.errors&.full_messages_for(field) || []) : []
%>

<div class="<%= wrapper_class %>">
  <% if label_text %>
    <label class="block text-sm font-medium text-gray-900 mb-3">
      <%= label_text %>
      <% if required %>
        <span class="text-red-500 ml-1">*</span>
      <% end %>
    </label>
  <% end %>
  
  <div class="space-y-3">
    <% options.each do |option| %>
      <%
        # Dynamic CSS classes for radio option based on validation state
        option_classes = [
          "flex items-center space-x-3 cursor-pointer p-3 rounded-lg border transition-colors",
          has_errors ? "border-red-300 hover:border-red-400" : "border-gray-200 hover:border-gray-300"
        ].join(" ")
      %>
      
      <label class="<%= option_classes %>">
        <% if using_form %>
          <%= form.radio_button field, option[:value],
              class: "aspect-square h-4 w-4 rounded-full border border-gray-300 text-blue-600 shadow focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
              onchange: onchange_handler,
              checked: (selected_value.to_s == option[:value].to_s) %>
        <% else %>
          <%= radio_button_tag field_name, option[:value], (selected_value.to_s == option[:value].to_s),
              class: "aspect-square h-4 w-4 rounded-full border border-gray-300 text-blue-600 shadow focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
              onchange: onchange_handler %>
        <% end %>
        
        <div class="flex-1">
          <span class="text-sm font-medium text-gray-900"><%= option[:label] %></span>
          <% if option[:description] %>
            <p class="text-xs text-gray-500 mt-1"><%= option[:description] %></p>
          <% end %>
        </div>
      </label>
    <% end %>
  </div>
  
  <% if has_errors %>
    <div class="mt-2">
      <% error_messages.each do |error| %>
        <p class="text-xs text-red-600"><%= error %></p>
      <% end %>
    </div>
  <% end %>
</div>