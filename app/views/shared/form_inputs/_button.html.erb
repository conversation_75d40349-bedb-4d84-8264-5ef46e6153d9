<%# 
  Shadcn-style button
  
  Parameters:
  - text: button text
  - variant: button variant (primary, secondary, destructive, outline, ghost)
  - size: button size (default, sm, lg, icon)
  - type: button type (submit, button, reset) - default: submit
  - disabled: whether button is disabled (optional, default: false)
  - class: additional CSS classes (optional)
  - data: data attributes hash (optional)
  - onclick: onclick handler (optional)
%>

<%
  text = local_assigns[:text] || "Submit"
  variant = local_assigns[:variant] || "primary"
  size = local_assigns[:size] || "default"
  button_type = local_assigns[:type] || "submit"
  disabled = local_assigns[:disabled] || false
  additional_class = local_assigns[:class] || ""
  data_attrs = local_assigns[:data] || {}
  onclick = local_assigns[:onclick]

  # Base classes
  base_classes = "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
  
  # Variant classes
  variant_classes = case variant
  when "primary"
    "bg-blue-600 text-white hover:bg-blue-700 focus-visible:ring-blue-500"
  when "secondary"
    "bg-gray-100 text-gray-900 hover:bg-gray-200 focus-visible:ring-gray-500"
  when "destructive"
    "bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500"
  when "outline"
    "border border-gray-200 bg-white hover:bg-gray-100 hover:text-gray-900 focus-visible:ring-gray-500"
  when "ghost"
    "hover:bg-gray-100 hover:text-gray-900 focus-visible:ring-gray-500"
  else
    "bg-blue-600 text-white hover:bg-blue-700 focus-visible:ring-blue-500"
  end
  
  # Size classes
  size_classes = case size
  when "sm"
    "h-9 px-3"
  when "lg"
    "h-11 px-8"
  when "icon"
    "h-10 w-10"
  else
    "h-10 px-4 py-2"
  end
  
  # Combine all classes
  button_classes = [base_classes, variant_classes, size_classes, additional_class].join(" ")
%>

<button type="<%= button_type %>" 
        class="<%= button_classes %>"
        <% if disabled %>disabled<% end %>
        <% if onclick %>onclick="<%= onclick %>"<% end %>
        <% data_attrs.each do |key, value| %>
          data-<%= key.to_s.dasherize %>="<%= value %>"
        <% end %>>
  <%= text %>
</button>