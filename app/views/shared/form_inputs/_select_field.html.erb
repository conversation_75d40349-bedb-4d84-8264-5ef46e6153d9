<%# 
  Shadcn-style select field
  
  Parameters:
  - form: form builder object
  - field: field name (symbol)
  - options: options for select (array or options_for_select result)
  - label: label text (optional, defaults to humanized field name)
  - prompt: prompt text (optional)
  - required: whether field is required (optional, default: false)
  - help_text: help text below input (optional)
  - wrapper_class: additional classes for wrapper (optional)
%>

<%
  label_text = local_assigns[:label] || field.to_s.humanize
  prompt_text = local_assigns[:prompt]
  required = local_assigns[:required] || false
  help_text = local_assigns[:help_text]
  wrapper_class = local_assigns[:wrapper_class] || ""
  select_options = local_assigns[:options] || []
  
  # Get current value from form object for better retention
  current_value = form.object&.send(field) if form.object.respond_to?(field)
  
  # Check for validation errors
  has_errors = form.object&.errors&.key?(field)
  error_messages = form.object&.errors&.full_messages_for(field) || []
%>

<div class="<%= wrapper_class %>">
  <%= form.label field, class: "block text-sm font-medium text-gray-900 mb-2" do %>
    <%= label_text %>
    <% if required %>
      <span class="text-red-500 ml-1">*</span>
    <% end %>
  <% end %>
  
  <%
    # Dynamic CSS classes based on validation state
    select_classes = [
      "flex h-10 w-full rounded-md border bg-white px-3 py-2 text-sm ring-offset-white",
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",
      "disabled:cursor-not-allowed disabled:opacity-50",
      has_errors ? "border-red-300 focus-visible:ring-red-500" : "border-gray-200 focus-visible:ring-blue-500"
    ].join(" ")
  %>
  
  <%= form.select field, select_options,
      { 
        prompt: prompt_text, 
        required: required,
        selected: current_value
      },
      { class: select_classes }
  %>
  
  <% if has_errors %>
    <div class="mt-1">
      <% error_messages.each do |error| %>
        <p class="text-xs text-red-600"><%= error %></p>
      <% end %>
    </div>
  <% end %>
  
  <% if help_text %>
    <p class="mt-2 text-xs text-gray-500"><%= help_text %></p>
  <% end %>
</div>