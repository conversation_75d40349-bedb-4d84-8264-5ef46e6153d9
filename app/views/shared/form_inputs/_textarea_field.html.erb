<%# 
  Shadcn-style textarea field
  
  Parameters:
  - form: form builder object
  - field: field name (symbol)
  - label: label text (optional, defaults to humanized field name)
  - placeholder: placeholder text (optional)
  - required: whether field is required (optional, default: false)
  - rows: number of rows (optional, default: 3)
  - help_text: help text below input (optional)
  - wrapper_class: additional classes for wrapper (optional)
%>

<%
  label_text = local_assigns[:label] || field.to_s.humanize
  placeholder_text = local_assigns[:placeholder] || ""
  required = local_assigns[:required] || false
  rows = local_assigns[:rows] || 3
  help_text = local_assigns[:help_text]
  wrapper_class = local_assigns[:wrapper_class] || ""
  
  # Get current value from form object for better retention
  current_value = form.object&.send(field) if form.object.respond_to?(field)
  
  # Check for validation errors
  has_errors = form.object&.errors&.key?(field)
  error_messages = form.object&.errors&.full_messages_for(field) || []
%>

<div class="<%= wrapper_class %>">
  <%= form.label field, class: "block text-sm font-medium text-gray-900 mb-2" do %>
    <%= label_text %>
    <% if required %>
      <span class="text-red-500 ml-1">*</span>
    <% end %>
  <% end %>
  
  <%
    # Dynamic CSS classes based on validation state
    textarea_classes = [
      "flex min-h-[80px] w-full rounded-md border bg-white px-3 py-2 text-sm ring-offset-white",
      "placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",
      "disabled:cursor-not-allowed disabled:opacity-50 resize-none",
      has_errors ? "border-red-300 focus-visible:ring-red-500" : "border-gray-200 focus-visible:ring-blue-500"
    ].join(" ")
  %>
  
  <%= form.text_area field,
      value: current_value,
      placeholder: placeholder_text,
      required: required,
      rows: rows,
      class: textarea_classes
  %>
  
  <% if has_errors %>
    <div class="mt-1">
      <% error_messages.each do |error| %>
        <p class="text-xs text-red-600"><%= error %></p>
      <% end %>
    </div>
  <% end %>
  
  <% if help_text %>
    <p class="mt-2 text-xs text-gray-500"><%= help_text %></p>
  <% end %>
</div>