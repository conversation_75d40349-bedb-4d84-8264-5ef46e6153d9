<!-- Help & Support Page -->
<div class="min-h-screen bg-gray-50">
  <%= render 'shared/top_navigation',
      title: 'Help & Support',
      subtitle: 'Find answers and get assistance',
      show_back_button: true,
      back_path: root_path %>

  <!-- Main Content -->
  <main class="pb-20">

    <!-- Quick Help Actions -->
    <section class="bg-white border-b border-gray-200">
      <div class="px-4 py-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Need Help?</h2>
        <div class="grid grid-cols-1 gap-3">
          <%= link_to help_contact_path,
              class: "flex items-center p-4 bg-zeiss-600 hover:bg-zeiss-700
                     text-white rounded-xl transition-colors duration-200 shadow-sm" do %>
            <i data-lucide="message-circle-question" class="w-6 h-6 mr-3"></i>
            <div class="text-left">
              <span class="text-sm font-semibold block">Ask a Question</span>
              <span class="text-xs opacity-90">Get personalized help from our team</span>
            </div>
          <% end %>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="px-4 py-6">
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-4 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Frequently Asked Questions</h3>
        </div>

        <div class="divide-y divide-gray-100">
          <% @faqs.each_with_index do |faq, index| %>
            <div class="px-4 py-4">
              <button class="w-full text-left focus:outline-none focus:ring-2 focus:ring-zeiss-500 focus:ring-offset-2 rounded-lg faq-toggle"
                      data-index="<%= index %>">
                <div class="flex items-center justify-between">
                  <h4 class="font-semibold text-gray-900 text-sm pr-4"><%= faq[:question] %></h4>
                  <i data-lucide="chevron-down" class="w-5 h-5 text-gray-400 transform transition-transform duration-200 faq-icon" data-index="<%= index %>"></i>
                </div>
              </button>
              <div class="mt-3 hidden faq-content"
                   data-index="<%= index %>">
                <p class="text-sm text-gray-600 leading-relaxed"><%= faq[:answer] %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </section>

    <!-- App Info Section -->
    <section class="px-4 py-6">
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-4 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">App Information</h3>
        </div>

        <div class="px-4 py-4 space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Version</span>
            <span class="text-sm font-medium text-gray-900">1.0.0</span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Last Updated</span>
            <span class="text-sm font-medium text-gray-900"><%= Date.current.strftime('%B %Y') %></span>
          </div>

          <div class="pt-4 border-t border-gray-100">
            <p class="text-xs text-gray-500 leading-relaxed">
              Zeiss Points is your gateway to earning and redeeming rewards for Zeiss product sales.
              For technical support or account issues, please use the "Ask a Question" feature above.
            </p>
          </div>
        </div>
      </div>
    </section>

  </main>

  <%= render 'shared/bottom_navigation', active_tab: 'help' %>
</div>

<script>
  // FAQ toggle functionality
  document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.faq-toggle');

    buttons.forEach(button => {
      button.addEventListener('click', function() {
        const index = this.dataset.index;
        const content = document.querySelector(`.faq-content[data-index="${index}"]`);
        const icon = document.querySelector(`.faq-icon[data-index="${index}"]`);

        if (content.classList.contains('hidden')) {
          content.classList.remove('hidden');
          icon.style.transform = 'rotate(180deg)';
        } else {
          content.classList.add('hidden');
          icon.style.transform = 'rotate(0deg)';
        }
      });
    });
  });
</script>