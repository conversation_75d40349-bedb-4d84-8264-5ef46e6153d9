# frozen_string_literal: true

class GiftCardBatchProcessorJob < ApplicationJob
  queue_as :default

  # Process gift card orders for a specific date
  def perform(date = Date.current)
    Rails.logger.info "Processing gift card batch for #{date}"

    # Find unbatched gift card orders for the date
    unbatched_orders = GiftCardOrder.for_date(date).unbatched

    if unbatched_orders.empty?
      Rails.logger.info "No unbatched gift card orders found for #{date}"
      return
    end

    # Create or find batch for the date
    batch = GiftCardBatch.for_date_or_create(date)

    begin
      # Process orders into batch and generate CSV
      batch.process_orders!
      Rails.logger.info "Generated batch #{batch.filename} with #{batch.total_orders} orders"

      # Upload to SFTP
      if batch.upload_to_sftp!
        Rails.logger.info "Successfully uploaded batch #{batch.filename} to SFTP"

        # Send notification email to admins
        GiftCardBatchMailer.batch_uploaded(batch).deliver_now
      else
        Rails.logger.error "Failed to upload batch #{batch.filename} to SFTP"

        # Send failure notification
        GiftCardBatchMailer.batch_upload_failed(batch).deliver_now
      end
    rescue => e
      Rails.logger.error "Error processing gift card batch for #{date}: #{e.message}"

      # Mark batch as failed
      batch.update(status: :failed, upload_error: e.message) if batch.persisted?

      # Send error notification
      GiftCardBatchMailer.batch_processing_failed(batch, e.message).deliver_now if batch.persisted?

      raise e
    end
  end
end
