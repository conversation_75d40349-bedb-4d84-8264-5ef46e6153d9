# frozen_string_literal: true

class GiftCardBatchMailer < ApplicationMailer
  default from: Rails.application.credentials.dig(:email, :from) || "<EMAIL>"

  def batch_uploaded(batch)
    @batch = batch
    @admin_emails = admin_notification_emails

    mail(
      to: @admin_emails,
      subject: "Gift Card Batch Uploaded Successfully - #{@batch.filename}"
    )
  end

  def batch_upload_failed(batch)
    @batch = batch
    @admin_emails = admin_notification_emails

    mail(
      to: @admin_emails,
      subject: "Gift Card Batch Upload Failed - #{@batch.filename}"
    )
  end

  def batch_processing_failed(batch, error_message)
    @batch = batch
    @error_message = error_message
    @admin_emails = admin_notification_emails

    mail(
      to: @admin_emails,
      subject: "Gift Card Batch Processing Failed - #{@batch&.batch_date || Date.current}"
    )
  end

  private

  def admin_notification_emails
    # Get admin emails from configuration or environment
    emails = Rails.application.credentials.dig(:gift_card_notifications, :admin_emails) ||
      ENV["GIFT_CARD_ADMIN_EMAILS"]&.split(",")&.map(&:strip)

    # Fallback to admin users if no specific emails configured
    emails.presence || User.admin.pluck(:email)
  end
end
