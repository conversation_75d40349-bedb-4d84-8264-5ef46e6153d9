class SupportTicketMailer < ApplicationMailer
  def new_admin_ticket(support_ticket)
    @support_ticket = support_ticket
    @user = support_ticket.user

    mail(
      to: "<EMAIL>", # Configure this in production
      subject: "[Zeiss Points Support] #{@support_ticket.category_name}: #{@support_ticket.subject}",
      reply_to: @user.email
    )
  end

  def admin_response(support_ticket)
    @support_ticket = support_ticket
    @user = support_ticket.user
    @admin = support_ticket.admin_user

    mail(
      to: @user.email,
      subject: "Re: #{@support_ticket.subject} [Ticket ##{@support_ticket.id}]"
    )
  end

  def auto_response(support_ticket)
    @support_ticket = support_ticket
    @user = support_ticket.user

    mail(
      to: @user.email,
      subject: "Thank you for contacting Zeiss Points Support [Ticket ##{@support_ticket.id}]"
    )
  end
end
