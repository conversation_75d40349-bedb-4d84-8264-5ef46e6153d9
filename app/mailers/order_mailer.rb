class OrderMailer < ApplicationMailer
  def new_order_submitted
    @order = params[:record]
    @user = @order.user
    @admin = params[:recipient]
    @line_items = safe_line_items_with_products(@order)

    mail(
      to: @admin.email,
      subject: "New Order ##{@order.id} Pending Approval - #{@order.total_points} Points"
    )
  end

  def order_approved
    @order = params[:record]
    @user = params[:recipient]
    @approved_by = params[:approved_by]
    @line_items = safe_line_items_with_products(@order)

    mail(
      to: @user.email,
      subject: "Order ##{@order.id} Approved - #{@order.total_points} Points Deducted"
    )
  end

  def order_rejected
    @order = params[:record]
    @user = params[:recipient]
    @rejected_by = params[:rejected_by]
    @reason = params[:reason]
    @line_items = safe_line_items_with_products(@order)

    mail(
      to: @user.email,
      subject: "Order ##{@order.id} Rejected"
    )
  end

  def order_shipped
    @order = params[:record]
    @user = params[:recipient]
    @shipped_by = params[:shipped_by]
    @line_items = safe_line_items_with_products(@order)

    mail(
      to: @user.email,
      subject: "Order ##{@order.id} Shipped - Tracking Information"
    )
  end

  def order_delivered
    @order = params[:record]
    @user = params[:recipient]
    @line_items = safe_line_items_with_products(@order)

    mail(
      to: @user.email,
      subject: "Order ##{@order.id} Delivered - Thank You!"
    )
  end

  private

  def safe_line_items_with_products(order)
    # Handle both real ActiveRecord objects and preview mock objects
    line_items = order.line_items
    if line_items.respond_to?(:includes)
      line_items.includes(:product)
    else
      line_items
    end
  end
end
