class HelpMailer < ApplicationMailer
  def new_question(user, question_params)
    @user = user
    @question = question_params
    @subject = question_params[:subject]
    @message = question_params[:message]
    @category = question_params[:category]

    mail(
      to: "<EMAIL>", # Configure this in production
      subject: "[Zeiss Points Support] #{@category.humanize}: #{@subject}",
      reply_to: @user.email
    )
  end
end
