class SaleMailer < ApplicationMailer
  def new_sale_submitted
    @sale = params[:record]
    @seller = @sale.user
    @product = @sale.product
    @admin = params[:recipient]

    mail(
      to: @admin.email,
      subject: "New Sale Pending Approval - #{@product.name}"
    )
  end

  def sale_approved
    @sale = params[:record]
    @user = params[:recipient]
    @product = @sale.product
    @approved_by = params[:approved_by]
    @base_points = @sale.points
    @total_points = @sale.total_points_with_promotions
    @bonus_points = @total_points - @base_points

    mail(
      to: @user.email,
      subject: "Sale Approved - #{@total_points} Points Credited!"
    )
  end

  def sale_rejected
    @sale = params[:record]
    @user = params[:recipient]
    @product = @sale.product
    @rejected_by = params[:rejected_by]
    @reason = params[:reason]

    mail(
      to: @user.email,
      subject: "Sale Rejected - #{@product.name}"
    )
  end
end
