# Notifies users when their order is approved
#
# OrderApprovedNotifier.with(record: @order, approved_by: @admin).deliver(@order.user)

class OrderApprovedNotifier < ApplicationNotifier
  # Deliver to user via email and push notifications
  deliver_by :email do |config|
    config.mailer = "OrderMailer"
    config.method = :order_approved
    config.if = -> { recipient.should_receive_notification?("order") && recipient.email_notifications_enabled }
  end

  deliver_by :web_push do |config|
    config.vapid_public_key = Rails.application.credentials.dig(:web_push, :vapid_public_key)
    config.vapid_private_key = Rails.application.credentials.dig(:web_push, :vapid_private_key)
    config.vapid_subject = Rails.application.credentials.dig(:web_push, :vapid_subject)
    config.if = -> { recipient.should_receive_notification?("order") && recipient.push_notifications_enabled && !recipient.in_quiet_hours? }
  end

  # Validate that we have an order record and approved_by admin
  validates :record, presence: true
  required_params :approved_by

  notification_methods do
    def message
      "Your order ##{record.id} has been approved! #{record.total_points} points deducted from your account."
    end

    def title
      "Order Approved!"
    end

    def url
      Rails.application.routes.url_helpers.orders_path
    end

    def order
      record
    end

    def total_points
      record.total_points
    end

    def approved_by_admin
      params[:approved_by]
    end

    def line_items
      record.line_items.includes(:product)
    end

    def sap_id
      record.sap_id
    end
  end
end
