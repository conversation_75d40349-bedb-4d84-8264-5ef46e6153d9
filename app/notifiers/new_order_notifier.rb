# Notifies admins when a new order is placed
#
# NewOrderNotifier.with(record: @order).deliver(User.admin)

class NewOrderNotifier < ApplicationNotifier
  # Deliver to admins via email and push notifications
  deliver_by :email do |config|
    config.mailer = "OrderMailer"
    config.method = :new_order_submitted
    config.if = -> { recipient.should_receive_notification?("order") && recipient.email_notifications_enabled }
  end

  deliver_by :web_push, class: "DeliveryMethods::WebPush" do |config|
    if Rails.env.test?
      config.vapid_public_key = "test_public_key"
      config.vapid_private_key = "test_private_key"
      config.vapid_subject = "mailto:<EMAIL>"
    else
      config.vapid_public_key = Rails.application.credentials.dig(:web_push, :vapid_public_key)
      config.vapid_private_key = Rails.application.credentials.dig(:web_push, :vapid_private_key)
      config.vapid_subject = Rails.application.credentials.dig(:web_push, :vapid_subject)
    end
    config.if = -> { recipient.should_receive_notification?("order") && recipient.push_notifications_enabled && !recipient.in_quiet_hours? }
  end

  # Define notification recipients - all admin users
  recipients -> { User.where(role: :admin) }

  # Validate that we have an order record
  validates :record, presence: true

  notification_methods do
    def message
      "New order ##{record.id} placed by #{record.user.email} for #{record.total_points} points"
    end

    def title
      "New Order Pending Approval"
    end

    def url
      Rails.application.routes.url_helpers.admin_order_path(record)
    end

    def order
      record
    end

    def customer
      record.user
    end

    def total_points
      record.total_points
    end

    def line_items
      record.line_items.includes(:product)
    end
  end
end
