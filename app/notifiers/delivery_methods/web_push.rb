class DeliveryMethods::WebPush < ApplicationDeliveryMethod
  # Specify the config options your delivery method requires in its config block
  required_options :vapid_public_key, :vapid_private_key, :vapid_subject

  def deliver
    # Get the user's active push subscriptions
    user = notification.recipient
    return unless user.respond_to?(:active_push_subscriptions)

    subscriptions = user.active_push_subscriptions
    return if subscriptions.empty?

    # Prepare the notification payload
    payload = {
      title: notification.params[:title] || "Notification",
      body: notification.params[:body] || notification.params[:message],
      icon: notification.params[:icon] || "/icon.svg",
      badge: notification.params[:badge] || "/icon.svg",
      data: notification.params[:data] || {},
      actions: notification.params[:actions] || []
    }.to_json

    # VAPID configuration
    vapid_config = {
      subject: config[:vapid_subject],
      public_key: config[:vapid_public_key],
      private_key: config[:vapid_private_key]
    }

    # Send to each active subscription
    subscriptions.find_each do |subscription|
      response = ::WebPush.payload_send(
        message: payload,
        endpoint: subscription.endpoint,
        p256dh: subscription.p256dh_key,
        auth: subscription.auth_key,
        vapid: vapid_config
      )

      # Update last used timestamp on successful send
      subscription.touch_last_used! if response.code == "200" || response.code == "201"
    rescue ::WebPush::InvalidSubscription, ::WebPush::ExpiredSubscription => e
      # Deactivate invalid or expired subscriptions
      Rails.logger.warn "Deactivating invalid push subscription #{subscription.id}: #{e.message}"
      subscription.deactivate!
    rescue => e
      # Log other errors but don't deactivate subscription
      Rails.logger.error "Failed to send push notification to subscription #{subscription.id}: #{e.message}"
    end
  end
end
