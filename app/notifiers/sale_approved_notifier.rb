# Notifies users when their sale is approved
#
# SaleApprovedNotifier.with(record: @sale, approved_by: @admin).deliver(@sale.user)

class SaleApprovedNotifier < ApplicationNotifier
  # Deliver to user via email and push notifications
  deliver_by :email do |config|
    config.mailer = "SaleMailer"
    config.method = :sale_approved
    config.if = -> { recipient.should_receive_notification?("sale") && recipient.email_notifications_enabled }
  end

  deliver_by :web_push do |config|
    config.vapid_public_key = Rails.application.credentials.dig(:web_push, :vapid_public_key)
    config.vapid_private_key = Rails.application.credentials.dig(:web_push, :vapid_private_key)
    config.vapid_subject = Rails.application.credentials.dig(:web_push, :vapid_subject)
    config.if = -> { recipient.should_receive_notification?("sale") && recipient.push_notifications_enabled && !recipient.in_quiet_hours? }
  end

  # Validate that we have a sale record and approved_by admin
  validates :record, presence: true
  required_params :approved_by

  notification_methods do
    def message
      bonus_text = (bonus_points > 0) ? " (including #{bonus_points} bonus points)" : ""
      "Your sale of #{record.product.name} has been approved! #{total_points} points credited to your account#{bonus_text}."
    end

    def title
      "Sale Approved!"
    end

    def url
      Rails.application.routes.url_helpers.root_path
    end

    def sale
      record
    end

    def product
      record.product
    end

    def base_points
      record.points
    end

    def total_points
      record.total_points_with_promotions
    end

    def bonus_points
      total_points - base_points
    end

    def approved_by_admin
      params[:approved_by]
    end

    def serial_number
      record.serial_number
    end

    def has_bonus?
      bonus_points > 0
    end
  end
end
