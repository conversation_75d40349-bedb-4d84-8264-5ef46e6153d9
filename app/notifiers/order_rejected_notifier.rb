# Notifies users when their order is rejected
#
# OrderRejectedNotifier.with(record: @order, rejected_by: @admin, reason: "Insufficient points").deliver(@order.user)

class OrderRejectedNotifier < ApplicationNotifier
  # Deliver to user via email and push notifications
  deliver_by :email do |config|
    config.mailer = "OrderMailer"
    config.method = :order_rejected
    config.if = -> { recipient.should_receive_notification?("order") && recipient.email_notifications_enabled }
  end

  deliver_by :web_push do |config|
    config.vapid_public_key = Rails.application.credentials.dig(:web_push, :vapid_public_key)
    config.vapid_private_key = Rails.application.credentials.dig(:web_push, :vapid_private_key)
    config.vapid_subject = Rails.application.credentials.dig(:web_push, :vapid_subject)
    config.if = -> { recipient.should_receive_notification?("order") && recipient.push_notifications_enabled && !recipient.in_quiet_hours? }
  end

  # Validate that we have an order record and rejected_by admin
  validates :record, presence: true
  required_params :rejected_by

  notification_methods do
    def message
      reason_text = params[:reason].present? ? " Reason: #{params[:reason]}" : ""
      "Your order ##{record.id} has been rejected.#{reason_text}"
    end

    def title
      "Order Rejected"
    end

    def url
      Rails.application.routes.url_helpers.products_path
    end

    def order
      record
    end

    def rejected_by_admin
      params[:rejected_by]
    end

    def rejection_reason
      params[:reason]
    end

    def line_items
      record.line_items.includes(:product)
    end

    def has_reason?
      params[:reason].present?
    end
  end
end
