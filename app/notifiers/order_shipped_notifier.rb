# Notifies users when their order is shipped
#
# OrderShippedNotifier.with(record: @order, shipped_by: @admin).deliver(@order.user)

class OrderShippedNotifier < ApplicationNotifier
  # Deliver to user via email and push notifications
  deliver_by :email do |config|
    config.mailer = "OrderMailer"
    config.method = :order_shipped
    config.if = -> { recipient.should_receive_notification?("order") && recipient.email_notifications_enabled }
  end

  deliver_by :web_push do |config|
    config.vapid_public_key = Rails.application.credentials.dig(:web_push, :vapid_public_key)
    config.vapid_private_key = Rails.application.credentials.dig(:web_push, :vapid_private_key)
    config.vapid_subject = Rails.application.credentials.dig(:web_push, :vapid_subject)
    config.if = -> { recipient.should_receive_notification?("order") && recipient.push_notifications_enabled && !recipient.in_quiet_hours? }
  end

  # Validate that we have an order record and shipped_by admin
  validates :record, presence: true
  required_params :shipped_by

  notification_methods do
    def message
      "Your order ##{record.id} has been shipped and is on its way to you!"
    end

    def title
      "Order Shipped!"
    end

    def url
      Rails.application.routes.url_helpers.orders_path
    end

    def order
      record
    end

    def shipped_by_admin
      params[:shipped_by]
    end

    def line_items
      record.line_items.includes(:product)
    end

    def sap_id
      record.sap_id
    end

    def shipping_type
      record.shipping_type
    end

    def shipping_address
      record.shipping_address
    end
  end
end
