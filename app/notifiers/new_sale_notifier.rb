# Notifies admins when a new sale is submitted for approval
#
# NewSaleNotifier.with(record: @sale).deliver(User.admin)

class NewSaleNotifier < ApplicationNotifier
  # Deliver to admins via email and push notifications
  deliver_by :email do |config|
    config.mailer = "SaleMailer"
    config.method = :new_sale_submitted
    config.if = -> { recipient.should_receive_notification?("sale") && recipient.email_notifications_enabled }
  end

  deliver_by :web_push do |config|
    config.vapid_public_key = Rails.application.credentials.dig(:web_push, :vapid_public_key)
    config.vapid_private_key = Rails.application.credentials.dig(:web_push, :vapid_private_key)
    config.vapid_subject = Rails.application.credentials.dig(:web_push, :vapid_subject)
    config.if = -> { recipient.should_receive_notification?("sale") && recipient.push_notifications_enabled && !recipient.in_quiet_hours? }
  end

  # Define notification recipients - all admin users
  recipients -> { User.where(role: :admin) }

  # Validate that we have a sale record
  validates :record, presence: true

  notification_methods do
    def message
      "New sale submitted by #{record.user.email} for #{record.product.name} (#{record.points} points)"
    end

    def title
      "New Sale Pending Approval"
    end

    def url
      Rails.application.routes.url_helpers.admin_sale_path(record)
    end

    def sale
      record
    end

    def seller
      record.user
    end

    def product
      record.product
    end

    def points
      record.points
    end

    def serial_number
      record.serial_number
    end
  end
end
