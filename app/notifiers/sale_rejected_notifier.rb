# Notifies users when their sale is rejected
#
# SaleRejectedNotifier.with(record: @sale, rejected_by: @admin, reason: "Invalid serial number").deliver(@sale.user)

class SaleRejectedNotifier < ApplicationNotifier
  # Deliver to user via email and push notifications
  deliver_by :email do |config|
    config.mailer = "SaleMailer"
    config.method = :sale_rejected
    config.if = -> { recipient.should_receive_notification?("sale") && recipient.email_notifications_enabled }
  end

  deliver_by :web_push do |config|
    config.vapid_public_key = Rails.application.credentials.dig(:web_push, :vapid_public_key)
    config.vapid_private_key = Rails.application.credentials.dig(:web_push, :vapid_private_key)
    config.vapid_subject = Rails.application.credentials.dig(:web_push, :vapid_subject)
    config.if = -> { recipient.should_receive_notification?("sale") && recipient.push_notifications_enabled && !recipient.in_quiet_hours? }
  end

  # Validate that we have a sale record and rejected_by admin
  validates :record, presence: true
  required_params :rejected_by

  notification_methods do
    def message
      reason_text = params[:reason].present? ? " Reason: #{params[:reason]}" : ""
      "Your sale of #{record.product.name} (#{record.serial_number}) has been rejected.#{reason_text}"
    end

    def title
      "Sale Rejected"
    end

    def url
      Rails.application.routes.url_helpers.new_sale_path
    end

    def sale
      record
    end

    def product
      record.product
    end

    def rejected_by_admin
      params[:rejected_by]
    end

    def rejection_reason
      params[:reason]
    end

    def serial_number
      record.serial_number
    end

    def has_reason?
      params[:reason].present?
    end
  end
end
