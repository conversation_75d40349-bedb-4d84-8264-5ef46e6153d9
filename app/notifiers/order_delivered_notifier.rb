# Notifies users when their order is delivered
#
# OrderDeliveredNotifier.with(record: @order).deliver(@order.user)

class OrderDeliveredNotifier < ApplicationNotifier
  # Deliver to user via email and push notifications
  deliver_by :email do |config|
    config.mailer = "OrderMailer"
    config.method = :order_delivered
    config.if = -> { recipient.should_receive_notification?("order") && recipient.email_notifications_enabled }
  end

  deliver_by :web_push do |config|
    config.vapid_public_key = Rails.application.credentials.dig(:web_push, :vapid_public_key)
    config.vapid_private_key = Rails.application.credentials.dig(:web_push, :vapid_private_key)
    config.vapid_subject = Rails.application.credentials.dig(:web_push, :vapid_subject)
    config.if = -> { recipient.should_receive_notification?("order") && recipient.push_notifications_enabled && !recipient.in_quiet_hours? }
  end

  # Validate that we have an order record
  validates :record, presence: true

  notification_methods do
    def message
      "Your order ##{record.id} has been delivered successfully! Thank you for choosing <PERSON><PERSON><PERSON>."
    end

    def title
      "Order Delivered!"
    end

    def url
      Rails.application.routes.url_helpers.products_path
    end

    def order
      record
    end

    def line_items
      record.line_items.includes(:product)
    end

    def sap_id
      record.sap_id
    end

    def total_points
      record.total_points
    end

    def delivery_time_text
      return "" unless record.shipped_at && record.delivered_at
      ActionController::Base.helpers.time_ago_in_words(record.shipped_at, record.delivered_at)
    end
  end
end
