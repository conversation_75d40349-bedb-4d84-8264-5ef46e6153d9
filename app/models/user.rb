# == Schema Information
#
# Table name: users
#
#  id                     :bigint           not null, primary key
#  approved_sales_count   :integer          default(0), not null
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  email                  :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  failed_attempts        :integer          default(0), not null
#  first_name             :string           default("<PERSON><PERSON><PERSON>"), not null
#  last_name              :string           default("User"), not null
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  locked_at              :datetime
#  notification_settings  :jsonb
#  orders_count           :integer          default(0), not null
#  pending_sales_count    :integer          default(0), not null
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  role                   :integer          default("regular"), not null
#  sales_count            :integer          default(0), not null
#  sign_in_count          :integer          default(0), not null
#  status                 :integer          default("inactive"), not null
#  total_points_earned    :integer          default(0), not null
#  unconfirmed_email      :string
#  unlock_token           :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  store_id               :bigint
#
# Indexes
#
#  index_users_on_approved_sales_count   (approved_sales_count)
#  index_users_on_confirmation_token     (confirmation_token) UNIQUE
#  index_users_on_email                  (email) UNIQUE
#  index_users_on_notification_settings  (notification_settings) USING gin
#  index_users_on_orders_count           (orders_count)
#  index_users_on_reset_password_token   (reset_password_token) UNIQUE
#  index_users_on_sales_count            (sales_count)
#  index_users_on_status                 (status)
#  index_users_on_store_id               (store_id)
#  index_users_on_total_points_earned    (total_points_earned)
#  index_users_on_unlock_token           (unlock_token) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (store_id => stores.id)
#
class User < ApplicationRecord
  has_many :orders, dependent: :nullify
  has_many :sales, dependent: :nullify
  belongs_to :store, optional: true

  # Counter culture for store analytics
  counter_culture :store, column_name: "users_count"

  # Counter culture for store chain analytics (through store)
  counter_culture [:store, :store_chain], column_name: "total_users_count"
  has_one :address, as: :addressable, dependent: :destroy
  accepts_nested_attributes_for :address
  # Regions where this user is the admin
  has_many :regions, foreign_key: :admin_user_id, dependent: :nullify
  has_one :cart, dependent: :destroy

  # Method to create cart if it doesn't exist (for compatibility with current_cart method)
  def create_cart
    cart || create_cart!
  end

  # Include default devise modules. Others available are:
  # :timeoutable, :omniauthable
  devise :database_authenticatable, :registerable,
    :recoverable, :rememberable, :validatable,
    :trackable, :confirmable, :lockable

  enum :role, {regular: 0, admin: 1, super_admin: 2}, default: :regular
  enum :status, {active: 0, inactive: 1, deleted: 2}, default: :inactive

  validates :email, presence: true, uniqueness: true
  validates :role, presence: true
  validates :store_id, presence: true, on: :create
  validates :status, presence: true

  has_one :wallet, dependent: :destroy
  has_many :push_notification_subscriptions, dependent: :destroy
  has_many :support_tickets, dependent: :destroy
  after_create :create_wallet

  # Notification settings stored as JSON
  store_accessor :notification_settings,
    :push_notifications_enabled,
    :email_notifications_enabled,
    :promotion_notifications,
    :order_notifications,
    :sale_notifications,
    :marketing_notifications,
    :security_notifications,
    :notification_frequency,
    :quiet_hours_enabled,
    :quiet_hours_start,
    :quiet_hours_end

  # Only allow sign in if email is confirmed, store is active, and user is active
  def active_for_authentication?
    super && confirmed? && store&.active? && active?
  end

  def inactive_message
    return :unconfirmed unless confirmed?
    return :inactive_store unless store&.active?
    return :inactive unless active?
    super
  end

  # Ransack configuration for admin search
  def self.ransackable_attributes(auth_object = nil)
    %w[created_at email id role status updated_at store_id sign_in_count last_sign_in_at confirmed_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[store wallet sales orders address push_notification_subscriptions]
  end

  # Push notification subscription helpers
  def active_push_subscriptions
    push_notification_subscriptions.active
  end

  def has_push_subscriptions?
    active_push_subscriptions.exists?
  end

  def add_push_subscription(endpoint:, p256dh_key:, auth_key:, user_agent: nil, device_name: nil)
    push_notification_subscriptions.find_or_create_by(endpoint: endpoint) do |subscription|
      subscription.p256dh_key = p256dh_key
      subscription.auth_key = auth_key
      subscription.user_agent = user_agent
      subscription.device_name = device_name
      subscription.active = true
      subscription.last_used_at = Time.current
    end
  end

  def remove_push_subscription(endpoint)
    push_notification_subscriptions.find_by(endpoint: endpoint)&.destroy
  end

  def cleanup_stale_subscriptions!
    push_notification_subscriptions.where("last_used_at < ? OR last_used_at IS NULL", 30.days.ago).destroy_all
  end

  # Notification settings helpers
  def notification_settings_with_defaults
    {
      push_notifications_enabled: push_notifications_enabled.nil? || push_notifications_enabled,
      email_notifications_enabled: email_notifications_enabled.nil? || email_notifications_enabled,
      promotion_notifications: promotion_notifications.nil? || promotion_notifications,
      order_notifications: order_notifications.nil? || order_notifications,
      sale_notifications: sale_notifications.nil? || sale_notifications,
      marketing_notifications: marketing_notifications.nil? ? false : marketing_notifications,
      security_notifications: security_notifications.nil? || security_notifications,
      notification_frequency: notification_frequency || "immediate",
      quiet_hours_enabled: quiet_hours_enabled.nil? ? false : quiet_hours_enabled,
      quiet_hours_start: quiet_hours_start || "22:00",
      quiet_hours_end: quiet_hours_end || "08:00"
    }
  end

  def should_receive_notification?(type)
    settings = notification_settings_with_defaults

    # Check if notifications are globally enabled
    return false unless settings[:push_notifications_enabled] || settings[:email_notifications_enabled]

    # Check specific notification type
    case type.to_s
    when "promotion"
      settings[:promotion_notifications]
    when "order"
      settings[:order_notifications]
    when "sale"
      settings[:sale_notifications]
    when "marketing"
      settings[:marketing_notifications]
    when "security"
      settings[:security_notifications]
    else
      true # Default to allow for unknown types
    end
  end

  def in_quiet_hours?
    return false unless notification_settings_with_defaults[:quiet_hours_enabled]

    start_time = notification_settings_with_defaults[:quiet_hours_start]
    end_time = notification_settings_with_defaults[:quiet_hours_end]
    current_time = Time.current.strftime("%H:%M")

    # Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if start_time > end_time
      current_time >= start_time || current_time <= end_time
    else
      current_time.between?(start_time, end_time)
    end
  end

  private

  def create_wallet
    Wallet.create!(user: self)
  end

  def create_cart!
    Cart.create!(user: self)
  end
end
