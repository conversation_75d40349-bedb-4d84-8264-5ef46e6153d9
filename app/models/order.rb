# == Schema Information
#
# Table name: orders
#
#  id                 :bigint           not null, primary key
#  approved_at        :datetime
#  approved_by        :bigint
#  delivered_at       :datetime
#  line_items_count   :integer          default(0), not null
#  points             :integer          not null
#  rejected_at        :datetime
#  rejected_by        :bigint
#  sap_processed_at   :datetime
#  shipped_at         :datetime
#  shipped_by         :bigint
#  shipping_address   :string
#  shipping_type      :string           not null
#  status             :integer          default("pending"), not null
#  total_points_cache :integer          default(0), not null
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  sap_id             :string
#  user_id            :bigint           not null
#
# Indexes
#
#  index_orders_on_approved_by         (approved_by)
#  index_orders_on_line_items_count    (line_items_count)
#  index_orders_on_rejected_by         (rejected_by)
#  index_orders_on_sap_id              (sap_id) UNIQUE
#  index_orders_on_shipped_by          (shipped_by)
#  index_orders_on_total_points_cache  (total_points_cache)
#  index_orders_on_user_id             (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class Order < ApplicationRecord
  belongs_to :user
  has_many :line_items, dependent: :destroy
  has_many :gift_card_orders, dependent: :destroy
  has_many :products, through: :line_items

  # Counter culture for user analytics
  counter_culture :user, column_name: "orders_count"

  # Counter culture for store analytics (through user)
  counter_culture [:user, :store],
    column_name: proc { |model| model.user.store_id ? "orders_count" : nil }

  enum :status, {pending: 0, approved: 1, rejected: 2, shipped: 3, delivered: 4}

  validates :shipping_type, presence: true
  validates :points, presence: true, numericality: {greater_than: 0}
  validates :sap_id, uniqueness: true, allow_blank: true

  scope :sap_processed, -> { where.not(sap_id: nil) }
  scope :sap_pending, -> { where(sap_id: nil) }

  def total_points
    # Use counter cache if available, otherwise calculate from line items
    if has_attribute?(:total_points_cache)
      total_points_cache
    elsif line_items.loaded? || line_items.any?
      line_items.sum(&:total_points)
    else
      points
    end
  end

  def sap_processed?
    sap_id.present?
  end

  def sap_pending?
    !sap_processed?
  end
end
