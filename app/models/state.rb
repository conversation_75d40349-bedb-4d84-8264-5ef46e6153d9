# == Schema Information
#
# Table name: states
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  region_id  :bigint           not null
#
# Indexes
#
#  index_states_on_region_id           (region_id)
#  index_states_on_region_id_and_name  (region_id,name) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (region_id => regions.id)
#
class State < ApplicationRecord
  belongs_to :region

  # Counter culture for region analytics
  counter_culture :region, column_name: "states_count"

  validates :name, presence: true, uniqueness: {scope: :region_id}

  # Ransack configuration for admin search
  def self.ransackable_attributes(auth_object = nil)
    %w[name created_at updated_at id region_id]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[region addresses]
  end
end
