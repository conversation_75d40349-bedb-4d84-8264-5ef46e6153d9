# == Schema Information
#
# Table name: sale_promotion_bonuses
#
#  id           :bigint           not null, primary key
#  sale_id      :bigint           not null
#  promotion_id :bigint           not null
#  bonus_points :integer          not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_sale_promotion_bonuses_on_sale_id_and_promotion_id  (sale_id,promotion_id) UNIQUE
#  index_sale_promotion_bonuses_on_sale_id                   (sale_id)
#  index_sale_promotion_bonuses_on_promotion_id              (promotion_id)
#
# Foreign Keys
#
#  fk_rails_...  (sale_id => sales.id)
#  fk_rails_...  (promotion_id => promotions.id)
#

class SalePromotionBonus < ApplicationRecord
  self.table_name = 'sale_promotion_bonuses'
  
  belongs_to :sale
  belongs_to :promotion

  # Counter culture for promotion analytics
  counter_culture :promotion, column_name: "sales_count"
  counter_culture :promotion,
    column_name: "total_bonus_points",
    delta_column: "bonus_points"

  validates :sale_id, uniqueness: {scope: :promotion_id}
  validates :bonus_points, presence: true, numericality: {greater_than: 0}
end
