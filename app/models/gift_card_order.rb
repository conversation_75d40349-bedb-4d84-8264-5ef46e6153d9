# == Schema Information
#
# Table name: gift_card_orders
#
#  id                   :bigint           not null, primary key
#  amount               :decimal(10, 2)   not null
#  message              :text
#  offer_code           :string
#  recipient_city       :string           not null
#  recipient_company    :string
#  recipient_country    :string           not null
#  recipient_email      :string           not null
#  recipient_first_name :string           not null
#  recipient_last_name  :string           not null
#  recipient_phone      :string
#  recipient_state      :string           not null
#  recipient_street1    :string           not null
#  recipient_street2    :string
#  recipient_zip        :string           not null
#  status               :integer          default("pending"), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  gift_card_batch_id   :bigint
#  order_id             :bigint           not null
#  product_id           :bigint           not null
#
# Indexes
#
#  index_gift_card_orders_on_created_at          (created_at)
#  index_gift_card_orders_on_gift_card_batch_id  (gift_card_batch_id)
#  index_gift_card_orders_on_order_id            (order_id)
#  index_gift_card_orders_on_product_id          (product_id)
#  index_gift_card_orders_on_status              (status)
#
# Foreign Keys
#
#  fk_rails_...  (gift_card_batch_id => gift_card_batches.id)
#  fk_rails_...  (order_id => orders.id)
#  fk_rails_...  (product_id => products.id)
#

class GiftCardOrder < ApplicationRecord
  belongs_to :order
  belongs_to :product
  belongs_to :gift_card_batch, optional: true

  enum :status, {pending: 0, batched: 1, uploaded: 2, failed: 3}

  validates :amount, presence: true, numericality: {greater_than: 0}
  validates :recipient_first_name, :recipient_last_name, :recipient_street1,
    :recipient_city, :recipient_state, :recipient_zip,
    :recipient_country, :recipient_email, presence: true
  validates :recipient_email, format: {with: URI::MailTo::EMAIL_REGEXP}

  scope :for_date, ->(date) { where(created_at: date.beginning_of_day..date.end_of_day) }
  scope :unbatched, -> { where(gift_card_batch: nil) }

  # Convert to CSV row format
  def to_csv_row
    [
      recipient_first_name,
      recipient_last_name,
      recipient_company || "",
      recipient_street1,
      recipient_street2 || "",
      recipient_city,
      recipient_state,
      recipient_zip,
      recipient_country,
      recipient_phone || "",
      recipient_email,
      amount.to_f,
      offer_code || "",
      message || ""
    ]
  end

  # CSV headers for export
  def self.csv_headers
    %w[
      FirstName LastName Company Street1 Street2 City State Zip Country
      Phone Email Amount OfferCode Message
    ]
  end
end
