# == Schema Information
#
# Table name: store_chains
#
#  id                :bigint           not null, primary key
#  name              :string           not null
#  promotions_count  :integer          default(0), not null
#  stores_count      :integer          default(0), not null
#  total_users_count :integer          default(0), not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#
# Indexes
#
#  index_store_chains_on_name               (name) UNIQUE
#  index_store_chains_on_stores_count       (stores_count)
#  index_store_chains_on_total_users_count  (total_users_count)
#
class StoreChain < ApplicationRecord
  has_many :stores, dependent: :nullify
  has_many :promotions, dependent: :destroy

  validates :name, presence: true, uniqueness: true
end
