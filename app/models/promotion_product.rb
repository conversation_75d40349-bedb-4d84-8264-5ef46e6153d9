# == Schema Information
#
# Table name: promotion_products
#
#  id           :bigint           not null, primary key
#  promotion_id :bigint           not null
#  product_id   :bigint           not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_promotion_products_on_promotion_id_and_product_id  (promotion_id,product_id) UNIQUE
#  index_promotion_products_on_promotion_id                 (promotion_id)
#  index_promotion_products_on_product_id                   (product_id)
#
# Foreign Keys
#
#  fk_rails_...  (promotion_id => promotions.id)
#  fk_rails_...  (product_id => products.id)
#

class PromotionProduct < ApplicationRecord
  belongs_to :promotion
  belongs_to :product

  # Counter culture for promotion analytics
  counter_culture :promotion, column_name: "promotion_products_count"

  # Counter culture for product analytics
  counter_culture :product, column_name: "promotion_products_count"

  validates :promotion_id, uniqueness: {scope: :product_id}
end
