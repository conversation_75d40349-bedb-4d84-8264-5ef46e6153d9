# == Schema Information
#
# Table name: promotions
#
#  id                       :bigint           not null, primary key
#  bonus_multiplier         :decimal(3, 2)
#  bonus_points             :integer          not null
#  description              :text
#  end_date                 :datetime         not null
#  name                     :string           not null
#  promotion_products_count :integer          default(0), not null
#  sales_count              :integer          default(0), not null
#  start_date               :datetime         not null
#  status                   :integer          default("active"), not null
#  total_bonus_points       :integer          default(0), not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  region_id                :bigint
#  store_chain_id           :bigint
#  store_id                 :bigint
#
# Indexes
#
#  index_promotions_on_end_date                 (end_date)
#  index_promotions_on_region_id                (region_id)
#  index_promotions_on_sales_count              (sales_count)
#  index_promotions_on_start_date               (start_date)
#  index_promotions_on_start_date_and_end_date  (start_date,end_date)
#  index_promotions_on_status                   (status)
#  index_promotions_on_store_chain_id           (store_chain_id)
#  index_promotions_on_store_id                 (store_id)
#  index_promotions_on_total_bonus_points       (total_bonus_points)
#
# Foreign Keys
#
#  fk_rails_...  (region_id => regions.id)
#  fk_rails_...  (store_chain_id => store_chains.id)
#  fk_rails_...  (store_id => stores.id)
#

class Promotion < ApplicationRecord
  belongs_to :store, optional: true
  belongs_to :region, optional: true
  belongs_to :store_chain, optional: true

  has_many :promotion_products, dependent: :destroy
  has_many :products, through: :promotion_products

  # Counter culture for store analytics
  counter_culture :store, column_name: "promotions_count"

  # Counter culture for region analytics
  counter_culture :region, column_name: "promotions_count"

  # Counter culture for store chain analytics
  counter_culture :store_chain, column_name: "promotions_count"

  enum :status, {active: 0, inactive: 1, expired: 2}

  validates :name, presence: true
  validates :bonus_points, presence: true, numericality: {greater_than_or_equal_to: 0}
  validates :bonus_multiplier, numericality: {greater_than: 0.0}, allow_nil: true
  validates :start_date, :end_date, presence: true
  validates :end_date, comparison: {greater_than: :start_date}

  validate :must_have_scope
  validate :must_have_bonus_type
  validate :cannot_overlap_dates_for_same_scope_and_products

  scope :current, -> { where("start_date <= ? AND end_date >= ?", Time.current, Time.current) }
  scope :for_store, ->(store) { where(store: store) }
  scope :for_region, ->(region) { where(region: region) }
  scope :for_store_chain, ->(store_chain) { where(store_chain: store_chain) }

  # Find applicable promotions for a sale
  def self.applicable_for_sale(sale)
    store = sale.user.store
    return none unless store

    # Filter by scope (store, region, or chain)
    scope_conditions = []
    scope_conditions << where(store: store)
    scope_conditions << where(store_chain: store.store_chain) if store.store_chain
    scope_conditions << where(region: store.address&.state&.region) if store.address&.state&.region

    applicable_promotions = scope_conditions.reduce(none) { |result, condition| result.or(condition) }

    # Filter by products and ensure promotions are current and active
    applicable_promotions.current.active.joins(:products).where(products: {id: sale.product_id}).distinct
  end

  # Calculate bonus points for a sale
  def calculate_bonus_points(base_points)
    if bonus_multiplier.present?
      (base_points * bonus_multiplier).to_i - base_points
    else
      bonus_points
    end
  end

  def scope_description
    return "Store: #{store.name}" if store
    return "Region: #{region.name}" if region
    return "Chain: #{store_chain.name}" if store_chain
    "No scope"
  end

  def active_period
    "#{start_date.strftime("%m/%d/%Y")} - #{end_date.strftime("%m/%d/%Y")}"
  end

  # Ransack configuration for admin search
  def self.ransackable_attributes(auth_object = nil)
    %w[name description bonus_points bonus_multiplier start_date end_date status created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[store region store_chain products]
  end

  private

  def must_have_scope
    if [store, region, store_chain].all?(&:blank?)
      errors.add(:base, "Promotion must apply to a store, region, or store chain")
    end

    scope_count = [store, region, store_chain].count(&:present?)
    if scope_count > 1
      errors.add(:base, "Promotion can only apply to one scope (store, region, or store chain)")
    end
  end

  def must_have_bonus_type
    if bonus_points.blank? && bonus_multiplier.blank?
      errors.add(:base, "Promotion must have either bonus points or bonus multiplier")
    end

    if bonus_points.present? && bonus_multiplier.present?
      errors.add(:base, "Promotion cannot have both bonus points and bonus multiplier")
    end
  end

  def cannot_overlap_dates_for_same_scope_and_products
    return unless start_date && end_date

    # Build scope conditions
    scope_condition = if store
      {store: store}
    elsif region
      {region: region}
    elsif store_chain
      {store_chain: store_chain}
    else
      return
    end

    # Check for overlapping promotions with same scope
    overlapping = self.class.where(scope_condition)
      .where.not(id: id)
      .where("start_date < ? AND end_date > ?", end_date, start_date)
      .active

    return unless overlapping.exists?

    # Check if any overlapping promotions share products
    if products.any?
      shared_products = overlapping.joins(:products).where(products: {id: product_ids})
      if shared_products.exists?
        errors.add(:base, "Promotion dates overlap with existing promotion for the same scope and products")
      end
    end
  end
end
