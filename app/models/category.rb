# == Schema Information
#
# Table name: categories
#
#  id                    :bigint           not null, primary key
#  active_products_count :integer          default(0), not null
#  name                  :string           not null
#  products_count        :integer          default(0), not null
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  brand_id              :bigint           not null
#
# Indexes
#
#  index_categories_on_active_products_count  (active_products_count)
#  index_categories_on_brand_id               (brand_id)
#  index_categories_on_name_and_brand_id      (name,brand_id) UNIQUE
#  index_categories_on_products_count         (products_count)
#
# Foreign Keys
#
#  fk_rails_...  (brand_id => brands.id)
#
class Category < ApplicationRecord
  belongs_to :brand
  has_many :products, dependent: :restrict_with_exception

  # Counter culture for brand analytics
  counter_culture :brand, column_name: "categories_count"

  validates :name, presence: true, uniqueness: {scope: :brand_id}

  # Ransack configuration for search functionality
  def self.ransackable_attributes(auth_object = nil)
    ["brand_id", "created_at", "id", "name", "updated_at"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["brand", "products"]
  end
end
