# == Schema Information
#
# Table name: line_items
#
#  id         :bigint           not null, primary key
#  quantity   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  cart_id    :bigint
#  order_id   :bigint
#  product_id :bigint           not null
#
# Indexes
#
#  index_line_items_on_cart_id     (cart_id)
#  index_line_items_on_order_id    (order_id)
#  index_line_items_on_product_id  (product_id)
#
# Foreign Keys
#
#  fk_rails_...  (cart_id => carts.id)
#  fk_rails_...  (order_id => orders.id)
#  fk_rails_...  (product_id => products.id)
#
class LineItem < ApplicationRecord
  belongs_to :cart, optional: true
  belongs_to :order, optional: true
  belongs_to :product

  validates :quantity, numericality: {greater_than: 0}

  # Counter culture for maintaining order totals
  counter_culture :order,
    column_name: proc { |model| model.order_id? ? "total_points_cache" : nil },
    delta_column: "total_points"

  counter_culture :order, column_name: "line_items_count"

  # Counter culture for product analytics
  counter_culture :product, column_name: "line_items_count"
  counter_culture :product,
    column_name: proc { |model| model.order_id? ? "orders_count" : nil }
  counter_culture :product,
    column_name: "total_quantity_sold",
    delta_column: "quantity"

  def total_price
    # In points-based system, price is always 0
    0.0
  end

  def total_points
    return 0 unless product
    # Use points_cost from product country data or fallback to 0
    points_per_item = product.product_country_data.first&.points_cost || 0
    points_per_item * quantity
  end

  # For backward compatibility - line items don't have price in points system
  def price
    0.0
  end

  def price=(value)
    # Ignore price setting in points-based system
  end
end
