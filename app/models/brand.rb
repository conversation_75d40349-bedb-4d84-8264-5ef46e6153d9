# == Schema Information
#
# Table name: brands
#
#  id               :bigint           not null, primary key
#  categories_count :integer          default(0), not null
#  name             :string           not null
#  products_count   :integer          default(0), not null
#  stores_count     :integer          default(0), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#
# Indexes
#
#  index_brands_on_categories_count  (categories_count)
#  index_brands_on_name              (name) UNIQUE
#  index_brands_on_products_count    (products_count)
#  index_brands_on_stores_count      (stores_count)
#
class Brand < ApplicationRecord
  has_many :categories, dependent: :restrict_with_exception
  has_many :stores, dependent: :restrict_with_exception

  validates :name, presence: true, uniqueness: true

  # Ransack configuration for search functionality
  def self.ransackable_attributes(auth_object = nil)
    ["created_at", "id", "name", "updated_at"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["categories", "stores"]
  end
end
