# == Schema Information
#
# Table name: support_tickets
#
#  id             :bigint           not null, primary key
#  admin_response :text
#  category       :string           not null
#  message        :text             not null
#  priority       :integer          default("normal"), not null
#  responded_at   :datetime
#  status         :integer          default("open"), not null
#  subject        :string           not null
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  admin_user_id  :bigint
#  user_id        :bigint           not null
#
# Indexes
#
#  index_support_tickets_on_admin_user_id  (admin_user_id)
#  index_support_tickets_on_category       (category)
#  index_support_tickets_on_created_at     (created_at)
#  index_support_tickets_on_priority       (priority)
#  index_support_tickets_on_status         (status)
#  index_support_tickets_on_user_id        (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => users.id)
#  fk_rails_...  (user_id => users.id)
#
class SupportTicket < ApplicationRecord
  belongs_to :user
  belongs_to :admin_user, class_name: "User", optional: true

  validates :subject, presence: true, length: {minimum: 5, maximum: 200}
  validates :message, presence: true, length: {minimum: 10}
  validates :category, presence: true
  validates :status, presence: true

  enum :status, {open: 0, in_progress: 1, resolved: 2, closed: 3}
  enum :priority, {low: 0, normal: 1, high: 2, urgent: 3}

  CATEGORIES = {
    "sales" => "Sales & Points",
    "orders" => "Orders & Redemptions",
    "account" => "Account & Profile",
    "technical" => "Technical Issue",
    "general" => "General Question",
    "bug" => "App Bug Report",
    "feature" => "Feature Request"
  }.freeze

  ADMIN_CATEGORIES = %w[sales orders account].freeze
  SELF_SERVICE_CATEGORIES = %w[technical general bug feature].freeze

  scope :admin_handled, -> { where(category: ADMIN_CATEGORIES) }
  scope :self_service, -> { where(category: SELF_SERVICE_CATEGORIES) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_status, ->(status) { where(status: status) }
  scope :by_priority, ->(priority) { where(priority: priority) }

  def admin_handled?
    category.in?(ADMIN_CATEGORIES)
  end

  def self_service?
    category.in?(SELF_SERVICE_CATEGORIES)
  end

  def category_name
    CATEGORIES[category]
  end

  def status_color
    case status
    when "open" then "bg-yellow-100 text-yellow-800"
    when "in_progress" then "bg-blue-100 text-blue-800"
    when "resolved" then "bg-green-100 text-green-800"
    when "closed" then "bg-gray-100 text-gray-800"
    end
  end

  def priority_color
    case priority
    when "low" then "bg-gray-100 text-gray-800"
    when "normal" then "bg-blue-100 text-blue-800"
    when "high" then "bg-orange-100 text-orange-800"
    when "urgent" then "bg-red-100 text-red-800"
    end
  end
end
