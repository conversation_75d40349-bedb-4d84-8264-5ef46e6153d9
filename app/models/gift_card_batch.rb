# == Schema Information
#
# Table name: gift_card_batches
#
#  id           :bigint           not null, primary key
#  batch_date   :date             not null
#  filename     :string           not null
#  processed_at :datetime
#  status       :integer          default("pending"), not null
#  total_amount :decimal(12, 2)   default(0.0), not null
#  total_orders :integer          default(0), not null
#  upload_error :text
#  uploaded_at  :datetime
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_gift_card_batches_on_batch_date  (batch_date) UNIQUE
#  index_gift_card_batches_on_filename    (filename)
#  index_gift_card_batches_on_status      (status)
#

class GiftCardBatch < ApplicationRecord
  has_many :gift_card_orders, dependent: :restrict_with_exception
  has_one_attached :csv_file

  enum :status, {pending: 0, generated: 1, uploaded: 2, failed: 3}

  validates :batch_date, presence: true, uniqueness: true
  validates :filename, presence: true
  validates :total_orders, :total_amount, presence: true, numericality: {greater_than_or_equal_to: 0}

  scope :for_date, ->(date) { where(batch_date: date) }

  # Generate filename with timestamp
  def self.generate_filename(timestamp = Time.current)
    "WWR-ZS3PG-#{timestamp.strftime("%Y%m%d_%H%M%S")}.csv"
  end

  # Create or find batch for a specific date
  def self.for_date_or_create(date = Date.current)
    find_or_create_by(batch_date: date) do |batch|
      batch.filename = generate_filename
    end
  end

  # Generate CSV content for this batch
  def generate_csv
    require "csv"

    CSV.generate(headers: true) do |csv|
      csv << GiftCardOrder.csv_headers

      gift_card_orders.each do |order|
        csv << order.to_csv_row
      end
    end
  end

  # Process all unbatched gift card orders for this date
  def process_orders!
    date_orders = GiftCardOrder.for_date(batch_date).unbatched.includes(:order, :product)

    return if date_orders.empty?

    transaction do
      # Update orders to belong to this batch
      date_orders.update_all(
        gift_card_batch_id: id,
        status: :batched,
        updated_at: Time.current
      )

      # Update batch totals
      update!(
        total_orders: date_orders.count,
        total_amount: date_orders.sum(:amount),
        status: :generated
      )

      # Generate and attach CSV
      csv_content = generate_csv
      csv_file.attach(
        io: StringIO.new(csv_content),
        filename: filename,
        content_type: "text/csv"
      )
    end

    self
  end

  # Upload CSV via SFTP
  def upload_to_sftp!
    return false unless csv_file.attached?

    begin
      GiftCardSftpService.new.upload_file(csv_file, filename)

      update!(
        status: :uploaded,
        uploaded_at: Time.current,
        upload_error: nil
      )

      # Mark all orders as uploaded
      gift_card_orders.update_all(status: :uploaded, updated_at: Time.current)

      true
    rescue => e
      update!(
        status: :failed,
        upload_error: e.message
      )

      # Mark orders as failed
      gift_card_orders.update_all(status: :failed, updated_at: Time.current)

      false
    end
  end

  # Retry failed upload
  def retry_upload!
    return false unless failed?

    update!(status: :generated, upload_error: nil)
    upload_to_sftp!
  end

  # Ransack configuration for search functionality
  def self.ransackable_attributes(auth_object = nil)
    ["batch_date", "filename", "total_orders", "total_amount", "status", "uploaded_at", "created_at", "updated_at"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["gift_card_orders"]
  end
end
