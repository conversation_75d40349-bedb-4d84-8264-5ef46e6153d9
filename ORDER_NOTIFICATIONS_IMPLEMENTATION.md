# Order Notifications and Emails Implementation

## Overview

Implemented comprehensive email notifications and push notifications for all order status changes in the Zeiss Points system.

## Components Created

### 1. Database Migration

- **File**: `db/migrate/20250115000009_add_status_timestamps_to_orders.rb`
- **Purpose**: Adds timestamp and user tracking fields for order status changes
- **Fields Added**:
  - `approved_at`, `approved_by`
  - `rejected_at`, `rejected_by`
  - `shipped_at`, `shipped_by`
  - `delivered_at`

### 2. Order Mailer

- **File**: `app/mailers/order_mailer.rb`
- **Methods**:
  - `new_order_submitted` - For admin notifications
  - `order_approved` - For user notifications
  - `order_rejected` - For user notifications
  - `order_shipped` - For user notifications
  - `order_delivered` - For user notifications

### 3. Email Templates (MJML)

All templates follow the same design patterns as sale emails with rounded borders:

- `app/views/order_mailer/new_order_submitted.html.mjml`
- `app/views/order_mailer/order_approved.html.mjml`
- `app/views/order_mailer/order_rejected.html.mjml`
- `app/views/order_mailer/order_shipped.html.mjml`
- `app/views/order_mailer/order_delivered.html.mjml`

### 4. Notifiers (Noticed Gem)

All notifiers support both email and push notifications with user preference checks:

- `app/notifiers/new_order_notifier.rb` - Notifies admins of new orders
- `app/notifiers/order_approved_notifier.rb` - Notifies users of approval
- `app/notifiers/order_rejected_notifier.rb` - Notifies users of rejection
- `app/notifiers/order_shipped_notifier.rb` - Notifies users of shipping
- `app/notifiers/order_delivered_notifier.rb` - Notifies users of delivery

### 5. Controller Integration

Updated controllers to trigger notifications:

- **OrdersController**: Sends `NewOrderNotifier` when orders are created
- **Admin::OrdersController**: Sends appropriate notifications for all status changes
  - Approve action → `OrderApprovedNotifier`
  - Reject action → `OrderRejectedNotifier` (with optional reason)
  - Ship action → `OrderShippedNotifier`
  - Deliver action → `OrderDeliveredNotifier`

### 6. Testing

- **Specs**: `spec/mailers/order_mailer_spec.rb` - Complete test coverage
- **Previews**: `spec/mailers/previews/order_mailer_preview.rb` - Email previews for all states

## Notification Features

### Email Notifications

- **Responsive Design**: Mobile-friendly MJML templates
- **Rich Content**: Order details, line items, points information
- **Status-Specific Styling**: Different colors and icons for each status
- **User Preferences**: Respects user's `order_notifications` setting

### Push Notifications

- **Real-time**: Immediate notifications for status changes
- **Quiet Hours**: Respects user's quiet hours settings
- **User Preferences**: Respects user's push notification settings

### Admin Features

- **New Order Alerts**: Admins get notified immediately when orders are placed
- **Order Management**: All status change actions trigger appropriate notifications
- **Optional Rejection Reasons**: Can include reasons when rejecting orders

## Email Content

### New Order Submitted (Admin)

- Order summary with customer info
- Line items breakdown
- Customer points balance
- Direct link to review order

### Order Approved (User)

- Points deduction confirmation
- SAP order ID
- Order timeline
- Link to view orders

### Order Rejected (User)

- Rejection reason (if provided)
- Order details
- Link to browse products

### Order Shipped (User)

- Shipping information
- SAP order ID
- Expected delivery info
- Order tracking link

### Order Delivered (User)

- Delivery confirmation
- Order summary
- Thank you message
- Link to browse more products

## Usage Examples

```ruby
# When order is created
NewOrderNotifier.with(record: @order).deliver_later

# When order is approved
OrderApprovedNotifier.with(record: @order, approved_by: current_user).deliver(@order.user)

# When order is rejected (with reason)
OrderRejectedNotifier.with(
  record: @order,
  rejected_by: current_user,
  reason: "Insufficient points"
).deliver(@order.user)

# When order is shipped
OrderShippedNotifier.with(record: @order, shipped_by: current_user).deliver(@order.user)

# When order is delivered
OrderDeliveredNotifier.with(record: @order).deliver(@order.user)
```

## Next Steps

1. **Run Migration**: Execute `rails db:migrate` to add the new timestamp fields
2. **Test Email Previews**: Visit `/rails/mailers/order_mailer` to preview all emails
3. **Configure VAPID Keys**: Ensure web push credentials are set for push notifications
4. **Test Notification Flow**: Place test orders and verify notifications work end-to-end

## Integration Notes

- Uses existing notification settings (`order_notifications`)
- Follows same patterns as sale notifications
- Integrates with existing quiet hours functionality
- Supports both immediate and background job delivery
- Maintains consistency with existing email design system
