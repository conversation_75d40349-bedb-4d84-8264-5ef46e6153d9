Rails.application.routes.draw do
  # Devise routes (public for registration/login)
  devise_for :users, controllers: {registrations: "users/registrations"}

  # Admin namespace (requires authentication)
  authenticated :user, lambda { |u| u.admin? } do
    namespace :admin do
      root "dashboard#index"

      resources :sales do
        member do
          post :approve
          post :reject
        end
      end

      resources :users do
        member do
          post :activate
          post :deactivate
          post :credit_wallet
          post :debit_wallet
        end
      end

      resources :stores do
        member do
          post :approve
          post :activate
          post :deactivate
          patch :update_chain
        end
      end

      resources :orders do
        member do
          post :approve
          post :reject
          post :ship
          post :deliver
        end
      end

      resources :promotions do
        member do
          post :activate
          post :deactivate
        end
      end

      resources :promotion_products, only: [:index]

      resources :gift_card_batches, only: [:index, :show] do
        member do
          get :download
          post :retry_upload
          post :process_batch
        end

        collection do
          post :process_today
          post :test_sftp
        end
      end

      resources :products do
        member do
          post :activate
          post :deactivate
        end

        collection do
          patch :bulk_update
        end
      end

      resources :support_tickets, only: [:index, :show, :update] do
        member do
          post :respond
        end
      end
    end
  end

  # Root path: authenticated users to dashboard, unauthenticated to landing
  authenticated :user do
    root to: "dashboard#show", as: :authenticated_root
  end

  # Store search and selection (public for registration flow)
  get "stores/search", to: "stores#search", as: :search_stores
  post "stores/select/:id", to: "stores#select", as: :select_store
  post "stores/create_store", to: "stores#create_store", as: :create_store_stores

  # Authenticated routes
  get "dashboard", to: "dashboard#show", as: :dashboard
  resource :profile, only: [:show, :edit, :update]

  # Help and Support
  get "help", to: "help#index", as: :help_index
  get "help/contact", to: "help#contact", as: :help_contact
  post "help/contact", to: "help#submit_question", as: :help_submit_question
  resources :products, only: [:index, :show]
  resources :orders, only: [:index, :new, :create]
  resource :cart, only: [:show, :destroy]
  resources :line_items, only: [:create, :update, :destroy]
  resources :sales, only: [:new, :create] do
    collection do
      get :points
    end
  end

  # Push notification subscriptions
  resources :push_subscriptions, only: [:create, :destroy, :index] do
    collection do
      post :cleanup
    end
    member do
      post :test
    end
  end

  # Health check (public)
  get "up", to: "rails/health#show", as: :rails_health_check

  # PWA manifest and service worker (public)
  get "manifest", to: "rails/pwa#manifest", as: :pwa_manifest
  get "service-worker", to: "rails/pwa#service_worker", as: :pwa_service_worker

  # Test routes (development only)
  if Rails.env.development?
    get "test/barcode" => "application#test_barcode", :as => :test_barcode
    get "test/lucide" => "application#lucide_test", :as => :lucide_test
  end

  root to: "landing#index"
end
