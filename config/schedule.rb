# Use this file to easily define all of your cron jobs.
#
# It's helpful, but not entirely necessary to understand cron before proceeding.
# http://en.wikipedia.org/wiki/Cron

# Example:
#
# set :output, "/path/to/my/cron_log.log"
#
# every 2.hours do
#   command "/usr/bin/some_great_command"
#   runner "MyModel.some_method"
#   rake "some:great:rake:task"
# end
#
# every 4.days do
#   runner "AnotherModel.prune_old_records"
# end

# Learn more: http://github.com/javan/whenever

# Process gift card batches daily at 6 PM
every 1.day, at: "6:00 pm" do
  rake "gift_cards:process_daily"
end

# Retry failed uploads every 4 hours during business hours
every 4.hours, at: ["8:00 am", "12:00 pm", "4:00 pm"] do
  rake "gift_cards:retry_failed"
end
