# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_16_151923) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "activities", force: :cascade do |t|
    t.string "trackable_type"
    t.bigint "trackable_id"
    t.string "owner_type"
    t.bigint "owner_id"
    t.string "key"
    t.text "parameters"
    t.string "recipient_type"
    t.bigint "recipient_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["owner_id", "owner_type"], name: "index_activities_on_owner_id_and_owner_type"
    t.index ["owner_type", "owner_id"], name: "index_activities_on_owner"
    t.index ["recipient_id", "recipient_type"], name: "index_activities_on_recipient_id_and_recipient_type"
    t.index ["recipient_type", "recipient_id"], name: "index_activities_on_recipient"
    t.index ["trackable_id", "trackable_type"], name: "index_activities_on_trackable_id_and_trackable_type"
    t.index ["trackable_type", "trackable_id"], name: "index_activities_on_trackable"
  end

  create_table "addresses", force: :cascade do |t|
    t.string "street", null: false
    t.string "city", null: false
    t.string "postal_code", null: false
    t.string "addressable_type", null: false
    t.bigint "addressable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "state_id"
    t.bigint "country_id", null: false
    t.index ["addressable_type", "addressable_id"], name: "index_addresses_on_addressable"
    t.index ["addressable_type", "addressable_id"], name: "index_addresses_on_addressable_type_and_addressable_id"
    t.index ["country_id"], name: "index_addresses_on_country_id"
    t.index ["state_id"], name: "index_addresses_on_state_id"
  end

  create_table "ahoy_events", force: :cascade do |t|
    t.bigint "visit_id"
    t.bigint "user_id"
    t.string "name"
    t.jsonb "properties"
    t.datetime "time"
    t.index ["name", "time"], name: "index_ahoy_events_on_name_and_time"
    t.index ["properties"], name: "index_ahoy_events_on_properties", opclass: :jsonb_path_ops, using: :gin
    t.index ["user_id"], name: "index_ahoy_events_on_user_id"
    t.index ["visit_id"], name: "index_ahoy_events_on_visit_id"
  end

  create_table "ahoy_visits", force: :cascade do |t|
    t.string "visit_token"
    t.string "visitor_token"
    t.bigint "user_id"
    t.string "ip"
    t.text "user_agent"
    t.text "referrer"
    t.string "referring_domain"
    t.text "landing_page"
    t.string "browser"
    t.string "os"
    t.string "device_type"
    t.string "country"
    t.string "region"
    t.string "city"
    t.float "latitude"
    t.float "longitude"
    t.string "utm_source"
    t.string "utm_medium"
    t.string "utm_term"
    t.string "utm_content"
    t.string "utm_campaign"
    t.string "app_version"
    t.string "os_version"
    t.string "platform"
    t.datetime "started_at"
    t.index ["user_id"], name: "index_ahoy_visits_on_user_id"
    t.index ["visit_token"], name: "index_ahoy_visits_on_visit_token", unique: true
    t.index ["visitor_token", "started_at"], name: "index_ahoy_visits_on_visitor_token_and_started_at"
  end

  create_table "brands", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "stores_count", default: 0, null: false
    t.integer "categories_count", default: 0, null: false
    t.integer "products_count", default: 0, null: false
    t.index ["categories_count"], name: "index_brands_on_categories_count"
    t.index ["name"], name: "index_brands_on_name", unique: true
    t.index ["products_count"], name: "index_brands_on_products_count"
    t.index ["stores_count"], name: "index_brands_on_stores_count"
  end

  create_table "carts", force: :cascade do |t|
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_carts_on_user_id"
  end

  create_table "categories", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "brand_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "products_count", default: 0, null: false
    t.integer "active_products_count", default: 0, null: false
    t.index ["active_products_count"], name: "index_categories_on_active_products_count"
    t.index ["brand_id"], name: "index_categories_on_brand_id"
    t.index ["name", "brand_id"], name: "index_categories_on_name_and_brand_id", unique: true
    t.index ["products_count"], name: "index_categories_on_products_count"
  end

  create_table "countries", force: :cascade do |t|
    t.string "code", limit: 2, null: false
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["code"], name: "index_countries_on_code", unique: true
    t.index ["name"], name: "index_countries_on_name", unique: true
  end

  create_table "gift_card_batches", force: :cascade do |t|
    t.date "batch_date", null: false
    t.string "filename", null: false
    t.integer "total_orders", default: 0, null: false
    t.decimal "total_amount", precision: 12, scale: 2, default: "0.0", null: false
    t.integer "status", default: 0, null: false
    t.datetime "uploaded_at"
    t.text "upload_error"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "processed_at"
    t.index ["batch_date"], name: "index_gift_card_batches_on_batch_date", unique: true
    t.index ["filename"], name: "index_gift_card_batches_on_filename"
    t.index ["status"], name: "index_gift_card_batches_on_status"
  end

  create_table "gift_card_orders", force: :cascade do |t|
    t.bigint "order_id", null: false
    t.bigint "product_id", null: false
    t.decimal "amount", precision: 10, scale: 2, null: false
    t.string "recipient_first_name", null: false
    t.string "recipient_last_name", null: false
    t.string "recipient_company"
    t.string "recipient_street1", null: false
    t.string "recipient_street2"
    t.string "recipient_city", null: false
    t.string "recipient_state", null: false
    t.string "recipient_zip", null: false
    t.string "recipient_country", null: false
    t.string "recipient_phone"
    t.string "recipient_email", null: false
    t.string "offer_code"
    t.text "message"
    t.integer "status", default: 0, null: false
    t.bigint "gift_card_batch_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_gift_card_orders_on_created_at"
    t.index ["gift_card_batch_id"], name: "index_gift_card_orders_on_gift_card_batch_id"
    t.index ["order_id"], name: "index_gift_card_orders_on_order_id"
    t.index ["product_id"], name: "index_gift_card_orders_on_product_id"
    t.index ["status"], name: "index_gift_card_orders_on_status"
  end

  create_table "line_items", force: :cascade do |t|
    t.bigint "cart_id"
    t.bigint "order_id"
    t.bigint "product_id", null: false
    t.integer "quantity", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cart_id"], name: "index_line_items_on_cart_id"
    t.index ["order_id"], name: "index_line_items_on_order_id"
    t.index ["product_id"], name: "index_line_items_on_product_id"
  end

  create_table "noticed_events", force: :cascade do |t|
    t.string "type"
    t.string "record_type"
    t.bigint "record_id"
    t.jsonb "params"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "notifications_count"
    t.index ["record_type", "record_id"], name: "index_noticed_events_on_record"
  end

  create_table "noticed_notifications", force: :cascade do |t|
    t.string "type"
    t.bigint "event_id", null: false
    t.string "recipient_type", null: false
    t.bigint "recipient_id", null: false
    t.datetime "read_at", precision: nil
    t.datetime "seen_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["event_id"], name: "index_noticed_notifications_on_event_id"
    t.index ["recipient_type", "recipient_id"], name: "index_noticed_notifications_on_recipient"
  end

  create_table "orders", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "status", default: 0, null: false
    t.string "shipping_type", null: false
    t.string "shipping_address"
    t.integer "points", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "sap_id"
    t.datetime "sap_processed_at"
    t.datetime "approved_at"
    t.bigint "approved_by"
    t.datetime "rejected_at"
    t.bigint "rejected_by"
    t.datetime "shipped_at"
    t.bigint "shipped_by"
    t.datetime "delivered_at"
    t.integer "line_items_count", default: 0, null: false
    t.integer "total_points_cache", default: 0, null: false
    t.index ["approved_by"], name: "index_orders_on_approved_by"
    t.index ["line_items_count"], name: "index_orders_on_line_items_count"
    t.index ["rejected_by"], name: "index_orders_on_rejected_by"
    t.index ["sap_id"], name: "index_orders_on_sap_id", unique: true
    t.index ["shipped_by"], name: "index_orders_on_shipped_by"
    t.index ["total_points_cache"], name: "index_orders_on_total_points_cache"
    t.index ["user_id"], name: "index_orders_on_user_id"
  end

  create_table "product_country_data", force: :cascade do |t|
    t.bigint "product_id", null: false
    t.decimal "msrp", precision: 10, scale: 2
    t.integer "points_earned"
    t.integer "points_cost"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "country_id", null: false
    t.index ["country_id"], name: "index_product_country_data_on_country_id"
    t.index ["product_id", "country_id"], name: "index_product_country_data_on_product_id_and_country_id", unique: true
    t.index ["product_id"], name: "index_product_country_data_on_product_id"
  end

  create_table "products", force: :cascade do |t|
    t.string "name", null: false
    t.string "sku", null: false
    t.string "upc", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "category_id", null: false
    t.integer "product_type", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.integer "sales_count", default: 0, null: false
    t.integer "orders_count", default: 0, null: false
    t.integer "line_items_count", default: 0, null: false
    t.integer "total_points_earned", default: 0, null: false
    t.integer "total_quantity_sold", default: 0, null: false
    t.integer "promotion_products_count", default: 0, null: false
    t.index ["category_id"], name: "index_products_on_category_id"
    t.index ["line_items_count"], name: "index_products_on_line_items_count"
    t.index ["orders_count"], name: "index_products_on_orders_count"
    t.index ["product_type"], name: "index_products_on_product_type"
    t.index ["sales_count"], name: "index_products_on_sales_count"
    t.index ["sku"], name: "index_products_on_sku", unique: true
    t.index ["total_points_earned"], name: "index_products_on_total_points_earned"
    t.index ["total_quantity_sold"], name: "index_products_on_total_quantity_sold"
    t.index ["upc"], name: "index_products_on_upc", unique: true
  end

  create_table "promotion_products", force: :cascade do |t|
    t.bigint "promotion_id", null: false
    t.bigint "product_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_promotion_products_on_product_id"
    t.index ["promotion_id", "product_id"], name: "index_promotion_products_on_promotion_id_and_product_id", unique: true
    t.index ["promotion_id"], name: "index_promotion_products_on_promotion_id"
  end

  create_table "promotions", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.integer "bonus_points", null: false
    t.decimal "bonus_multiplier", precision: 3, scale: 2
    t.datetime "start_date", null: false
    t.datetime "end_date", null: false
    t.integer "status", default: 0, null: false
    t.bigint "store_id"
    t.bigint "region_id"
    t.bigint "store_chain_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "sales_count", default: 0, null: false
    t.integer "total_bonus_points", default: 0, null: false
    t.integer "promotion_products_count", default: 0, null: false
    t.index ["end_date"], name: "index_promotions_on_end_date"
    t.index ["region_id"], name: "index_promotions_on_region_id"
    t.index ["sales_count"], name: "index_promotions_on_sales_count"
    t.index ["start_date", "end_date"], name: "index_promotions_on_start_date_and_end_date"
    t.index ["start_date"], name: "index_promotions_on_start_date"
    t.index ["status"], name: "index_promotions_on_status"
    t.index ["store_chain_id"], name: "index_promotions_on_store_chain_id"
    t.index ["store_id"], name: "index_promotions_on_store_id"
    t.index ["total_bonus_points"], name: "index_promotions_on_total_bonus_points"
  end

  create_table "push_notification_subscriptions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.text "endpoint", null: false
    t.string "p256dh_key", null: false
    t.string "auth_key", null: false
    t.text "user_agent"
    t.string "device_name"
    t.boolean "active", default: true, null: false
    t.datetime "last_used_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_push_notification_subscriptions_on_active"
    t.index ["last_used_at"], name: "index_push_notification_subscriptions_on_last_used_at"
    t.index ["user_id", "endpoint"], name: "index_push_subscriptions_on_user_and_endpoint", unique: true
    t.index ["user_id"], name: "index_push_notification_subscriptions_on_user_id"
  end

  create_table "regions", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "admin_user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "states_count", default: 0, null: false
    t.integer "promotions_count", default: 0, null: false
    t.index ["admin_user_id"], name: "index_regions_on_admin_user_id"
    t.index ["name"], name: "index_regions_on_name", unique: true
    t.index ["states_count"], name: "index_regions_on_states_count"
  end

  create_table "sale_promotion_bonuses", force: :cascade do |t|
    t.bigint "sale_id", null: false
    t.bigint "promotion_id", null: false
    t.integer "bonus_points", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["promotion_id"], name: "index_sale_promotion_bonuses_on_promotion_id"
    t.index ["sale_id", "promotion_id"], name: "index_sale_promotion_bonuses_on_sale_id_and_promotion_id", unique: true
    t.index ["sale_id"], name: "index_sale_promotion_bonuses_on_sale_id"
  end

  create_table "sales", force: :cascade do |t|
    t.string "serial_number", null: false
    t.bigint "user_id", null: false
    t.bigint "product_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "sold_at", null: false
    t.integer "points", null: false
    t.text "notes"
    t.datetime "approved_at"
    t.integer "approved_by"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approved_by"], name: "index_sales_on_approved_by"
    t.index ["product_id"], name: "index_sales_on_product_id"
    t.index ["serial_number"], name: "index_sales_on_serial_number", unique: true
    t.index ["user_id"], name: "index_sales_on_user_id"
  end

  create_table "states", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "region_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["region_id", "name"], name: "index_states_on_region_id_and_name", unique: true
    t.index ["region_id"], name: "index_states_on_region_id"
  end

  create_table "store_chains", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "stores_count", default: 0, null: false
    t.integer "total_users_count", default: 0, null: false
    t.integer "promotions_count", default: 0, null: false
    t.index ["name"], name: "index_store_chains_on_name", unique: true
    t.index ["stores_count"], name: "index_store_chains_on_stores_count"
    t.index ["total_users_count"], name: "index_store_chains_on_total_users_count"
  end

  create_table "stores", force: :cascade do |t|
    t.string "name", null: false
    t.string "phone_number", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "store_chain_id"
    t.bigint "brand_id"
    t.integer "users_count", default: 0, null: false
    t.integer "sales_count", default: 0, null: false
    t.integer "approved_sales_count", default: 0, null: false
    t.integer "pending_sales_count", default: 0, null: false
    t.integer "total_sales_points", default: 0, null: false
    t.integer "orders_count", default: 0, null: false
    t.integer "promotions_count", default: 0, null: false
    t.index ["approved_sales_count"], name: "index_stores_on_approved_sales_count"
    t.index ["brand_id"], name: "index_stores_on_brand_id"
    t.index ["orders_count"], name: "index_stores_on_orders_count"
    t.index ["sales_count"], name: "index_stores_on_sales_count"
    t.index ["status"], name: "index_stores_on_status"
    t.index ["store_chain_id"], name: "index_stores_on_store_chain_id"
    t.index ["total_sales_points"], name: "index_stores_on_total_sales_points"
    t.index ["users_count"], name: "index_stores_on_users_count"
  end

  create_table "support_tickets", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "admin_user_id"
    t.string "subject", null: false
    t.text "message", null: false
    t.string "category", null: false
    t.integer "status", default: 0, null: false
    t.integer "priority", default: 1, null: false
    t.text "admin_response"
    t.datetime "responded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_support_tickets_on_admin_user_id"
    t.index ["category"], name: "index_support_tickets_on_category"
    t.index ["created_at"], name: "index_support_tickets_on_created_at"
    t.index ["priority"], name: "index_support_tickets_on_priority"
    t.index ["status"], name: "index_support_tickets_on_status"
    t.index ["user_id"], name: "index_support_tickets_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.integer "failed_attempts", default: 0, null: false
    t.string "unlock_token"
    t.datetime "locked_at"
    t.integer "role", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "store_id"
    t.integer "status", default: 1, null: false
    t.jsonb "notification_settings", default: {}
    t.integer "sales_count", default: 0, null: false
    t.integer "approved_sales_count", default: 0, null: false
    t.integer "pending_sales_count", default: 0, null: false
    t.integer "orders_count", default: 0, null: false
    t.integer "total_points_earned", default: 0, null: false
    t.string "first_name", default: "Zeiss", null: false
    t.string "last_name", default: "User", null: false
    t.index ["approved_sales_count"], name: "index_users_on_approved_sales_count"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["notification_settings"], name: "index_users_on_notification_settings", using: :gin
    t.index ["orders_count"], name: "index_users_on_orders_count"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["sales_count"], name: "index_users_on_sales_count"
    t.index ["status"], name: "index_users_on_status"
    t.index ["store_id"], name: "index_users_on_store_id"
    t.index ["total_points_earned"], name: "index_users_on_total_points_earned"
    t.index ["unlock_token"], name: "index_users_on_unlock_token", unique: true
  end

  create_table "wallets", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "points", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_wallets_on_user_id", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "addresses", "countries"
  add_foreign_key "addresses", "states"
  add_foreign_key "carts", "users"
  add_foreign_key "categories", "brands"
  add_foreign_key "gift_card_orders", "gift_card_batches"
  add_foreign_key "gift_card_orders", "orders"
  add_foreign_key "gift_card_orders", "products"
  add_foreign_key "line_items", "carts"
  add_foreign_key "line_items", "orders"
  add_foreign_key "line_items", "products"
  add_foreign_key "orders", "users"
  add_foreign_key "product_country_data", "countries"
  add_foreign_key "product_country_data", "products"
  add_foreign_key "products", "categories"
  add_foreign_key "promotion_products", "products"
  add_foreign_key "promotion_products", "promotions"
  add_foreign_key "promotions", "regions"
  add_foreign_key "promotions", "store_chains"
  add_foreign_key "promotions", "stores"
  add_foreign_key "push_notification_subscriptions", "users"
  add_foreign_key "regions", "users", column: "admin_user_id"
  add_foreign_key "sale_promotion_bonuses", "promotions"
  add_foreign_key "sale_promotion_bonuses", "sales"
  add_foreign_key "sales", "products"
  add_foreign_key "sales", "users"
  add_foreign_key "states", "regions"
  add_foreign_key "stores", "brands"
  add_foreign_key "stores", "store_chains"
  add_foreign_key "support_tickets", "users"
  add_foreign_key "support_tickets", "users", column: "admin_user_id"
  add_foreign_key "users", "stores"
  add_foreign_key "wallets", "users"
end
