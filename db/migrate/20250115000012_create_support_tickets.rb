class CreateSupportTickets < ActiveRecord::Migration[8.0]
  def change
    create_table :support_tickets do |t|
      t.references :user, null: false, foreign_key: true
      t.references :admin_user, null: true, foreign_key: { to_table: :users }
      t.string :subject, null: false
      t.text :message, null: false
      t.string :category, null: false
      t.integer :status, default: 0, null: false
      t.integer :priority, default: 1, null: false
      t.text :admin_response
      t.datetime :responded_at
      t.timestamps
    end

    add_index :support_tickets, :status
    add_index :support_tickets, :category
    add_index :support_tickets, :priority
    add_index :support_tickets, :created_at
  end
end