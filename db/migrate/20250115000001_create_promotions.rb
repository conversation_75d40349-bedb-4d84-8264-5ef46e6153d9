class CreatePromotions < ActiveRecord::Migration[8.0]
  def change
    create_table :promotions do |t|
      t.string :name, null: false
      t.text :description
      t.integer :bonus_points, null: false
      t.decimal :bonus_multiplier, precision: 3, scale: 2
      t.datetime :start_date, null: false
      t.datetime :end_date, null: false
      t.integer :status, default: 0, null: false
      
      # Scope - can apply to store, region, or chain
      t.references :store, null: true, foreign_key: true
      t.references :region, null: true, foreign_key: true
      t.references :store_chain, null: true, foreign_key: true
      
      t.timestamps
    end

    add_index :promotions, :status
    add_index :promotions, :start_date
    add_index :promotions, :end_date
    add_index :promotions, [:start_date, :end_date]
  end
end