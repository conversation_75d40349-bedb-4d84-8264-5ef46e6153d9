class CreateGiftCardBatches < ActiveRecord::Migration[8.0]
  def change
    create_table :gift_card_batches do |t|
      t.date :batch_date, null: false
      t.string :filename, null: false
      t.integer :total_orders, default: 0, null: false
      t.decimal :total_amount, precision: 12, scale: 2, default: 0.0, null: false
      t.integer :status, default: 0, null: false
      t.datetime :uploaded_at
      t.text :upload_error

      t.timestamps
    end

    add_index :gift_card_batches, :batch_date, unique: true
    add_index :gift_card_batches, :status
    add_index :gift_card_batches, :filename
  end
end
