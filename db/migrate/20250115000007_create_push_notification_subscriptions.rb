class CreatePushNotificationSubscriptions < ActiveRecord::Migration[8.0]
  def change
    create_table :push_notification_subscriptions do |t|
      t.references :user, null: false, foreign_key: true
      t.text :endpoint, null: false
      t.string :p256dh_key, null: false
      t.string :auth_key, null: false
      t.text :user_agent
      t.string :device_name
      t.boolean :active, default: true, null: false
      t.datetime :last_used_at
      t.timestamps
    end

    add_index :push_notification_subscriptions, [:user_id, :endpoint], unique: true, name: 'index_push_subscriptions_on_user_and_endpoint'
    add_index :push_notification_subscriptions, :active
    add_index :push_notification_subscriptions, :last_used_at
  end
end