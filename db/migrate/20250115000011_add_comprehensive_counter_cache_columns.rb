class AddComprehensiveCounterCacheColumns < ActiveRecord::Migration[8.0]
  def change
    # === STORES COUNTERS ===
    # Track users, sales, and points per store
    add_column :stores, :users_count, :integer, default: 0, null: false
    add_column :stores, :sales_count, :integer, default: 0, null: false
    add_column :stores, :approved_sales_count, :integer, default: 0, null: false
    add_column :stores, :pending_sales_count, :integer, default: 0, null: false
    add_column :stores, :total_sales_points, :integer, default: 0, null: false
    add_column :stores, :orders_count, :integer, default: 0, null: false
    add_column :stores, :promotions_count, :integer, default: 0, null: false

    # === USERS COUNTERS ===
    # Track user activity and engagement
    add_column :users, :sales_count, :integer, default: 0, null: false
    add_column :users, :approved_sales_count, :integer, default: 0, null: false
    add_column :users, :pending_sales_count, :integer, default: 0, null: false
    add_column :users, :orders_count, :integer, default: 0, null: false
    add_column :users, :total_points_earned, :integer, default: 0, null: false

    # === PRODUCTS COUNTERS ===
    # Track product popularity and performance
    add_column :products, :sales_count, :integer, default: 0, null: false
    add_column :products, :orders_count, :integer, default: 0, null: false
    add_column :products, :line_items_count, :integer, default: 0, null: false
    add_column :products, :total_points_earned, :integer, default: 0, null: false
    add_column :products, :total_quantity_sold, :integer, default: 0, null: false
    add_column :products, :promotion_products_count, :integer, default: 0, null: false

    # === CATEGORIES COUNTERS ===
    # Track products per category
    add_column :categories, :products_count, :integer, default: 0, null: false
    add_column :categories, :active_products_count, :integer, default: 0, null: false

    # === BRANDS COUNTERS ===
    # Track brand portfolio and performance
    add_column :brands, :stores_count, :integer, default: 0, null: false
    add_column :brands, :categories_count, :integer, default: 0, null: false
    add_column :brands, :products_count, :integer, default: 0, null: false

    # === STORE CHAINS COUNTERS ===
    # Track chain-level metrics
    add_column :store_chains, :stores_count, :integer, default: 0, null: false
    add_column :store_chains, :total_users_count, :integer, default: 0, null: false
    add_column :store_chains, :promotions_count, :integer, default: 0, null: false

    # === REGIONS COUNTERS ===
    # Track regional metrics
    add_column :regions, :states_count, :integer, default: 0, null: false
    add_column :regions, :promotions_count, :integer, default: 0, null: false

    # === PROMOTIONS COUNTERS ===
    # Track promotion effectiveness
    add_column :promotions, :sales_count, :integer, default: 0, null: false
    add_column :promotions, :total_bonus_points, :integer, default: 0, null: false
    add_column :promotions, :promotion_products_count, :integer, default: 0, null: false

    # === PERFORMANCE INDEXES ===
    # Add indexes for frequently queried counter columns

    # Store indexes
    add_index :stores, :users_count
    add_index :stores, :sales_count
    add_index :stores, :approved_sales_count
    add_index :stores, :total_sales_points
    add_index :stores, :orders_count

    # User indexes
    add_index :users, :sales_count
    add_index :users, :approved_sales_count
    add_index :users, :orders_count
    add_index :users, :total_points_earned

    # Product indexes
    add_index :products, :sales_count
    add_index :products, :orders_count
    add_index :products, :line_items_count
    add_index :products, :total_points_earned
    add_index :products, :total_quantity_sold

    # Category indexes
    add_index :categories, :products_count
    add_index :categories, :active_products_count

    # Brand indexes
    add_index :brands, :stores_count
    add_index :brands, :categories_count
    add_index :brands, :products_count

    # Store chain indexes
    add_index :store_chains, :stores_count
    add_index :store_chains, :total_users_count

    # Region indexes
    add_index :regions, :states_count

    # Promotion indexes
    add_index :promotions, :sales_count
    add_index :promotions, :total_bonus_points
  end
end
