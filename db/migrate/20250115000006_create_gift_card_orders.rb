class CreateGiftCardOrders < ActiveRecord::Migration[8.0]
  def change
    create_table :gift_card_orders do |t|
      t.references :order, null: false, foreign_key: true
      t.references :product, null: false, foreign_key: true
      t.decimal :amount, precision: 10, scale: 2, null: false
      t.string :recipient_first_name, null: false
      t.string :recipient_last_name, null: false
      t.string :recipient_company
      t.string :recipient_street1, null: false
      t.string :recipient_street2
      t.string :recipient_city, null: false
      t.string :recipient_state, null: false
      t.string :recipient_zip, null: false
      t.string :recipient_country, null: false
      t.string :recipient_phone
      t.string :recipient_email, null: false
      t.string :offer_code
      t.text :message
      t.integer :status, default: 0, null: false
      t.references :gift_card_batch, null: true, foreign_key: true

      t.timestamps
    end

    add_index :gift_card_orders, :status
    add_index :gift_card_orders, :created_at
  end
end
