class CreateSalePromotionBonuses < ActiveRecord::Migration[8.0]
  def change
    create_table :sale_promotion_bonuses do |t|
      t.references :sale, null: false, foreign_key: true
      t.references :promotion, null: false, foreign_key: true
      t.integer :bonus_points, null: false
      
      t.timestamps
    end

    add_index :sale_promotion_bonuses, [:sale_id, :promotion_id], unique: true
  end
end