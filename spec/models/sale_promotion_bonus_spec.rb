# frozen_string_literal: true

require "rails_helper"

RSpec.describe SalePromotionBonus, type: :model do
  describe "table name" do
    it "uses correct table name" do
      expect(described_class.table_name).to eq("sale_promotion_bonuses")
    end
  end

  describe "associations" do
    it { should belong_to(:sale) }
    it { should belong_to(:promotion) }
  end

  describe "validations" do
    let(:sale) { create(:sale) }
    let(:promotion) { create(:promotion) }

    it { should validate_presence_of(:bonus_points) }
    it { should validate_numericality_of(:bonus_points).is_greater_than(0) }

    describe "uniqueness validation" do
      let!(:existing_bonus) do
        create(:sale_promotion_bonus, sale: sale, promotion: promotion)
      end

      it "validates uniqueness of sale_id scoped to promotion_id" do
        duplicate_bonus = build(:sale_promotion_bonus, sale: sale, promotion: promotion)
        expect(duplicate_bonus).not_to be_valid
        expect(duplicate_bonus.errors[:sale_id]).to include("has already been taken")
      end

      it "allows same sale with different promotion" do
        other_promotion = create(:promotion)
        bonus = build(:sale_promotion_bonus, sale: sale, promotion: other_promotion)
        expect(bonus).to be_valid
      end

      it "allows same promotion with different sale" do
        other_sale = create(:sale)
        bonus = build(:sale_promotion_bonus, sale: other_sale, promotion: promotion)
        expect(bonus).to be_valid
      end
    end
  end

  describe "counter culture" do
    let(:promotion) { create(:promotion) }
    let(:sale) { create(:sale) }

    it "increments promotion sales_count when created" do
      expect do
        create(:sale_promotion_bonus, sale: sale, promotion: promotion, bonus_points: 100)
      end.to change { promotion.reload.sales_count }.by(1)
    end

    it "decrements promotion sales_count when destroyed" do
      bonus = create(:sale_promotion_bonus, sale: sale, promotion: promotion, bonus_points: 100)

      expect do
        bonus.destroy
      end.to change { promotion.reload.sales_count }.by(-1)
    end

    it "updates promotion total_bonus_points when created" do
      expect do
        create(:sale_promotion_bonus, sale: sale, promotion: promotion, bonus_points: 150)
      end.to change { promotion.reload.total_bonus_points }.by(150)
    end

    it "updates promotion total_bonus_points when destroyed" do
      bonus = create(:sale_promotion_bonus, sale: sale, promotion: promotion, bonus_points: 150)

      expect do
        bonus.destroy
      end.to change { promotion.reload.total_bonus_points }.by(-150)
    end

    it "updates promotion total_bonus_points when bonus_points changed" do
      bonus = create(:sale_promotion_bonus, sale: sale, promotion: promotion, bonus_points: 100)

      expect do
        bonus.update!(bonus_points: 200)
      end.to change { promotion.reload.total_bonus_points }.by(100) # +200 - 100
    end
  end

  describe "factory" do
    it "creates valid sale promotion bonus" do
      bonus = create(:sale_promotion_bonus)
      expect(bonus).to be_valid
    end

    it "has required associations" do
      bonus = create(:sale_promotion_bonus)
      expect(bonus.sale).to be_present
      expect(bonus.promotion).to be_present
    end

    it "has positive bonus points" do
      bonus = create(:sale_promotion_bonus)
      expect(bonus.bonus_points).to be > 0
    end
  end

  describe "business logic" do
    it "represents bonus points earned from a promotion on a sale" do
      sale = create(:sale)
      promotion = create(:promotion, bonus_points: 50)
      bonus = create(:sale_promotion_bonus,
        sale: sale,
        promotion: promotion,
        bonus_points: promotion.bonus_points)

      expect(bonus.sale).to eq(sale)
      expect(bonus.promotion).to eq(promotion)
      expect(bonus.bonus_points).to eq(50)
    end
  end
end
