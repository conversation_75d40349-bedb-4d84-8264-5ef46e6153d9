# frozen_string_literal: true

# == Schema Information
#
# Table name: users
#
#  id                     :bigint           not null, primary key
#  approved_sales_count   :integer          default(0), not null
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  email                  :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  failed_attempts        :integer          default(0), not null
#  first_name             :string           default("<PERSON>eiss"), not null
#  last_name              :string           default("User"), not null
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  locked_at              :datetime
#  notification_settings  :jsonb
#  orders_count           :integer          default(0), not null
#  pending_sales_count    :integer          default(0), not null
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  role                   :integer          default("regular"), not null
#  sales_count            :integer          default(0), not null
#  sign_in_count          :integer          default(0), not null
#  status                 :integer          default("inactive"), not null
#  total_points_earned    :integer          default(0), not null
#  unconfirmed_email      :string
#  unlock_token           :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  store_id               :bigint
#
# Indexes
#
#  index_users_on_approved_sales_count   (approved_sales_count)
#  index_users_on_confirmation_token     (confirmation_token) UNIQUE
#  index_users_on_email                  (email) UNIQUE
#  index_users_on_notification_settings  (notification_settings) USING gin
#  index_users_on_orders_count           (orders_count)
#  index_users_on_reset_password_token   (reset_password_token) UNIQUE
#  index_users_on_sales_count            (sales_count)
#  index_users_on_status                 (status)
#  index_users_on_store_id               (store_id)
#  index_users_on_total_points_earned    (total_points_earned)
#  index_users_on_unlock_token           (unlock_token) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (store_id => stores.id)
#
require "rails_helper"

RSpec.describe User, type: :model do
  subject(:user) { build(:user) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:email) }
    it { is_expected.to validate_uniqueness_of(:email).case_insensitive }
    it { is_expected.to validate_presence_of(:role) }
  end

  describe "associations" do
    it { is_expected.to have_one(:wallet).dependent(:destroy) }
    it { should have_many(:regions).with_foreign_key("admin_user_id").dependent(:nullify) }
  end

  describe "devise modules" do
    it "includes database_authenticatable" do
      expect(User.devise_modules).to include(:database_authenticatable)
    end
    it "includes registerable" do
      expect(User.devise_modules).to include(:registerable)
    end
    it "includes recoverable" do
      expect(User.devise_modules).to include(:recoverable)
    end
    it "includes rememberable" do
      expect(User.devise_modules).to include(:rememberable)
    end
    it "includes validatable" do
      expect(User.devise_modules).to include(:validatable)
    end
    it "includes trackable" do
      expect(User.devise_modules).to include(:trackable)
    end
    it "includes confirmable" do
      expect(User.devise_modules).to include(:confirmable)
    end
    it "includes lockable" do
      expect(User.devise_modules).to include(:lockable)
    end
  end

  describe "enums" do
    it { is_expected.to define_enum_for(:role).with_values(regular: 0, admin: 1, super_admin: 2).backed_by_column_of_type(:integer) }
    it "defaults to regular" do
      expect(user.role).to eq("regular")
    end
  end

  describe "wallet creation" do
    it "creates a wallet after user is created" do
      store = create(:store)
      user = build(:user, store: store)
      expect { user.save! }.to change { Wallet.count }.by(1)
      expect(user.wallet).to be_present
    end
  end

  describe "email uniqueness" do
    before { create(:user, email: "<EMAIL>") }
    it "does not allow duplicate emails" do
      user.email = "<EMAIL>"
      expect(user).not_to be_valid
      expect(user.errors[:email]).to include("has already been taken")
    end
  end

  describe "dependent destroy" do
    it "destroys associated wallet when user is destroyed" do
      store = create(:store)
      user = build(:user, store: store)
      user.save!
      wallet = user.wallet
      expect { user.destroy }.to change { Wallet.count }.by(-1)
      expect { wallet.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end
end
