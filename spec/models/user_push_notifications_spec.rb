require 'rails_helper'

RSpec.describe User, 'push notifications', type: :model do
  let(:user) { create(:user) }

  describe '#active_push_subscriptions' do
    it 'returns only active subscriptions' do
      active_sub = create(:push_notification_subscription, user: user, active: true)
      inactive_sub = create(:push_notification_subscription, user: user, active: false)

      expect(user.active_push_subscriptions).to include(active_sub)
      expect(user.active_push_subscriptions).not_to include(inactive_sub)
    end
  end

  describe '#has_push_subscriptions?' do
    it 'returns true when user has active subscriptions' do
      create(:push_notification_subscription, user: user, active: true)
      expect(user.has_push_subscriptions?).to be true
    end

    it 'returns false when user has no active subscriptions' do
      create(:push_notification_subscription, user: user, active: false)
      expect(user.has_push_subscriptions?).to be false
    end

    it 'returns false when user has no subscriptions' do
      expect(user.has_push_subscriptions?).to be false
    end
  end

  describe '#add_push_subscription' do
    let(:subscription_params) do
      {
        endpoint: 'https://fcm.googleapis.com/fcm/send/test123',
        p256dh_key: 'test_p256dh_key',
        auth_key: 'test_auth_key',
        user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0)',
        device_name: 'iPhone 15'
      }
    end

    it 'creates a new subscription' do
      expect {
        user.add_push_subscription(**subscription_params)
      }.to change { user.push_notification_subscriptions.count }.by(1)
    end

    it 'sets the subscription as active with current timestamp' do
      subscription = user.add_push_subscription(**subscription_params)
      
      expect(subscription).to be_active
      expect(subscription.last_used_at).to be_within(1.second).of(Time.current)
    end

    it 'finds existing subscription by endpoint instead of creating duplicate' do
      existing_subscription = create(:push_notification_subscription, 
        user: user, 
        endpoint: subscription_params[:endpoint]
      )

      expect {
        user.add_push_subscription(**subscription_params)
      }.not_to change { user.push_notification_subscriptions.count }
    end

    it 'updates existing subscription when found by endpoint' do
      existing_subscription = create(:push_notification_subscription, 
        user: user, 
        endpoint: subscription_params[:endpoint],
        device_name: 'Old Device'
      )

      user.add_push_subscription(**subscription_params)
      existing_subscription.reload

      expect(existing_subscription.device_name).to eq('iPhone 15')
      expect(existing_subscription.p256dh_key).to eq('test_p256dh_key')
    end
  end

  describe '#remove_push_subscription' do
    it 'removes subscription by endpoint' do
      subscription = create(:push_notification_subscription, user: user)
      
      expect {
        user.remove_push_subscription(subscription.endpoint)
      }.to change { user.push_notification_subscriptions.count }.by(-1)
    end

    it 'returns nil when subscription not found' do
      result = user.remove_push_subscription('non-existent-endpoint')
      expect(result).to be_nil
    end
  end

  describe '#cleanup_stale_subscriptions!' do
    it 'removes subscriptions older than 30 days' do
      old_subscription = create(:push_notification_subscription, 
        user: user, 
        last_used_at: 31.days.ago
      )
      recent_subscription = create(:push_notification_subscription, 
        user: user, 
        last_used_at: 29.days.ago
      )

      expect {
        user.cleanup_stale_subscriptions!
      }.to change { user.push_notification_subscriptions.count }.by(-1)

      expect(user.push_notification_subscriptions).to include(recent_subscription)
      expect(user.push_notification_subscriptions).not_to include(old_subscription)
    end

    it 'removes subscriptions with nil last_used_at' do
      nil_subscription = create(:push_notification_subscription, 
        user: user, 
        last_used_at: nil
      )

      expect {
        user.cleanup_stale_subscriptions!
      }.to change { user.push_notification_subscriptions.count }.by(-1)
    end
  end
end