# == Schema Information
#
# Table name: line_items
#
#  id         :bigint           not null, primary key
#  quantity   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  cart_id    :bigint
#  order_id   :bigint
#  product_id :bigint           not null
#
# Indexes
#
#  index_line_items_on_cart_id     (cart_id)
#  index_line_items_on_order_id    (order_id)
#  index_line_items_on_product_id  (product_id)
#
# Foreign Keys
#
#  fk_rails_...  (cart_id => carts.id)
#  fk_rails_...  (order_id => orders.id)
#  fk_rails_...  (product_id => products.id)
#
require "rails_helper"

RSpec.describe LineItem, type: :model do
  subject(:line_item) { build(:line_item) }

  describe "associations" do
    it { is_expected.to belong_to(:cart).optional(true) }
    it { is_expected.to belong_to(:order).optional(true) }
    it { is_expected.to belong_to(:product) }
  end

  describe "validations" do
    it { is_expected.to validate_numericality_of(:quantity).is_greater_than(0) }

    it "is valid with valid attributes" do
      expect(line_item).to be_valid
    end

    it "is invalid with zero quantity" do
      line_item.quantity = 0
      expect(line_item).not_to be_valid
      expect(line_item.errors[:quantity]).to be_present
    end

    it "is invalid with negative quantity" do
      line_item.quantity = -1
      expect(line_item).not_to be_valid
      expect(line_item.errors[:quantity]).to be_present
    end

    it "is valid with positive quantity" do
      line_item.quantity = 5
      expect(line_item).to be_valid
    end
  end

  describe "#total_price" do
    it "always returns 0 in points-based system" do
      line_item = build(:line_item, :in_cart, quantity: 3)
      expect(line_item.total_price).to eq(0.0)
    end
  end

  describe "#total_points" do
    let(:country) { create(:country, code: "US", name: "United States") }
    let(:product) { create(:product) }
    let!(:product_country_data) { create(:product_country_datum, product: product, country: country, points_cost: 50) }
    let(:line_item) { build(:line_item, :in_cart, product: product, quantity: 3) }

    it "calculates total points correctly" do
      expect(line_item.total_points).to eq(50 * 3)
    end

    context "with quantity of 1" do
      let(:line_item) { build(:line_item, :in_cart, product: product, quantity: 1) }

      it "returns the points cost" do
        expect(line_item.total_points).to eq(50)
      end
    end

    context "with zero points cost" do
      let!(:product_country_data) { create(:product_country_datum, product: product, country: country, points_cost: 0) }
      let(:line_item) { build(:line_item, :in_cart, product: product, quantity: 2) }

      it "returns zero" do
        expect(line_item.total_points).to eq(0)
      end
    end

    context "when product has no country data" do
      let(:product_without_data) { create(:product) }
      let(:line_item) { build(:line_item, :in_cart, product: product_without_data, quantity: 2) }

      before do
        # Ensure no country data exists for this product
        product_without_data.product_country_data.destroy_all
      end

      it "returns zero" do
        expect(line_item.total_points).to eq(0)
      end
    end
  end

  describe "#price" do
    it "always returns 0.0 in points-based system" do
      line_item = build(:line_item, :in_cart)
      expect(line_item.price).to eq(0.0)
    end
  end

  describe "cart vs order association" do
    let(:cart) { create(:cart) }
    let(:order) { create(:order) }

    context "when associated with cart" do
      let(:line_item) { create(:line_item, :in_cart, cart: cart) }

      it "belongs to cart and not order" do
        expect(line_item.cart).to eq(cart)
        expect(line_item.order).to be_nil
      end
    end

    context "when associated with order" do
      let(:line_item) { create(:line_item, :in_order, order: order) }

      it "belongs to order and not cart" do
        expect(line_item.order).to eq(order)
        expect(line_item.cart).to be_nil
      end
    end

    context "when moving from cart to order" do
      let(:line_item) { create(:line_item, :in_cart, cart: cart) }

      it "can be moved from cart to order" do
        line_item.update!(order: order, cart: nil)
        line_item.reload

        expect(line_item.order).to eq(order)
        expect(line_item.cart).to be_nil
      end
    end
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:line_item)).to be_valid
    end

    it "has a valid in_cart trait" do
      line_item = build(:line_item, :in_cart)
      expect(line_item).to be_valid
      expect(line_item.cart).to be_present
      expect(line_item.order).to be_nil
    end

    it "has a valid in_order trait" do
      line_item = build(:line_item, :in_order)
      expect(line_item).to be_valid
      expect(line_item.order).to be_present
      expect(line_item.cart).to be_nil
    end

    it "creates line item with product country data for points" do
      line_item = build(:line_item)
      expect(line_item.product.product_country_data).not_to be_empty
    end

    it "has valid associations" do
      line_item = build(:line_item)
      expect(line_item.product).to be_present
      expect(line_item.cart).to be_present
    end
  end

  describe "points handling" do
    let(:country) { create(:country, code: "US", name: "United States") }
    let(:product) { create(:product) }

    before do
      create(:product_country_datum, product: product, country: country, points_cost: 100)
    end

    it "calculates points from product country data" do
      line_item = build(:line_item, :in_cart, product: product, quantity: 2)
      expect(line_item.total_points).to eq(200)
    end

    it "handles missing country data gracefully" do
      product_without_data = create(:product)
      # Ensure no country data exists
      product_without_data.product_country_data.destroy_all
      line_item = build(:line_item, :in_cart, product: product_without_data, quantity: 2)
      expect(line_item.total_points).to eq(0)
    end
  end
end
