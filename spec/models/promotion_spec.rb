# == Schema Information
#
# Table name: promotions
#
#  id                       :bigint           not null, primary key
#  bonus_multiplier         :decimal(3, 2)
#  bonus_points             :integer          not null
#  description              :text
#  end_date                 :datetime         not null
#  name                     :string           not null
#  promotion_products_count :integer          default(0), not null
#  sales_count              :integer          default(0), not null
#  start_date               :datetime         not null
#  status                   :integer          default("active"), not null
#  total_bonus_points       :integer          default(0), not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  region_id                :bigint
#  store_chain_id           :bigint
#  store_id                 :bigint
#
# Indexes
#
#  index_promotions_on_end_date                 (end_date)
#  index_promotions_on_region_id                (region_id)
#  index_promotions_on_sales_count              (sales_count)
#  index_promotions_on_start_date               (start_date)
#  index_promotions_on_start_date_and_end_date  (start_date,end_date)
#  index_promotions_on_status                   (status)
#  index_promotions_on_store_chain_id           (store_chain_id)
#  index_promotions_on_store_id                 (store_id)
#  index_promotions_on_total_bonus_points       (total_bonus_points)
#
# Foreign Keys
#
#  fk_rails_...  (region_id => regions.id)
#  fk_rails_...  (store_chain_id => store_chains.id)
#  fk_rails_...  (store_id => stores.id)
#
require 'rails_helper'

RSpec.describe Promotion, type: :model do
  let(:store) { create(:store) }
  let(:region) { create(:region) }
  let(:store_chain) { create(:store_chain) }
  let(:product) { create(:product) }

  describe 'validations' do
    it 'requires a name' do
      promotion = build(:promotion, name: nil)
      expect(promotion).not_to be_valid
      expect(promotion.errors[:name]).to include("can't be blank")
    end

    it 'requires bonus_points' do
      promotion = build(:promotion, bonus_points: nil)
      expect(promotion).not_to be_valid
      expect(promotion.errors[:bonus_points]).to include("can't be blank")
    end

    it 'requires start_date and end_date' do
      promotion = build(:promotion, start_date: nil, end_date: nil)
      expect(promotion).not_to be_valid
      expect(promotion.errors[:start_date]).to include("can't be blank")
      expect(promotion.errors[:end_date]).to include("can't be blank")
    end

    it 'requires end_date to be after start_date' do
      promotion = build(:promotion, 
                       start_date: 1.day.from_now, 
                       end_date: Time.current)
      expect(promotion).not_to be_valid
      expect(promotion.errors[:end_date]).to include("must be greater than start_date")
    end

    it 'requires at least one scope (store, region, or store_chain)' do
      promotion = build(:promotion, store: nil, region: nil, store_chain: nil)
      expect(promotion).not_to be_valid
      expect(promotion.errors[:base]).to include("Promotion must apply to a store, region, or store chain")
    end

    it 'allows only one scope' do
      promotion = build(:promotion, store: store, region: region)
      expect(promotion).not_to be_valid
      expect(promotion.errors[:base]).to include("Promotion can only apply to one scope (store, region, or store chain)")
    end

    it 'requires either bonus_points or bonus_multiplier' do
      promotion = build(:promotion, bonus_points: nil, bonus_multiplier: nil)
      expect(promotion).not_to be_valid
      expect(promotion.errors[:base]).to include("Promotion must have either bonus points or bonus multiplier")
    end

    it 'does not allow both bonus_points and bonus_multiplier' do
      promotion = build(:promotion, bonus_points: 100, bonus_multiplier: 1.5)
      expect(promotion).not_to be_valid
      expect(promotion.errors[:base]).to include("Promotion cannot have both bonus points and bonus multiplier")
    end
  end

  describe 'associations' do
    it { should belong_to(:store).optional }
    it { should belong_to(:region).optional }
    it { should belong_to(:store_chain).optional }
    it { should have_many(:promotion_products).dependent(:destroy) }
    it { should have_many(:products).through(:promotion_products) }
  end

  describe 'scopes' do
    let!(:current_promotion) { create(:promotion, start_date: 1.day.ago, end_date: 1.day.from_now) }
    let!(:future_promotion) { create(:promotion, start_date: 1.day.from_now, end_date: 2.days.from_now) }
    let!(:past_promotion) { create(:promotion, start_date: 3.days.ago, end_date: 2.days.ago) }

    describe '.current' do
      it 'returns promotions that are currently active' do
        expect(Promotion.current).to include(current_promotion)
        expect(Promotion.current).not_to include(future_promotion)
        expect(Promotion.current).not_to include(past_promotion)
      end
    end

    describe '.for_store' do
      let!(:store_promotion) { create(:promotion, store: store) }
      let!(:other_promotion) { create(:promotion, region: region) }

      it 'returns promotions for the specified store' do
        expect(Promotion.for_store(store)).to include(store_promotion)
        expect(Promotion.for_store(store)).not_to include(other_promotion)
      end
    end
  end

  describe '#calculate_bonus_points' do
    context 'with fixed bonus points' do
      let(:promotion) { create(:promotion, bonus_points: 50) }

      it 'returns the fixed bonus points' do
        expect(promotion.calculate_bonus_points(100)).to eq(50)
      end
    end

    context 'with bonus multiplier' do
      let(:promotion) { create(:promotion, bonus_points: nil, bonus_multiplier: 1.5) }

      it 'calculates bonus based on multiplier' do
        base_points = 100
        expected_bonus = (base_points * 1.5).to_i - base_points
        expect(promotion.calculate_bonus_points(base_points)).to eq(expected_bonus)
      end
    end
  end

  describe '#scope_description' do
    it 'returns store description when store is set' do
      promotion = create(:promotion, store: store)
      expect(promotion.scope_description).to eq("Store: #{store.name}")
    end

    it 'returns region description when region is set' do
      promotion = create(:promotion, region: region)
      expect(promotion.scope_description).to eq("Region: #{region.name}")
    end

    it 'returns chain description when store_chain is set' do
      promotion = create(:promotion, store_chain: store_chain)
      expect(promotion.scope_description).to eq("Chain: #{store_chain.name}")
    end
  end

  describe '.applicable_for_sale' do
    let(:user) { create(:user, store: store) }
    let(:sale) { create(:sale, user: user, product: product) }
    let!(:applicable_promotion) { create(:promotion, store: store, products: [product]) }
    let!(:different_store_promotion) { create(:promotion, store: create(:store), products: [product]) }
    let!(:different_product_promotion) { create(:promotion, store: store, products: [create(:product)]) }

    it 'returns promotions applicable to the sale' do
      applicable = Promotion.applicable_for_sale(sale)
      expect(applicable).to include(applicable_promotion)
      expect(applicable).not_to include(different_store_promotion)
      expect(applicable).not_to include(different_product_promotion)
    end
  end
end
