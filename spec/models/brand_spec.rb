# frozen_string_literal: true

# == Schema Information
#
# Table name: brands
#
#  id               :bigint           not null, primary key
#  categories_count :integer          default(0), not null
#  name             :string           not null
#  products_count   :integer          default(0), not null
#  stores_count     :integer          default(0), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#
# Indexes
#
#  index_brands_on_categories_count  (categories_count)
#  index_brands_on_name              (name) UNIQUE
#  index_brands_on_products_count    (products_count)
#  index_brands_on_stores_count      (stores_count)
#
require "rails_helper"

RSpec.describe Brand, type: :model do
  subject(:brand) { build(:brand) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name) }
  end

  describe "associations" do
    it { is_expected.to have_many(:categories).dependent(:restrict_with_exception) }
    it { is_expected.to have_many(:stores).dependent(:restrict_with_exception) }
  end
end
