# frozen_string_literal: true

# == Schema Information
#
# Table name: stores
#
#  id                   :bigint           not null, primary key
#  approved_sales_count :integer          default(0), not null
#  name                 :string           not null
#  orders_count         :integer          default(0), not null
#  pending_sales_count  :integer          default(0), not null
#  phone_number         :string           not null
#  promotions_count     :integer          default(0), not null
#  sales_count          :integer          default(0), not null
#  status               :integer          default("requested"), not null
#  total_sales_points   :integer          default(0), not null
#  users_count          :integer          default(0), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  brand_id             :bigint
#  store_chain_id       :bigint
#
# Indexes
#
#  index_stores_on_approved_sales_count  (approved_sales_count)
#  index_stores_on_brand_id              (brand_id)
#  index_stores_on_orders_count          (orders_count)
#  index_stores_on_sales_count           (sales_count)
#  index_stores_on_status                (status)
#  index_stores_on_store_chain_id        (store_chain_id)
#  index_stores_on_total_sales_points    (total_sales_points)
#  index_stores_on_users_count           (users_count)
#
# Foreign Keys
#
#  fk_rails_...  (brand_id => brands.id)
#  fk_rails_...  (store_chain_id => store_chains.id)
#
require "rails_helper"

RSpec.describe Store, type: :model do
  subject(:store) { build(:store) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:phone_number) }
    it { is_expected.to validate_presence_of(:status) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:store_chain).optional(true) }
    it { is_expected.to belong_to(:brand) }
    it { is_expected.to have_one(:address) }
    it { is_expected.to have_many(:users) }
  end

  describe "enums" do
    it { is_expected.to define_enum_for(:status).with_values(requested: 0, active: 1, inactive: 2) }
  end

  describe "Ransack configuration" do
    it "allows searching by name" do
      store1 = create(:store, name: "Zeiss Optical Store")
      store2 = create(:store, name: "Vision Center")

      search = Store.ransack(name_cont: "Zeiss")
      results = search.result

      expect(results).to include(store1)
      expect(results).not_to include(store2)
    end

    it "defines ransackable attributes" do
      expected_attributes = %w[name phone_number status created_at updated_at]
      expect(Store.ransackable_attributes).to include(*expected_attributes)
    end

    it "defines ransackable associations" do
      expected_associations = %w[brand address]
      expect(Store.ransackable_associations).to eq(expected_associations)
    end
  end
end
