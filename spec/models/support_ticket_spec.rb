require "rails_helper"

RSpec.describe SupportTicket, type: :model do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user) }

  describe "associations" do
    it { should belong_to(:user) }
    it { should belong_to(:admin_user).class_name("User").optional }
  end

  describe "validations" do
    it { should validate_presence_of(:subject) }
    it { should validate_length_of(:subject).is_at_least(5).is_at_most(200) }
    it { should validate_presence_of(:message) }
    it { should validate_length_of(:message).is_at_least(10) }
    it { should validate_presence_of(:category) }
    it { should validate_presence_of(:status) }
  end

  describe "enums" do
    it { should define_enum_for(:status).with_values(open: 0, in_progress: 1, resolved: 2, closed: 3) }
    it { should define_enum_for(:priority).with_values(low: 0, normal: 1, high: 2, urgent: 3) }
  end

  describe "scopes" do
    let!(:admin_ticket) { create(:support_ticket, user: user, category: "sales") }
    let!(:self_service_ticket) { create(:support_ticket, user: user, category: "technical") }
    let!(:open_ticket) { create(:support_ticket, user: user, status: "open") }
    let!(:resolved_ticket) { create(:support_ticket, user: user, status: "resolved") }

    describe ".admin_handled" do
      it "returns tickets with admin-handled categories" do
        expect(SupportTicket.admin_handled).to include(admin_ticket)
        expect(SupportTicket.admin_handled).not_to include(self_service_ticket)
      end
    end

    describe ".self_service" do
      it "returns tickets with self-service categories" do
        expect(SupportTicket.self_service).to include(self_service_ticket)
        expect(SupportTicket.self_service).not_to include(admin_ticket)
      end
    end

    describe ".recent" do
      it "orders tickets by created_at desc" do
        expect(SupportTicket.recent.first.created_at).to be >= SupportTicket.recent.last.created_at
      end
    end

    describe ".by_status" do
      it "filters tickets by status" do
        expect(SupportTicket.by_status("open")).to include(open_ticket)
        expect(SupportTicket.by_status("open")).not_to include(resolved_ticket)
      end
    end
  end

  describe "instance methods" do
    let(:support_ticket) { create(:support_ticket, user: user, category: "sales", status: "open", priority: "high") }

    describe "#admin_handled?" do
      it "returns true for admin-handled categories" do
        expect(support_ticket.admin_handled?).to be true
      end

      it "returns false for self-service categories" do
        support_ticket.update(category: "technical")
        expect(support_ticket.admin_handled?).to be false
      end
    end

    describe "#self_service?" do
      it "returns false for admin-handled categories" do
        expect(support_ticket.self_service?).to be false
      end

      it "returns true for self-service categories" do
        support_ticket.update(category: "technical")
        expect(support_ticket.self_service?).to be true
      end
    end

    describe "#category_name" do
      it "returns the human-readable category name" do
        expect(support_ticket.category_name).to eq("Sales & Points")
      end
    end

    describe "#status_color" do
      it "returns correct CSS classes for each status" do
        expect(support_ticket.status_color).to eq("bg-yellow-100 text-yellow-800")

        support_ticket.update(status: "in_progress")
        expect(support_ticket.status_color).to eq("bg-blue-100 text-blue-800")

        support_ticket.update(status: "resolved")
        expect(support_ticket.status_color).to eq("bg-green-100 text-green-800")

        support_ticket.update(status: "closed")
        expect(support_ticket.status_color).to eq("bg-gray-100 text-gray-800")
      end
    end

    describe "#priority_color" do
      it "returns correct CSS classes for each priority" do
        expect(support_ticket.priority_color).to eq("bg-orange-100 text-orange-800")

        support_ticket.update(priority: "low")
        expect(support_ticket.priority_color).to eq("bg-gray-100 text-gray-800")

        support_ticket.update(priority: "normal")
        expect(support_ticket.priority_color).to eq("bg-blue-100 text-blue-800")

        support_ticket.update(priority: "urgent")
        expect(support_ticket.priority_color).to eq("bg-red-100 text-red-800")
      end
    end
  end

  describe "constants" do
    it "defines correct categories" do
      expect(SupportTicket::CATEGORIES).to include(
        "sales" => "Sales & Points",
        "orders" => "Orders & Redemptions",
        "account" => "Account & Profile",
        "technical" => "Technical Issue",
        "general" => "General Question",
        "bug" => "App Bug Report",
        "feature" => "Feature Request"
      )
    end

    it "defines correct admin categories" do
      expect(SupportTicket::ADMIN_CATEGORIES).to eq(%w[sales orders account])
    end

    it "defines correct self-service categories" do
      expect(SupportTicket::SELF_SERVICE_CATEGORIES).to eq(%w[technical general bug feature])
    end
  end
end
