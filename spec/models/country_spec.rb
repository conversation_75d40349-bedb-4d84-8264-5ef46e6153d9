# frozen_string_literal: true

# == Schema Information
#
# Table name: countries
#
#  id         :bigint           not null, primary key
#  code       :string(2)        not null
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_countries_on_code  (code) UNIQUE
#  index_countries_on_name  (name) UNIQUE
#
require "rails_helper"

RSpec.describe Country, type: :model do
  describe "associations" do
    it { should have_many(:addresses) }
  end

  describe "validations" do
    subject { build(:country) }

    it { should validate_presence_of(:code) }
    it { should validate_presence_of(:name) }
    it { should validate_uniqueness_of(:code) }
    it { should validate_uniqueness_of(:name) }
    it { should validate_length_of(:code).is_equal_to(2) }
  end

  describe "code validation" do
    it "accepts valid 2-character country codes" do
      valid_codes = %w[US CA GB DE FR JP AU]

      valid_codes.each do |code|
        country = build(:country, code: code)
        expect(country).to be_valid, "#{code} should be valid"
      end
    end

    it "rejects codes that are not exactly 2 characters" do
      invalid_codes = ["U", "USA", "CANADA", ""]

      invalid_codes.each do |code|
        country = build(:country, code: code)
        expect(country).not_to be_valid, "#{code} should be invalid"
      end
    end
  end

  describe "uniqueness validations" do
    let!(:existing_country) { create(:country, code: "US", name: "United States") }

    it "prevents duplicate country codes" do
      duplicate_code_country = build(:country, code: "US", name: "United States of America")
      expect(duplicate_code_country).not_to be_valid
      expect(duplicate_code_country.errors[:code]).to include("has already been taken")
    end

    it "prevents duplicate country names" do
      duplicate_name_country = build(:country, code: "USA", name: "United States")
      expect(duplicate_name_country).not_to be_valid
      expect(duplicate_name_country.errors[:name]).to include("has already been taken")
    end

    it "allows different codes and names" do
      different_country = build(:country, code: "CA", name: "Canada")
      expect(different_country).to be_valid
    end
  end

  describe "factory" do
    it "creates valid country" do
      country = build(:country)
      expect(country).to be_valid
    end

    it "has required attributes" do
      country = create(:country)
      expect(country.code).to be_present
      expect(country.name).to be_present
      expect(country.code.length).to eq(2)
    end
  end

  describe "common country examples" do
    it "can create United States" do
      us = create(:country, code: "US", name: "United States")
      expect(us).to be_valid
      expect(us.code).to eq("US")
      expect(us.name).to eq("United States")
    end

    it "can create Canada" do
      canada = create(:country, code: "CA", name: "Canada")
      expect(canada).to be_valid
      expect(canada.code).to eq("CA")
      expect(canada.name).to eq("Canada")
    end

    it "can create Germany" do
      germany = create(:country, code: "DE", name: "Germany")
      expect(germany).to be_valid
      expect(germany.code).to eq("DE")
      expect(germany.name).to eq("Germany")
    end
  end

  describe "case sensitivity" do
    it "treats codes as case sensitive" do
      create(:country, code: "US", name: "United States")
      lowercase_country = build(:country, code: "us", name: "United States Lowercase")
      expect(lowercase_country).to be_valid
    end

    it "treats names as case sensitive" do
      create(:country, code: "US", name: "United States")
      different_case_country = build(:country, code: "UK", name: "united states")
      expect(different_case_country).to be_valid
    end
  end
end
