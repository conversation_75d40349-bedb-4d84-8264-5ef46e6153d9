# == Schema Information
#
# Table name: orders
#
#  id                 :bigint           not null, primary key
#  approved_at        :datetime
#  approved_by        :bigint
#  delivered_at       :datetime
#  line_items_count   :integer          default(0), not null
#  points             :integer          not null
#  rejected_at        :datetime
#  rejected_by        :bigint
#  sap_processed_at   :datetime
#  shipped_at         :datetime
#  shipped_by         :bigint
#  shipping_address   :string
#  shipping_type      :string           not null
#  status             :integer          default("pending"), not null
#  total_points_cache :integer          default(0), not null
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  sap_id             :string
#  user_id            :bigint           not null
#
# Indexes
#
#  index_orders_on_approved_by         (approved_by)
#  index_orders_on_line_items_count    (line_items_count)
#  index_orders_on_rejected_by         (rejected_by)
#  index_orders_on_sap_id              (sap_id) UNIQUE
#  index_orders_on_shipped_by          (shipped_by)
#  index_orders_on_total_points_cache  (total_points_cache)
#  index_orders_on_user_id             (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
require "rails_helper"

RSpec.describe Order, type: :model do
  subject(:order) { build(:order) }

  describe "associations" do
    it { is_expected.to belong_to(:user) }
    it { is_expected.to have_many(:line_items).dependent(:destroy) }
    it { is_expected.to have_many(:products).through(:line_items) }
  end

  describe "validations" do
    it { is_expected.to validate_presence_of(:shipping_type) }
    it { is_expected.to validate_presence_of(:points) }
    it { is_expected.to validate_numericality_of(:points).is_greater_than(0) }

    it "is valid with valid attributes" do
      expect(order).to be_valid
    end

    it "is invalid without shipping_type" do
      order.shipping_type = nil
      expect(order).not_to be_valid
      expect(order.errors[:shipping_type]).to be_present
    end

    it "is invalid without points" do
      order.points = nil
      expect(order).not_to be_valid
      expect(order.errors[:points]).to be_present
    end

    it "is invalid with zero points" do
      order.points = 0
      expect(order).not_to be_valid
      expect(order.errors[:points]).to be_present
    end

    it "is invalid with negative points" do
      order.points = -10
      expect(order).not_to be_valid
      expect(order.errors[:points]).to be_present
    end

    it "is valid with positive points" do
      order.points = 100
      expect(order).to be_valid
    end
  end

  describe "enums" do
    it { is_expected.to define_enum_for(:status).with_values(pending: 0, approved: 1, rejected: 2, shipped: 3, delivered: 4) }

    it "defaults to pending status" do
      new_order = Order.new
      expect(new_order.status).to eq("pending")
    end

    it "can be set to approved" do
      order.status = :approved
      expect(order.status).to eq("approved")
      expect(order.approved?).to be true
    end

    it "can be set to rejected" do
      order.status = :rejected
      expect(order.status).to eq("rejected")
      expect(order.rejected?).to be true
    end
  end

  describe "shipping types" do
    context "when shipping to user" do
      let(:order) { build(:order, :ship_to_user) }

      it "has user shipping type" do
        expect(order.shipping_type).to eq("user")
      end

      it "has shipping address" do
        expect(order.shipping_address).to be_present
      end
    end

    context "when shipping to store" do
      let(:order) { build(:order, :ship_to_store) }

      it "has store shipping type" do
        expect(order.shipping_type).to eq("store")
      end

      it "may not have shipping address" do
        expect(order.shipping_address).to be_nil
      end
    end
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:order)).to be_valid
    end

    it "has valid status traits" do
      expect(build(:order, :approved)).to be_valid
      expect(build(:order, :approved).status).to eq("approved")

      expect(build(:order, :rejected)).to be_valid
      expect(build(:order, :rejected).status).to eq("rejected")
    end

    it "has valid shipping traits" do
      ship_to_user = build(:order, :ship_to_user)
      expect(ship_to_user).to be_valid
      expect(ship_to_user.shipping_type).to eq("user")
      expect(ship_to_user.shipping_address).to be_present

      ship_to_store = build(:order, :ship_to_store)
      expect(ship_to_store).to be_valid
      expect(ship_to_store.shipping_type).to eq("store")
      expect(ship_to_store.shipping_address).to be_nil
    end

    it "has valid points traits" do
      high_points = build(:order, :high_points)
      expect(high_points).to be_valid
      expect(high_points.points).to eq(500)

      low_points = build(:order, :low_points)
      expect(low_points).to be_valid
      expect(low_points.points).to eq(10)
    end

    it "creates order with line items using trait" do
      order = create(:order, :with_line_items)
      expect(order.line_items.count).to eq(2)
      expect(order.total).to be > 0
      expect(order.total).to eq(order.line_items.sum(&:total_price))
    end
  end

  describe "line items relationship" do
    let(:order) { create(:order) }
    let(:product1) { create(:product) }
    let(:product2) { create(:product) }

    context "with line items" do
      before do
        create(:line_item, order: order, product: product1, quantity: 2, price: 10.99)
        create(:line_item, order: order, product: product2, quantity: 1, price: 25.50)
      end

      it "has associated line items" do
        expect(order.line_items.count).to eq(2)
      end

      it "has associated products through line items" do
        expect(order.products).to include(product1, product2)
      end
    end

    context "when order is destroyed" do
      let!(:line_item) { create(:line_item, order: order) }

      it "destroys associated line items" do
        expect { order.destroy }.to change(LineItem, :count).by(-1)
      end
    end
  end

  describe "total calculation" do
    let(:order) { create(:order, total: 0) }
    let(:product1) { create(:product) }
    let(:product2) { create(:product) }

    before do
      create(:line_item, order: order, product: product1, quantity: 2, price: 15.99)
      create(:line_item, order: order, product: product2, quantity: 3, price: 8.50)
    end

    it "can calculate total from line items" do
      calculated_total = order.line_items.sum(&:total_price)
      expected_total = (15.99 * 2) + (8.50 * 3)
      expect(calculated_total).to eq(expected_total)
    end
  end
end
