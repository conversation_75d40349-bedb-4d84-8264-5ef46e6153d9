# frozen_string_literal: true

require "rails_helper"

RSpec.describe GiftCardBatch, type: :model do
  describe "associations" do
    it { should have_many(:gift_card_orders).dependent(:restrict_with_exception) }
    it { should have_one_attached(:csv_file) }
  end

  describe "validations" do
    it { should validate_presence_of(:batch_date) }
    it { should validate_uniqueness_of(:batch_date) }
    it { should validate_presence_of(:filename) }
    it { should validate_numericality_of(:total_orders).is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:total_amount).is_greater_than_or_equal_to(0) }
  end

  describe "enums" do
    it { should define_enum_for(:status).with_values(pending: 0, generated: 1, uploaded: 2, failed: 3) }
  end

  describe "scopes" do
    describe ".for_date" do
      let(:date) { Date.current }
      let!(:batch_for_date) { create(:gift_card_batch, batch_date: date) }
      let!(:batch_for_other_date) { create(:gift_card_batch, batch_date: date + 1.day) }

      it "returns batches for the specified date" do
        expect(described_class.for_date(date)).to include(batch_for_date)
        expect(described_class.for_date(date)).not_to include(batch_for_other_date)
      end
    end
  end

  describe ".generate_filename" do
    it "generates filename with correct format" do
      timestamp = Time.zone.local(2023, 6, 15, 14, 30, 45)
      filename = described_class.generate_filename(timestamp)
      expect(filename).to eq("WWR-ZS3PG-20230615_143045.csv")
    end

    it "uses current time when no timestamp provided" do
      travel_to(Time.zone.local(2023, 6, 15, 14, 30, 45)) do
        filename = described_class.generate_filename
        expected = "WWR-ZS3PG-20230615_143045.csv"
        expect(filename).to eq(expected)
      end
    end
  end

  describe ".for_date_or_create" do
    let(:date) { Date.current }

    context "when batch exists for date" do
      let!(:existing_batch) { create(:gift_card_batch, batch_date: date) }

      it "returns existing batch" do
        expect(described_class.for_date_or_create(date)).to eq(existing_batch)
      end

      it "does not create new batch" do
        expect { described_class.for_date_or_create(date) }.not_to change(described_class, :count)
      end
    end

    context "when batch does not exist for date" do
      it "creates new batch" do
        expect { described_class.for_date_or_create(date) }.to change(described_class, :count).by(1)
      end

      it "sets correct batch_date" do
        batch = described_class.for_date_or_create(date)
        expect(batch.batch_date).to eq(date)
      end

      it "generates filename" do
        batch = described_class.for_date_or_create(date)
        expect(batch.filename).to match(/WWR-ZS3PG-\d{8}_\d{6}\.csv/)
      end
    end

    it "uses current date when no date provided" do
      batch = described_class.for_date_or_create
      expect(batch.batch_date).to eq(Date.current)
    end
  end

  describe "#generate_csv" do
    let(:batch) { create(:gift_card_batch) }
    let!(:gift_card_order1) { create(:gift_card_order, gift_card_batch: batch) }
    let!(:gift_card_order2) { create(:gift_card_order, gift_card_batch: batch) }

    it "generates CSV with headers" do
      csv_content = batch.generate_csv
      lines = csv_content.split("\n")
      expect(lines.first).to eq(GiftCardOrder.csv_headers.join(","))
    end

    it "includes all gift card orders" do
      csv_content = batch.generate_csv
      lines = csv_content.split("\n")
      expect(lines.length).to eq(3) # header + 2 orders
    end

    it "includes order data in CSV format" do
      csv_content = batch.generate_csv
      expect(csv_content).to include(gift_card_order1.recipient_first_name)
      expect(csv_content).to include(gift_card_order2.recipient_first_name)
    end
  end

  describe "#process_orders!" do
    let(:batch) { create(:gift_card_batch, batch_date: Date.current) }
    let(:order1) { create(:order) }
    let(:order2) { create(:order) }
    let!(:gift_card_order1) do
      create(:gift_card_order, order: order1, amount: 100.0, created_at: Date.current.beginning_of_day)
    end
    let!(:gift_card_order2) do
      create(:gift_card_order, order: order2, amount: 150.0, created_at: Date.current.beginning_of_day)
    end
    let!(:other_date_order) do
      create(:gift_card_order, created_at: 1.day.ago)
    end

    it "updates orders to belong to batch" do
      batch.process_orders!
      expect(gift_card_order1.reload.gift_card_batch).to eq(batch)
      expect(gift_card_order2.reload.gift_card_batch).to eq(batch)
    end

    it "does not update orders from other dates" do
      batch.process_orders!
      expect(other_date_order.reload.gift_card_batch).to be_nil
    end

    it "updates order status to batched" do
      batch.process_orders!
      expect(gift_card_order1.reload.status).to eq("batched")
      expect(gift_card_order2.reload.status).to eq("batched")
    end

    it "updates batch totals" do
      batch.process_orders!
      batch.reload
      expect(batch.total_orders).to eq(2)
      expect(batch.total_amount).to eq(gift_card_order1.amount + gift_card_order2.amount)
    end

    it "updates batch status to generated" do
      batch.process_orders!
      expect(batch.reload.status).to eq("generated")
    end

    it "attaches CSV file" do
      batch.process_orders!
      expect(batch.csv_file).to be_attached
    end

    context "when no orders for date" do
      let(:batch) { create(:gift_card_batch, batch_date: 1.week.from_now) }

      it "returns early without changes" do
        expect { batch.process_orders! }.not_to change(batch, :updated_at)
      end
    end
  end

  describe "#upload_to_sftp!" do
    let(:batch) { create(:gift_card_batch, :with_csv_file) }
    let(:sftp_service) { instance_double(GiftCardSftpService) }

    before do
      allow(GiftCardSftpService).to receive(:new).and_return(sftp_service)
    end

    context "when CSV file is attached" do
      context "when upload succeeds" do
        before do
          allow(sftp_service).to receive(:upload_file).and_return(true)
        end

        it "uploads file via SFTP service" do
          batch.upload_to_sftp!
          expect(sftp_service).to have_received(:upload_file).with(batch.csv_file, batch.filename)
        end

        it "updates batch status to uploaded" do
          batch.upload_to_sftp!
          expect(batch.reload.status).to eq("uploaded")
        end

        it "sets uploaded_at timestamp" do
          travel_to(Time.current) do
            batch.upload_to_sftp!
            expect(batch.reload.uploaded_at).to be_within(1.second).of(Time.current)
          end
        end

        it "clears upload_error" do
          batch.update!(upload_error: "Previous error")
          batch.upload_to_sftp!
          expect(batch.reload.upload_error).to be_nil
        end

        it "returns true" do
          expect(batch.upload_to_sftp!).to be true
        end
      end

      context "when upload fails" do
        let(:error_message) { "SFTP connection failed" }

        before do
          allow(sftp_service).to receive(:upload_file).and_raise(StandardError.new(error_message))
        end

        it "updates batch status to failed" do
          batch.upload_to_sftp!
          expect(batch.reload.status).to eq("failed")
        end

        it "sets upload_error" do
          batch.upload_to_sftp!
          expect(batch.reload.upload_error).to eq(error_message)
        end

        it "returns false" do
          expect(batch.upload_to_sftp!).to be false
        end
      end
    end

    context "when CSV file is not attached" do
      let(:batch) { create(:gift_card_batch) }

      it "returns false without attempting upload" do
        expect(batch.upload_to_sftp!).to be false
      end
    end
  end

  describe "#retry_upload!" do
    context "when batch is failed" do
      let(:batch) { create(:gift_card_batch, :failed, :with_csv_file) }
      let(:sftp_service) { instance_double(GiftCardSftpService) }

      before do
        allow(GiftCardSftpService).to receive(:new).and_return(sftp_service)
        allow(sftp_service).to receive(:upload_file).and_return(true)
      end

      it "resets status to generated" do
        batch.retry_upload!
        expect(batch.reload.status).to eq("uploaded") # After successful retry
      end

      it "clears upload_error" do
        batch.update!(upload_error: "Previous error")
        batch.retry_upload!
        expect(batch.reload.upload_error).to be_nil
      end

      it "attempts upload again" do
        batch.retry_upload!
        expect(sftp_service).to have_received(:upload_file)
      end
    end

    context "when batch is not failed" do
      let(:batch) { create(:gift_card_batch, :uploaded) }

      it "returns false without retrying" do
        expect(batch.retry_upload!).to be false
      end
    end
  end

  describe "ransack configuration" do
    it "allows searching by specified attributes" do
      expected_attributes = %w[batch_date filename total_orders total_amount status uploaded_at created_at updated_at]
      expect(described_class.ransackable_attributes).to match_array(expected_attributes)
    end

    it "allows searching by specified associations" do
      expected_associations = %w[gift_card_orders]
      expect(described_class.ransackable_associations).to match_array(expected_associations)
    end
  end
end
