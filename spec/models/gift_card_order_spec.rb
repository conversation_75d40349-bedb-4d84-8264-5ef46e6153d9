# frozen_string_literal: true

# == Schema Information
#
# Table name: gift_card_orders
#
#  id                   :bigint           not null, primary key
#  amount               :decimal(10, 2)   not null
#  message              :text
#  offer_code           :string
#  recipient_city       :string           not null
#  recipient_company    :string
#  recipient_country    :string           not null
#  recipient_email      :string           not null
#  recipient_first_name :string           not null
#  recipient_last_name  :string           not null
#  recipient_phone      :string
#  recipient_state      :string           not null
#  recipient_street1    :string           not null
#  recipient_street2    :string
#  recipient_zip        :string           not null
#  status               :integer          default("pending"), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  gift_card_batch_id   :bigint
#  order_id             :bigint           not null
#  product_id           :bigint           not null
#
# Indexes
#
#  index_gift_card_orders_on_created_at          (created_at)
#  index_gift_card_orders_on_gift_card_batch_id  (gift_card_batch_id)
#  index_gift_card_orders_on_order_id            (order_id)
#  index_gift_card_orders_on_product_id          (product_id)
#  index_gift_card_orders_on_status              (status)
#
# Foreign Keys
#
#  fk_rails_...  (gift_card_batch_id => gift_card_batches.id)
#  fk_rails_...  (order_id => orders.id)
#  fk_rails_...  (product_id => products.id)
#
require "rails_helper"

RSpec.describe GiftCardOrder, type: :model do
  describe "associations" do
    it { should belong_to(:order) }
    it { should belong_to(:product) }
    it { should belong_to(:gift_card_batch).optional }
  end

  describe "validations" do
    it { should validate_presence_of(:amount) }
    it { should validate_numericality_of(:amount).is_greater_than(0) }
    it { should validate_presence_of(:recipient_first_name) }
    it { should validate_presence_of(:recipient_last_name) }
    it { should validate_presence_of(:recipient_street1) }
    it { should validate_presence_of(:recipient_city) }
    it { should validate_presence_of(:recipient_state) }
    it { should validate_presence_of(:recipient_zip) }
    it { should validate_presence_of(:recipient_country) }
    it { should validate_presence_of(:recipient_email) }

    describe "email format validation" do
      let(:gift_card_order) { build(:gift_card_order) }

      it "accepts valid email addresses" do
        valid_emails = %w[
          <EMAIL>
          <EMAIL>
          <EMAIL>
        ]

        valid_emails.each do |email|
          gift_card_order.recipient_email = email
          expect(gift_card_order).to be_valid, "#{email} should be valid"
        end
      end

      it "rejects invalid email addresses" do
        invalid_emails = %w[
          invalid-email
          @domain.com
          user@
          <EMAIL>
        ]

        invalid_emails.each do |email|
          gift_card_order.recipient_email = email
          expect(gift_card_order).not_to be_valid, "#{email} should be invalid"
        end
      end
    end
  end

  describe "enums" do
    it { should define_enum_for(:status).with_values(pending: 0, batched: 1, uploaded: 2, failed: 3) }
  end

  describe "scopes" do
    describe ".for_date" do
      let(:target_date) { Date.current }
      let!(:order_for_date) do
        create(:gift_card_order, created_at: target_date.beginning_of_day + 12.hours)
      end
      let!(:order_for_other_date) do
        create(:gift_card_order, created_at: target_date + 1.day)
      end

      it "returns orders created on the specified date" do
        result = described_class.for_date(target_date)
        expect(result).to include(order_for_date)
        expect(result).not_to include(order_for_other_date)
      end

      it "includes orders from beginning to end of day" do
        early_order = create(:gift_card_order, created_at: target_date.beginning_of_day)
        late_order = create(:gift_card_order, created_at: target_date.end_of_day)

        result = described_class.for_date(target_date)
        expect(result).to include(early_order, late_order)
      end
    end

    describe ".unbatched" do
      let!(:batched_order) { create(:gift_card_order, :batched) }
      let!(:unbatched_order) { create(:gift_card_order, gift_card_batch: nil) }

      it "returns only orders without a batch" do
        result = described_class.unbatched
        expect(result).to include(unbatched_order)
        expect(result).not_to include(batched_order)
      end
    end
  end

  describe "#to_csv_row" do
    let(:gift_card_order) do
      create(:gift_card_order,
        recipient_first_name: "John",
        recipient_last_name: "Doe",
        recipient_company: "Acme Corp",
        recipient_street1: "123 Main St",
        recipient_street2: "Apt 4B",
        recipient_city: "Anytown",
        recipient_state: "CA",
        recipient_zip: "12345",
        recipient_country: "US",
        recipient_phone: "555-1234",
        recipient_email: "<EMAIL>",
        amount: 100.50,
        offer_code: "SAVE10",
        message: "Happy Birthday!")
    end

    it "returns array with all required fields" do
      row = gift_card_order.to_csv_row
      expected = [
        "John", "Doe", "Acme Corp", "123 Main St", "Apt 4B",
        "Anytown", "CA", "12345", "US", "555-1234",
        "<EMAIL>", 100.5, "SAVE10", "Happy Birthday!"
      ]
      expect(row).to eq(expected)
    end

    it "handles nil optional fields" do
      gift_card_order.update!(
        recipient_company: nil,
        recipient_street2: nil,
        recipient_phone: nil,
        offer_code: nil,
        message: nil
      )

      row = gift_card_order.to_csv_row
      expect(row[2]).to eq("") # company
      expect(row[4]).to eq("") # street2
      expect(row[9]).to eq("") # phone
      expect(row[12]).to eq("") # offer_code
      expect(row[13]).to eq("") # message
    end
  end

  describe ".csv_headers" do
    it "returns correct header array" do
      expected_headers = %w[
        FirstName LastName Company Street1 Street2 City State Zip Country
        Phone Email Amount OfferCode Message
      ]
      expect(described_class.csv_headers).to eq(expected_headers)
    end

    it "has same number of headers as csv row fields" do
      gift_card_order = create(:gift_card_order)
      expect(described_class.csv_headers.length).to eq(gift_card_order.to_csv_row.length)
    end
  end

  describe "factory" do
    it "creates valid gift card order" do
      gift_card_order = create(:gift_card_order)
      expect(gift_card_order).to be_valid
    end

    it "creates gift card order with batch" do
      gift_card_order = create(:gift_card_order, :batched)
      expect(gift_card_order.gift_card_batch).to be_present
      expect(gift_card_order.status).to eq("batched")
    end
  end
end
