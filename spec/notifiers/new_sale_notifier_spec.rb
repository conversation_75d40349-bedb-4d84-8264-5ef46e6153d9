require "rails_helper"

RSpec.describe NewSaleNotifier, type: :notifier do
  let(:user) { create(:user) }
  let(:admin) { create(:user, :admin) }
  let(:product) { create(:product) }
  let(:sale) { create(:sale, user: user, product: product) }

  describe ".deliver" do
    it "delivers notifications to all admin users" do
      admin1 = create(:user, :admin)
      admin2 = create(:user, :admin)
      regular_user = create(:user)

      expect {
        NewSaleNotifier.with(record: sale).deliver
      }.to change { Noticed::Notification.count }.by(2) # Only to admins

      notifications = Noticed::Notification.all
      expect(notifications.map(&:recipient)).to contain_exactly(admin1, admin2)
    end

    it "respects user notification preferences" do
      admin.update!(sale_notifications: false)

      expect {
        NewSaleNotifier.with(record: sale).deliver
      }.not_to change { Noticed::Notification.count }
    end

    it "respects quiet hours" do
      admin.update!(
        quiet_hours_enabled: true,
        quiet_hours_start: "22:00",
        quiet_hours_end: "08:00"
      )

      # Mock current time to be in quiet hours
      allow(Time).to receive(:current).and_return(Time.parse("23:30"))

      # Should still create notification but not deliver push notification
      expect {
        NewSaleNotifier.with(record: sale).deliver
      }.to change { Noticed::Notification.count }.by(1)
    end
  end

  describe "notification methods" do
    let(:notification) { NewSaleNotifier.with(record: sale) }

    it "generates correct message" do
      expect(notification.message).to eq(
        "New sale submitted by #{user.email} for #{product.name} (#{sale.points} points)"
      )
    end

    it "generates correct title" do
      expect(notification.title).to eq("New Sale Pending Approval")
    end

    it "generates correct URL" do
      expect(notification.url).to include("/admin/sales/#{sale.id}")
    end

    it "provides access to sale data" do
      expect(notification.sale).to eq(sale)
      expect(notification.seller).to eq(user)
      expect(notification.product).to eq(product)
      expect(notification.points).to eq(sale.points)
      expect(notification.serial_number).to eq(sale.serial_number)
    end
  end

  describe "validations" do
    it "requires a record" do
      notifier = NewSaleNotifier.with(record: nil)
      expect(notifier).not_to be_valid
      expect(notifier.errors[:record]).to include("can't be blank")
    end
  end
end
