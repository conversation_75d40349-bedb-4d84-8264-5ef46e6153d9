# frozen_string_literal: true

require "rails_helper"

RSpec.describe ApplicationNotifier, type: :notifier do
  describe "inheritance" do
    it "inherits from Noticed::Event" do
      expect(described_class.superclass).to eq(Noticed::Event)
    end
  end

  describe "delivery methods" do
    it "can be configured with delivery methods" do
      expect(described_class).to respond_to(:deliver_by)
    end
  end

  describe "notification creation" do
    let(:user) { create(:user) }

    # Since ApplicationNotifier is abstract, we'll test with a concrete subclass
    let(:notifier_class) do
      Class.new(ApplicationNotifier) do
        deliver_by :database

        def message
          "Test notification"
        end
      end
    end

    it "can create notifications" do
      expect do
        notifier_class.with(message: "Test").deliver(user)
      end.to change(Noticed::Event, :count).by(1)
    end
  end

  describe "base functionality" do
    it "provides base notification functionality" do
      expect(described_class.ancestors).to include(Noticed::Event)
    end

    it "can be subclassed" do
      subclass = Class.new(described_class)
      expect(subclass.superclass).to eq(described_class)
    end
  end
end
