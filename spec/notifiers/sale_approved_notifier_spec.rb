require "rails_helper"

RSpec.describe SaleApprovedNotifier, type: :notifier do
  let(:user) { create(:user) }
  let(:admin) { create(:user, :admin) }
  let(:product) { create(:product) }
  let(:sale) { create(:sale, user: user, product: product, status: :approved) }

  describe ".deliver" do
    it "delivers notification to the sale user" do
      expect {
        SaleApprovedNotifier.with(record: sale, approved_by: admin).deliver(user)
      }.to change { user.notifications.count }.by(1)
    end

    it "respects user notification preferences" do
      user.update!(sale_notifications: false)

      expect {
        SaleApprovedNotifier.with(record: sale, approved_by: admin).deliver(user)
      }.not_to change { Noticed::Notification.count }
    end

    it "respects email notification settings" do
      user.update!(email_notifications_enabled: false)

      # Should still create notification but not send email
      expect {
        SaleApprovedNotifier.with(record: sale, approved_by: admin).deliver(user)
      }.to change { Noticed::Notification.count }.by(1)
    end
  end

  describe "notification methods" do
    let(:notification) { SaleApprovedNotifier.with(record: sale, approved_by: admin) }

    before do
      allow(sale).to receive(:total_points_with_promotions).and_return(150)
    end

    it "generates correct message with bonus points" do
      expected_message = "Your sale of #{product.name} has been approved! 150 points credited to your account (including 50 bonus points)."
      expect(notification.message).to eq(expected_message)
    end

    it "generates correct message without bonus points" do
      allow(sale).to receive(:total_points_with_promotions).and_return(100)
      expected_message = "Your sale of #{product.name} has been approved! 100 points credited to your account."
      expect(notification.message).to eq(expected_message)
    end

    it "generates correct title" do
      expect(notification.title).to eq("Sale Approved!")
    end

    it "provides access to sale data" do
      expect(notification.sale).to eq(sale)
      expect(notification.product).to eq(product)
      expect(notification.base_points).to eq(sale.points)
      expect(notification.approved_by_admin).to eq(admin)
      expect(notification.serial_number).to eq(sale.serial_number)
    end

    it "calculates bonus points correctly" do
      allow(sale).to receive(:total_points_with_promotions).and_return(150)
      expect(notification.bonus_points).to eq(50)
      expect(notification.has_bonus?).to be true
    end

    it "handles no bonus points" do
      allow(sale).to receive(:total_points_with_promotions).and_return(100)
      expect(notification.bonus_points).to eq(0)
      expect(notification.has_bonus?).to be false
    end
  end

  describe "validations" do
    it "requires a record" do
      notifier = SaleApprovedNotifier.with(record: nil, approved_by: admin)
      expect(notifier).not_to be_valid
      expect(notifier.errors[:record]).to include("can't be blank")
    end

    it "requires approved_by parameter" do
      expect {
        SaleApprovedNotifier.with(record: sale)
      }.to raise_error(Noticed::ValidationError)
    end
  end
end
