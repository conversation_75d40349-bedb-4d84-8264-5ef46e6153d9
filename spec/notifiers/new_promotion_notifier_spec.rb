# frozen_string_literal: true

require "rails_helper"

RSpec.describe NewPromotionNotifier, type: :notifier do
  let(:user) { create(:user) }
  let(:promotion) { create(:promotion) }

  describe "delivery methods" do
    it "inherits from ApplicationNotifier" do
      expect(described_class.superclass).to eq(ApplicationNotifier)
    end
  end

  describe "notification creation" do
    it "can create new promotion notifications" do
      expect do
        described_class.with(promotion: promotion).deliver(user)
      end.to change(Noticed::Event, :count).by(1)
    end

    it "includes promotion data in params" do
      notification = described_class.with(promotion: promotion).deliver(user)
      expect(notification.params["promotion"]).to eq(promotion)
    end
  end

  describe "delivery" do
    let(:notification) { described_class.with(promotion: promotion) }

    it "delivers to the specified user" do
      delivered_notification = notification.deliver(user)
      expect(delivered_notification.recipient).to eq(user)
    end

    it "can deliver to multiple users" do
      users = create_list(:user, 3)

      expect do
        notification.deliver(users)
      end.to change(Noticed::Event, :count).by(3)
    end
  end

  describe "notification content" do
    let(:notification) { described_class.with(promotion: promotion).deliver(user) }

    it "stores promotion information" do
      expect(notification.params).to include("promotion")
    end

    it "can access promotion through params" do
      stored_promotion = notification.params["promotion"]
      expect(stored_promotion).to eq(promotion)
    end
  end

  describe "integration with promotion workflow" do
    it "can be triggered when promotion is created" do
      expect do
        described_class.with(promotion: promotion).deliver(user)
      end.to change(Noticed::Event, :count).by(1)
    end

    it "handles active promotions" do
      promotion.update!(status: :active)

      expect do
        described_class.with(promotion: promotion).deliver(user)
      end.to change(Noticed::Event, :count).by(1)
    end

    it "can notify about promotion expiration" do
      promotion.update!(end_date: 1.day.from_now)

      expect do
        described_class.with(promotion: promotion, type: "expiring_soon").deliver(user)
      end.to change(Noticed::Event, :count).by(1)
    end
  end

  describe "bulk notifications" do
    let(:users) { create_list(:user, 5) }

    it "can send promotion notifications to multiple users efficiently" do
      expect do
        described_class.with(promotion: promotion).deliver(users)
      end.to change(Noticed::Event, :count).by(5)
    end

    it "handles empty recipient lists gracefully" do
      expect do
        described_class.with(promotion: promotion).deliver([])
      end.not_to change(Noticed::Event, :count)
    end
  end
end
