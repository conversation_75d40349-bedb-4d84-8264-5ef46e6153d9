# frozen_string_literal: true

require "rails_helper"

RSpec.describe NewOrderNotifier, type: :notifier do
  let(:user) { create(:user) }
  let(:order) { create(:order, user: user) }

  describe "delivery methods" do
    it "inherits from ApplicationNotifier" do
      expect(described_class.superclass).to eq(ApplicationNotifier)
    end
  end

  describe "notification creation" do
    it "can create new order notifications" do
      expect do
        described_class.with(order: order).deliver(user)
      end.to change(Noticed::Event, :count).by(1)
    end

    it "includes order data in params" do
      notification = described_class.with(order: order).deliver(user)
      expect(notification.params["order"]).to eq(order)
    end
  end

  describe "delivery" do
    let(:notification) { described_class.with(order: order) }

    it "delivers to the specified user" do
      delivered_notification = notification.deliver(user)
      expect(delivered_notification.recipient).to eq(user)
    end

    it "can deliver to multiple recipients" do
      admin_user = create(:user, role: :admin)

      expect do
        notification.deliver([user, admin_user])
      end.to change(Noticed::Event, :count).by(2)
    end
  end

  describe "notification content" do
    let(:notification) { described_class.with(order: order).deliver(user) }

    it "stores order information" do
      expect(notification.params).to include("order")
    end

    it "can access order through params" do
      stored_order = notification.params["order"]
      expect(stored_order).to eq(order)
    end
  end

  describe "integration with order workflow" do
    it "can be triggered when order is created" do
      # This would typically be called from an order callback or service
      expect do
        described_class.with(order: order).deliver(user)
      end.to change(Noticed::Event, :count).by(1)
    end

    it "handles order updates" do
      order.update!(status: :approved)

      expect do
        described_class.with(order: order).deliver(user)
      end.to change(Noticed::Event, :count).by(1)
    end
  end
end
