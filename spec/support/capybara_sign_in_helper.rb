module CapybaraSignInHelper
  def sign_in_with_capybara(user, password = "password")
    user.confirm if user.respond_to?(:confirm)
    visit new_user_session_path
    fill_in "Email Address", with: user.email
    fill_in "Password", with: password
    click_button "Sign In"
    expect(page).not_to have_content("Sign In")
    # Ensure we're redirected to the dashboard
    visit root_path
    expect(page).to have_content("Welcome back")
  end

  # Alias for compatibility with specs using sign_in_user
  def sign_in_user(user, password = "password")
    sign_in_with_capybara(user, password)
  end
end
