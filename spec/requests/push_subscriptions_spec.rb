require "rails_helper"

RSpec.describe PushSubscriptionsController, type: :request do
  let(:user) { create(:user) }
  let(:subscription_params) { {endpoint: "https://example.com", keys: {p256dh: "key", auth: "auth"}} }

  before { sign_in user }

  it "creates a push subscription" do
    post push_subscriptions_path, params: {push_subscription: subscription_params}
    expect(response).to have_http_status(:created)
  end

  # Add more tests for update, destroy, and edge cases
end
