require "rails_helper"

RSpec.describe "Dashboard", type: :request do
  let(:user) { create(:user) }
  let(:product) { create(:product) }

  before do
    login_as(user, scope: :user)
  end

  describe "GET /dashboard" do
    it "returns http success" do
      get authenticated_root_path
      expect(response).to have_http_status(:success)
    end

    it "shows only the user's recent sales (max 5, most recent first)" do
      7.times do |i|
        # SN000006 is most recent, SN000000 is oldest
        create(:sale, user: user, product: product, serial_number: "SN00000#{i}", sold_at: (6 - i).days.ago - 2.hours)
      end
      other_user = create(:user, email: "<EMAIL>")
      create(:sale, user: other_user, product: product, serial_number: "OTHERSN00", sold_at: 2.hours.ago)

      get authenticated_root_path

      # Only 5 most recent sales for the user should be shown (SN000006 to SN000002)
      (2..6).to_a.reverse_each do |i|
        expect(response.body).to include("SN00000#{i}")
      end
      expect(response.body).not_to include("SN000000")
      expect(response.body).not_to include("SN000001")
      expect(response.body).not_to include("OTHERSN00")
    end

    it "shows only the user's recent orders (max 5, most recent first)" do
      7.times do |i|
        create(:order, user: user, points: 10 + i, shipping_type: "standard", created_at: i.days.ago)
      end
      other_user = create(:user, email: "<EMAIL>")
      create(:order, user: other_user, points: 99, shipping_type: "express", created_at: 2.hours.ago)

      get authenticated_root_path

      # Only 5 most recent orders for the user should be shown
      (2..6).each do |i|
        expect(response.body).to include((10 + i).to_s)
      end
      expect(response.body).not_to include("99")
    end
  end
end
