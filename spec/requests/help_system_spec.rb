require "rails_helper"

RSpec.describe "Help System API", type: :request do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user) }

  describe "Help endpoints" do
    before do
      login_as(user, scope: :user)
    end

    describe "GET /help" do
      it "returns successful response" do
        get help_index_path

        expect(response).to have_http_status(:success)
        expect(response.body).to include("Help & Support")
        expect(response.body).to include("Frequently Asked Questions")
      end

      it "assigns FAQ data" do
        get help_index_path

        expect(assigns(:faqs)).to be_present
        expect(assigns(:faqs)).to be_an(Array)
        expect(assigns(:faqs).length).to eq(8)

        faq = assigns(:faqs).first
        expect(faq).to have_key(:question)
        expect(faq).to have_key(:answer)
      end

      it "includes comprehensive FAQ content" do
        get help_index_path

        faqs = assigns(:faqs)
        questions = faqs.map { |faq| faq[:question] }

        expect(questions).to include(
          "How do I earn points?",
          "How do I redeem my points?",
          "Why is my sale still pending?",
          "How do I track my orders?",
          "Can I cancel an order?",
          "How do I update my profile information?",
          "What should I do if I can't scan a barcode?",
          "How do push notifications work?"
        )
      end

      it "requires authentication" do
        logout(:user)
        get help_index_path

        expect(response).to redirect_to(new_user_session_path)
      end
    end

    describe "GET /help/contact" do
      it "returns successful response" do
        get help_contact_path

        expect(response).to have_http_status(:success)
        expect(response.body).to include("Contact Support")
        expect(response.body).to include("Send us your question")
      end

      it "shows user information in form" do
        get help_contact_path

        expect(response.body).to include(user.email)
        expect(response.body).to include("##{user.id}")
      end

      it "includes all category options" do
        get help_contact_path

        SupportTicket::CATEGORIES.each do |key, value|
          expect(response.body).to include(ERB::Util.html_escape(value))
        end
      end

      it "requires authentication" do
        logout(:user)
        get help_contact_path

        expect(response).to redirect_to(new_user_session_path)
      end
    end

    describe "POST /help/contact" do
      let(:valid_params) do
        {
          question: {
            subject: "Question about points calculation",
            message: "I submitted a sale but the points don't match what I expected. Can you help me understand how points are calculated for different product categories?",
            category: "sales"
          }
        }
      end

      let(:invalid_params) do
        {
          question: {
            subject: "Hi",
            message: "Help",
            category: "sales"
          }
        }
      end

      context "with valid parameters" do
        it "creates a support ticket" do
          expect {
            post help_submit_question_path, params: valid_params
          }.to change(SupportTicket, :count).by(1)

          expect(response).to redirect_to(help_index_path)
        end

        it "sets correct ticket attributes" do
          post help_submit_question_path, params: valid_params

          ticket = SupportTicket.last
          expect(ticket.user).to eq(user)
          expect(ticket.subject).to eq("Question about points calculation")
          expect(ticket.category).to eq("sales")
          expect(ticket.status).to eq("open")
          expect(ticket.priority).to eq("normal")
        end

        context "for admin-handled categories" do
          it "sends admin notification email" do
            expect {
              post help_submit_question_path, params: valid_params
            }.to change { ActionMailer::Base.deliveries.count }.by(1)

            email = ActionMailer::Base.deliveries.last
            expect(email.to).to include("<EMAIL>")
            expect(email.subject).to include("Sales & Points")
          end

          it "shows appropriate success message" do
            post help_submit_question_path, params: valid_params

            follow_redirect!
            expect(response.body).to include("submitted to our support team")
          end
        end

        context "for self-service categories" do
          let(:self_service_params) do
            valid_params.deep_merge(question: {category: "technical"})
          end

          it "sends auto-response email" do
            expect {
              post help_submit_question_path, params: self_service_params
            }.to change { ActionMailer::Base.deliveries.count }.by(1)

            email = ActionMailer::Base.deliveries.last
            expect(email.to).to include(user.email)
            expect(email.subject).to include("Thank you for contacting")
          end

          it "shows appropriate success message" do
            post help_submit_question_path, params: self_service_params

            follow_redirect!
            expect(response.body).to include("sent some helpful information")
          end
        end
      end

      context "with invalid parameters" do
        it "does not create a support ticket" do
          expect {
            post help_submit_question_path, params: invalid_params
          }.not_to change(SupportTicket, :count)
        end

        it "renders contact form with errors" do
          post help_submit_question_path, params: invalid_params

          expect(response).to have_http_status(:success)
          expect(response).to render_template(:contact)
          expect(response.body).to include("error submitting")
        end

        it "preserves form data on error" do
          post help_submit_question_path, params: invalid_params

          expect(response.body).to include('value="Hi"')  # Subject field
          expect(response.body).to include("Help")  # Message field
        end
      end

      it "requires authentication" do
        logout(:user)
        post help_submit_question_path, params: valid_params

        expect(response).to redirect_to(new_user_session_path)
      end

      it "validates required parameters" do
        post help_submit_question_path, params: {question: {}}

        expect(response).to have_http_status(:success)
        expect(response.body).to include("error submitting")
      end
    end
  end

  describe "Admin support ticket endpoints" do
    before do
      login_as(admin_user, scope: :user)
    end

    describe "GET /admin/support_tickets" do
      let!(:admin_user) { create(:user, :admin) }
      let!(:tickets) do
        [
          create(:support_ticket, user: admin_user, status: "open", category: "sales"),
          create(:support_ticket, user: admin_user, status: "resolved", category: "orders"),
          create(:support_ticket, user: admin_user, priority: "high")
        ]
      end

      before do
        login_as(admin_user, scope: :user)
      end

      it "returns successful response" do
        get admin_support_tickets_path

        expect(response).to have_http_status(:success)
        expect(response.body).to include("Support Tickets")
        tickets.each do |ticket|
          expect(response.body).to include(ticket.subject)
        end
      end

      it "lists paginated tickets with associations" do
        get admin_support_tickets_path

        tickets.each do |ticket|
          expect(response.body).to include(ticket.subject)
          expect(response.body).to include(ticket.user.email)
        end
      end

      it "shows status counts" do
        get admin_support_tickets_path

        expect(response.body).to include(SupportTicket.count.to_s)
        expect(response.body).to include(SupportTicket.open.count.to_s)
        expect(response.body).to include(SupportTicket.resolved.count.to_s)
      end

      context "with filters" do
        it "filters by status" do
          get admin_support_tickets_path, params: {status: "open"}

          expect(response.body).to include("Open")
          expect(response.body).to include(tickets.first.subject)
        end

        it "filters by category" do
          get admin_support_tickets_path, params: {category: "sales"}

          expect(response.body).to include("Sales &amp; Points")
          expect(response.body).to include(tickets.first.subject)
        end

        it "filters by priority" do
          get admin_support_tickets_path, params: {priority: "high"}

          expect(response.body).to include("High")
          expect(response.body).to include(tickets.last.subject)
        end
      end
    end

    describe "GET /admin/support_tickets/:id" do
      let(:admin_user) { create(:user, :admin) }
      let(:support_ticket) { create(:support_ticket, user: admin_user) }

      before do
        login_as(admin_user, scope: :user)
      end

      it "returns successful response" do
        get admin_support_ticket_path(support_ticket)

        expect(response).to have_http_status(:success)
        expect(response.body).to include(support_ticket.subject)
      end

      it "shows user information" do
        get admin_support_ticket_path(support_ticket)

        expect(response.body).to include(admin_user.email)
        expect(response.body).to include("##{admin_user.id}")
      end
    end

    describe "PATCH /admin/support_tickets/:id" do
      let(:admin_user) { create(:user, :admin) }
      let(:support_ticket) { create(:support_ticket, user: admin_user, status: "open") }

      before do
        login_as(admin_user, scope: :user)
      end

      it "updates ticket status and priority" do
        patch admin_support_ticket_path(support_ticket), params: {
          support_ticket: {
            status: "in_progress",
            priority: "high"
          }
        }

        expect(response).to redirect_to(admin_support_ticket_path(support_ticket))

        support_ticket.reload
        expect(support_ticket.status).to eq("in_progress")
        expect(support_ticket.priority).to eq("high")
      end

      it "shows success message" do
        patch admin_support_ticket_path(support_ticket), params: {
          support_ticket: {status: "resolved"}
        }

        follow_redirect!
        expect(response.body).to include("Support ticket updated successfully")
      end
    end

    describe "POST /admin/support_tickets/:id/respond" do
      let(:admin_user) { create(:user, :admin) }
      let(:support_ticket) { create(:support_ticket, user: admin_user, status: "open") }

      before do
        login_as(admin_user, scope: :user)
      end

      it "adds admin response and sends email" do
        expect {
          post respond_admin_support_ticket_path(support_ticket), params: {
            support_ticket: {
              admin_response: "Thank you for your question. Here's the solution..."
            }
          }
        }.to change { ActionMailer::Base.deliveries.count }.by(1)

        expect(response).to redirect_to(admin_support_ticket_path(support_ticket))

        support_ticket.reload
        expect(support_ticket.admin_response).to be_present
        expect(support_ticket.admin_user).to eq(admin_user)
        expect(support_ticket.responded_at).to be_present
        expect(support_ticket.status).to eq("resolved")
      end

      it "validates response content" do
        post respond_admin_support_ticket_path(support_ticket), params: {
          support_ticket: {admin_response: ""}
        }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.body).to include("Failed to send response")
        expect(response.body).to include("Admin response can&#39;t be blank")
      end
    end
  end

  describe "Error handling" do
    let(:admin_user) { create(:user, :admin) }
    before do
      login_as(admin_user, scope: :user)
    end

    it "handles missing support ticket gracefully" do
      get admin_support_ticket_path(99999)
      expect(response).to have_http_status(:not_found)
      expect(response.body).to include("Routing Error")
    end

    it "handles malformed parameters" do
      post help_submit_question_path, params: {invalid: "data"}

      expect(response).to have_http_status(:success)
      expect(response.body).to include("error submitting")
    end
  end
end
