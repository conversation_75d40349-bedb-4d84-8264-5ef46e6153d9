require "rails_helper"

RSpec.describe "Pagination", type: :request do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user) }

  describe "User-facing pagination endpoints" do
    before do
      sign_in user
    end

    describe "GET /products" do
      before do
        create_list(:product, 15, status: "active")
      end

      it "paginates products correctly" do
        get products_path

        expect(response).to have_http_status(:success)
        expect(assigns(:pagy)).to be_present
        expect(assigns(:products).count).to eq(12)  # First page
      end

      it "handles page parameter" do
        get products_path, params: {page: 2}

        expect(response).to have_http_status(:success)
        expect(assigns(:products).count).to eq(3)  # Second page
      end

      it "maintains search parameters across pages" do
        create(:product, name: "Special Product", status: "active")

        get products_path, params: {search: "Special", page: 1}

        expect(response).to have_http_status(:success)
        expect(assigns(:products).map(&:name)).to include("Special Product")
      end

      it "handles invalid page numbers gracefully" do
        get products_path, params: {page: 999}

        expect(response).to have_http_status(:success)
        # Should show last available page or handle gracefully
      end
    end

    describe "GET /orders" do
      before do
        create_list(:order, 12, user: user)
      end

      it "paginates user orders" do
        get orders_path

        expect(response).to have_http_status(:success)
        expect(assigns(:pagy)).to be_present
        expect(assigns(:orders).count).to eq(10)  # First page
      end

      it "only shows current user's orders" do
        other_user = create(:user)
        create(:order, user: other_user)

        get orders_path

        orders = assigns(:orders)
        expect(orders.map(&:user)).to all(eq(user))
      end

      it "orders by creation date descending" do
        get orders_path

        orders = assigns(:orders)
        expect(orders.first.created_at).to be >= orders.last.created_at
      end
    end
  end

  describe "Admin pagination endpoints" do
    before do
      sign_in admin_user
    end

    describe "GET /admin/support_tickets" do
      before do
        create_list(:support_ticket, 30, user: user)
      end

      it "paginates support tickets" do
        get admin_support_tickets_path

        expect(response).to have_http_status(:success)
        expect(assigns(:pagy)).to be_present
        expect(assigns(:support_tickets).count).to eq(25)  # Admin page size
      end

      it "includes user associations to prevent N+1 queries" do
        get admin_support_tickets_path

        tickets = assigns(:support_tickets)
        expect(tickets.first.association(:user)).to be_loaded
      end

      it "calculates status counts correctly" do
        create_list(:support_ticket, 5, user: user, status: "open")
        create_list(:support_ticket, 3, user: user, status: "resolved")

        get admin_support_tickets_path

        status_counts = assigns(:status_counts)
        expect(status_counts[:open]).to eq(SupportTicket.open.count)
        expect(status_counts[:resolved]).to eq(SupportTicket.resolved.count)
        expect(status_counts[:all]).to eq(SupportTicket.count)
      end

      context "with filters" do
        before do
          create_list(:support_ticket, 5, user: user, status: "open", category: "sales")
          create_list(:support_ticket, 3, user: user, status: "resolved", category: "technical")
          create_list(:support_ticket, 2, user: user, priority: "high")
        end

        it "filters by status" do
          get admin_support_tickets_path, params: {status: "open"}

          tickets = assigns(:support_tickets)
          expect(tickets.map(&:status)).to all(eq("open"))
        end

        it "filters by category" do
          get admin_support_tickets_path, params: {category: "sales"}

          tickets = assigns(:support_tickets)
          expect(tickets.map(&:category)).to all(eq("sales"))
        end

        it "filters by priority" do
          get admin_support_tickets_path, params: {priority: "high"}

          tickets = assigns(:support_tickets)
          expect(tickets.map(&:priority)).to all(eq("high"))
        end

        it "combines multiple filters" do
          get admin_support_tickets_path, params: {status: "open", category: "sales"}

          tickets = assigns(:support_tickets)
          expect(tickets.map(&:status)).to all(eq("open"))
          expect(tickets.map(&:category)).to all(eq("sales"))
        end
      end
    end

    describe "GET /admin/orders" do
      before do
        create_list(:order, 30, user: user)
      end

      it "paginates admin orders" do
        get admin_orders_path

        expect(response).to have_http_status(:success)
        expect(assigns(:pagy)).to be_present
        expect(assigns(:orders).count).to eq(25)  # Admin page size
      end

      it "includes necessary associations" do
        get admin_orders_path

        orders = assigns(:orders)
        expect(orders.first.association(:user)).to be_loaded
        expect(orders.first.association(:line_items)).to be_loaded
      end
    end

    describe "GET /admin/users" do
      before do
        create_list(:user, 25)
      end

      it "paginates users list" do
        get admin_users_path

        expect(response).to have_http_status(:success)
        expect(assigns(:pagy)).to be_present
        expect(assigns(:users).count).to eq(20)  # Admin users page size
      end
    end

    describe "GET /admin/products" do
      before do
        create_list(:product, 25)
      end

      it "paginates products list" do
        get admin_products_path

        expect(response).to have_http_status(:success)
        expect(assigns(:pagy)).to be_present
        expect(assigns(:products).count).to eq(20)  # Admin products page size
      end
    end
  end

  describe "Pagination edge cases" do
    before do
      sign_in user
    end

    it "handles empty collections" do
      Product.destroy_all

      get products_path

      expect(response).to have_http_status(:success)
      expect(assigns(:products)).to be_empty
      expect(assigns(:pagy).count).to eq(0)
    end

    it "handles single item collections" do
      Product.destroy_all
      create(:product, status: "active")

      get products_path

      expect(response).to have_http_status(:success)
      expect(assigns(:products).count).to eq(1)
      expect(assigns(:pagy).pages).to eq(1)
    end

    it "handles exact page boundary" do
      Product.destroy_all
      create_list(:product, 12, status: "active")  # Exactly one page

      get products_path

      expect(response).to have_http_status(:success)
      expect(assigns(:products).count).to eq(12)
      expect(assigns(:pagy).pages).to eq(1)
    end
  end

  describe "Pagination performance" do
    before do
      sign_in admin_user
    end

    it "performs efficiently with large datasets" do
      create_list(:support_ticket, 1000, user: user)

      expect {
        get admin_support_tickets_path
      }.to perform_under(1.second)

      expect(response).to have_http_status(:success)
      expect(assigns(:support_tickets).count).to eq(25)
    end

    it "uses efficient queries with includes" do
      create_list(:support_ticket, 10, user: user)

      expect {
        get admin_support_tickets_path
      }.to make_database_queries(count: be < 10)  # Should not have N+1 queries
    end
  end

  describe "Pagination headers and metadata" do
    before do
      sign_in user
      create_list(:product, 15, status: "active")
    end

    it "provides correct pagination metadata" do
      get products_path

      pagy = assigns(:pagy)
      expect(pagy.count).to eq(15)
      expect(pagy.pages).to eq(2)
      expect(pagy.page).to eq(1)
      expect(pagy.items).to eq(12)
      expect(pagy.from).to eq(1)
      expect(pagy.to).to eq(12)
    end

    it "calculates correct metadata for second page" do
      get products_path, params: {page: 2}

      pagy = assigns(:pagy)
      expect(pagy.page).to eq(2)
      expect(pagy.from).to eq(13)
      expect(pagy.to).to eq(15)
    end
  end

  describe "API pagination" do
    before do
      sign_in user
      create_list(:product, 15, status: "active")
    end

    it "works with JSON requests" do
      get products_path, params: {page: 1}, headers: {"Accept" => "application/json"}

      expect(response).to have_http_status(:success)
      expect(response.content_type).to include("application/json")
    end
  end
end
