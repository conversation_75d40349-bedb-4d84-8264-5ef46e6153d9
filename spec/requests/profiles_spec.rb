require 'rails_helper'

RSpec.describe "Profiles", type: :request do
  let(:user) { create(:user, :active) }
  let(:wallet) { user.wallet }

  before do
    sign_in user
  end

  describe "GET /profile" do
    it "returns http success" do
      get profile_path
      expect(response).to have_http_status(:success)
    end

    it "displays user information" do
      get profile_path
      expect(response.body).to include(user.email)
      expect(response.body).to include(wallet.points.to_s)
    end

    it "shows wallet activities when present" do
      wallet.credit(100, context: nil)
      get profile_path
      expect(response.body).to include("Points Earned")
      expect(response.body).to include("+100 pts")
    end

    it "shows recent sales when present" do
      sale = create(:sale, user: user, status: 'approved')
      get profile_path
      expect(response.body).to include(sale.product.name)
      expect(response.body).to include("+#{sale.points} pts")
    end

    it "shows recent orders when present" do
      order = create(:order, user: user)
      get profile_path
      expect(response.body).to include("Order ##{order.id}")
      expect(response.body).to include("-#{order.points} pts")
    end
  end

  describe "GET /profile/edit" do
    it "returns http success" do
      get edit_profile_path
      expect(response).to have_http_status(:success)
    end

    it "displays edit form" do
      get edit_profile_path
      expect(response.body).to include("Edit Profile")
      expect(response.body).to include(user.email)
    end
  end

  describe "PATCH /profile" do
    context "with valid address parameters" do
      let(:valid_params) do
        {
          user: {
            address_attributes: {
              street: "123 Main St",
              city: "Test City",
              postal_code: "12345"
            }
          }
        }
      end

      it "updates the user's address" do
        patch profile_path, params: valid_params
        expect(response).to redirect_to(profile_path)
        follow_redirect!
        expect(response.body).to include("Profile updated successfully")
        
        user.reload
        expect(user.address.street).to eq("123 Main St")
        expect(user.address.city).to eq("Test City")
        expect(user.address.postal_code).to eq("12345")
      end
    end

    context "with invalid parameters" do
      let(:invalid_params) do
        {
          user: {
            address_attributes: {
              street: "",
              city: "",
              postal_code: ""
            }
          }
        }
      end

      it "renders edit template with errors" do
        patch profile_path, params: invalid_params
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  context "when user is not signed in" do
    before { sign_out user }

    it "redirects to sign in page" do
      get profile_path
      expect(response).to redirect_to(new_user_session_path)
    end
  end
end