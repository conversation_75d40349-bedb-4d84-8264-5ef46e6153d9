# frozen_string_literal: true

require "rails_helper"

describe "Admin::BaseController requests", type: :request do
  let(:regular_user) { create(:user, role: :regular) }
  let(:admin_user) { create(:user, role: :admin) }
  let(:super_admin_user) { create(:user, role: :super_admin) }

  context "when user is not signed in" do
    it "redirects to sign in page" do
      get "/admin"
      expect(response).to redirect_to(new_user_session_path)
    end
  end

  context "when user is signed in but not admin" do
    before { sign_in regular_user }
    it "redirects to root path with access denied message" do
      get "/admin"
      expect(response).to redirect_to(root_path)
      expect(flash[:alert]).to eq("Access denied. Admin privileges required.")
    end
  end

  context "when user is admin" do
    before { sign_in admin_user }
    it "allows access" do
      get "/admin"
      expect(response).to have_http_status(:ok)
    end
  end

  context "when user is super admin" do
    before { sign_in super_admin_user }
    it "allows access" do
      get "/admin"
      expect(response).to have_http_status(:ok)
    end
  end
end
