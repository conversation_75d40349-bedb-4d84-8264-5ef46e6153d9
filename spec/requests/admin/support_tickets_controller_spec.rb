require "rails_helper"

describe "Admin::SupportTicketsController requests", type: :request do
  let(:admin_user) { create(:user, role: :admin) }
  let(:regular_user) { create(:user, role: :regular) }
  let!(:support_ticket) { create(:support_ticket, user: regular_user) }

  before { sign_in admin_user }

  describe "GET /admin/support_tickets" do
    it "returns http success" do
      get "/admin/support_tickets"
      expect(response).to have_http_status(:success)
    end
    # Add more request-level tests as needed, e.g. filters, pagination, etc.
  end
end
