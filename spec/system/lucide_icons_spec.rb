require "rails_helper"

RSpec.describe "Lucide Icons", type: :system, js: true do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user) }

  describe "Icon initialization" do
    it "loads Lucide library correctly" do
      visit "/test/lucide"

      expect(page).to have_content("Lucide Icons Test")

      # Check that Lucide is available in the browser
      lucide_available = page.evaluate_script("!!window.lucide")
      expect(lucide_available).to be true

      # Check that icons are found
      icons_count = page.evaluate_script('document.querySelectorAll("[data-lucide]").length')
      expect(icons_count).to be > 0
    end

    it "initializes icons after page load" do
      visit "/test/lucide"

      # Wait for JavaScript to execute
      expect(page).to have_content("Lucide loaded: Yes ✓")

      # Check that icons are properly rendered (Lucide replaces <i> with <svg>)
      expect(page).to have_css("svg[data-lucide]", wait: 2)
    end
  end

  describe "User interface icons" do
    before do
      sign_in_user(user)
    end

    describe "Bottom navigation icons" do
      it "displays all navigation icons correctly" do
        visit root_path

        within(".bottom-navigation") do
          # Dashboard icon
          expect(page).to have_css('[data-lucide="layout-dashboard"]')

          # Add Sale icon
          expect(page).to have_css('[data-lucide="plus-circle"]')

          # Shop icon
          expect(page).to have_css('[data-lucide="shopping-bag"]')

          # Orders icon
          expect(page).to have_css('[data-lucide="clipboard-list"]')

          # Help icon
          expect(page).to have_css('[data-lucide="help-circle"]')
        end
      end

      it "renders icons as SVG elements after initialization" do
        visit root_path

        # Wait for Lucide to initialize
        sleep 1

        within(".bottom-navigation") do
          expect(page).to have_css("svg", count: 5)  # All 5 nav icons should be SVGs
        end
      end
    end

    describe "Top navigation icons" do
      it "shows correct icons in top navigation" do
        visit products_path

        within(".top-navigation") do
          # Back arrow
          expect(page).to have_css('[data-lucide="arrow-left"]')

          # Cart icon
          expect(page).to have_css('[data-lucide="shopping-cart"]')

          # User profile icon
          expect(page).to have_css('[data-lucide="user"]')
        end
      end
    end

    describe "Page-specific icons" do
      it "displays correct icons on dashboard" do
        visit root_path

        # Quick action icons
        expect(page).to have_css('[data-lucide="plus"]')  # Add sale
        expect(page).to have_css('[data-lucide="shopping-bag"]')  # Shop
      end

      it "displays correct icons on products page" do
        create(:product, status: "active")
        visit products_path

        # Search icon
        expect(page).to have_css('[data-lucide="search"]')

        # Product action icons
        expect(page).to have_css('[data-lucide="eye"]')  # View
        expect(page).to have_css('[data-lucide="shopping-cart"]')  # Add to cart
      end

      it "displays correct icons on profile page" do
        visit profile_path

        # Edit icon
        expect(page).to have_css('[data-lucide="edit"]')

        # Building icon for store info
        expect(page).to have_css('[data-lucide="building"]')

        # Coins icon for points
        expect(page).to have_css('[data-lucide="coins"]')
      end

      it "displays correct icons on help pages" do
        visit help_index_path

        # Message icon for contact
        expect(page).to have_css('[data-lucide="message-circle-question"]')

        # Chevron icons for FAQ
        expect(page).to have_css('[data-lucide="chevron-down"]')

        visit help_contact_path

        # Contact method icons
        expect(page).to have_css('[data-lucide="mail"]')
        expect(page).to have_css('[data-lucide="phone"]')
      end
    end
  end

  describe "Admin interface icons" do
    before do
      sign_in_user(admin_user)
    end

    describe "Admin navigation icons" do
      it "displays all admin navigation icons" do
        visit admin_root_path

        within(".admin-navigation") do
          expect(page).to have_css('[data-lucide="layout-dashboard"]')  # Dashboard
          expect(page).to have_css('[data-lucide="clipboard-list"]')   # Sales
          expect(page).to have_css('[data-lucide="users"]')            # Users
          expect(page).to have_css('[data-lucide="shopping-cart"]')    # Orders
          expect(page).to have_css('[data-lucide="store"]')            # Stores
          expect(page).to have_css('[data-lucide="package"]')          # Products
          expect(page).to have_css('[data-lucide="percent"]')          # Promotions
          expect(page).to have_css('[data-lucide="gift"]')             # Gift Cards
          expect(page).to have_css('[data-lucide="message-circle-question"]')  # Support
        end
      end

      it "renders admin icons as SVG after initialization" do
        visit admin_root_path

        # Wait for Lucide to initialize
        sleep 1

        within(".admin-navigation") do
          expect(page).to have_css("svg[data-lucide]", minimum: 8)
        end
      end
    end

    describe "Admin page icons" do
      it "displays icons on support tickets page" do
        create(:support_ticket, user: user)
        visit admin_support_tickets_path

        # Should have various action icons
        expect(page).to have_css("[data-lucide]", minimum: 1)
      end
    end
  end

  describe "Icon consistency across pages" do
    before do
      sign_in_user(user)
    end

    it "maintains consistent icon usage across navigation" do
      # Test that same icons are used consistently
      pages_to_test = [
        root_path,
        products_path,
        orders_path,
        profile_path,
        help_index_path
      ]

      pages_to_test.each do |page_path|
        visit page_path

        # Bottom navigation should always have the same icons
        within(".bottom-navigation") do
          expect(page).to have_css('[data-lucide="layout-dashboard"]')
          expect(page).to have_css('[data-lucide="plus-circle"]')
          expect(page).to have_css('[data-lucide="shopping-bag"]')
          expect(page).to have_css('[data-lucide="clipboard-list"]')
          expect(page).to have_css('[data-lucide="help-circle"]')
        end
      end
    end
  end

  describe "Icon accessibility" do
    before do
      sign_in_user(user)
    end

    it "provides proper icon sizing" do
      visit root_path

      # Check that icons have proper CSS classes for sizing
      icon_elements = page.all("[data-lucide]")

      icon_elements.each do |element|
        classes = element[:class]
        expect(classes).to match(/w-\d+/)  # Width class
        expect(classes).to match(/h-\d+/)  # Height class
      end
    end

    it "maintains icon visibility with different color schemes" do
      visit root_path

      # Icons should be visible (not transparent)
      icon_elements = page.all("[data-lucide]")

      icon_elements.each do |element|
        opacity = page.evaluate_script("window.getComputedStyle(arguments[0]).opacity", element)
        expect(opacity.to_f).to be > 0
      end
    end
  end

  describe "Icon performance" do
    it "loads icons efficiently" do
      start_time = Time.current

      visit root_path

      # Wait for all icons to be initialized
      expect(page).to have_css("svg[data-lucide]", wait: 5)

      load_time = Time.current - start_time
      expect(load_time).to be < 3.seconds  # Should load reasonably fast
    end

    it "handles page transitions without icon flickering" do
      sign_in_user(user)

      visit root_path
      expect(page).to have_css('[data-lucide="layout-dashboard"]')

      # Navigate to another page
      click_link "Shop"
      expect(page).to have_css('[data-lucide="search"]')

      # Navigate back
      within(".bottom-navigation") do
        click_link "Dashboard"
      end

      # Icons should still be present
      expect(page).to have_css('[data-lucide="layout-dashboard"]')
    end
  end

  describe "Turbo navigation compatibility" do
    before do
      sign_in_user(user)
    end

    it "reinitializes icons after Turbo navigation" do
      visit root_path

      # Ensure icons are loaded
      expect(page).to have_css("svg[data-lucide]")

      # Navigate using Turbo
      click_link "Shop"

      # Icons should still work on new page
      expect(page).to have_css("svg[data-lucide]")

      # Navigate back
      within(".bottom-navigation") do
        click_link "Dashboard"
      end

      # Icons should still work
      expect(page).to have_css("svg[data-lucide]")
    end
  end

  describe "Error handling" do
    it "gracefully handles missing icon names" do
      # This would need to be tested with a custom page that has invalid icon names
      # For now, we'll test that valid icons work correctly
      visit "/test/lucide"

      # Should not have JavaScript errors
      logs = page.driver.browser.logs.get(:browser)
      error_logs = logs.select { |log| log.level == "SEVERE" }

      # Filter out non-icon related errors
      icon_errors = error_logs.select { |log| log.message.include?("lucide") }
      expect(icon_errors).to be_empty
    end
  end

  describe "Mobile responsiveness" do
    before do
      page.driver.browser.manage.window.resize_to(375, 667)  # iPhone SE size
      sign_in_user(user)
    end

    it "displays icons correctly on mobile" do
      visit root_path

      within(".bottom-navigation") do
        expect(page).to have_css("[data-lucide]", count: 5)
      end

      # Icons should be properly sized for mobile
      icon_elements = page.all("[data-lucide]")
      icon_elements.each do |element|
        # Should have mobile-appropriate sizing classes
        classes = element[:class]
        expect(classes).to include("w-5 h-5").or include("w-4 h-4").or include("w-6 h-6")
      end
    end
  end
end
