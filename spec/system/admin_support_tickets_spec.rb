require "rails_helper"

RSpec.describe "Admin Support Tickets", type: :system do
  let(:admin_user) { create(:user) }
  let(:regular_user) { create(:user) }
  let!(:open_ticket) { create(:support_ticket, user: regular_user, status: "open", category: "sales") }
  let!(:resolved_ticket) { create(:support_ticket, user: regular_user, status: "resolved", category: "orders") }
  let!(:high_priority_ticket) { create(:support_ticket, user: regular_user, priority: "high", category: "account") }

  before do
    sign_in_user(admin_user)
  end

  describe "Support tickets navigation" do
    it "shows support tickets link in admin navigation" do
      visit admin_root_path

      within(".admin-navigation") do
        expect(page).to have_link("Support Tickets", href: admin_support_tickets_path)
        expect(page).to have_css('[data-lucide="message-circle-question"]')
      end
    end

    it "shows open ticket count badge" do
      visit admin_root_path

      open_count = SupportTicket.open.admin_handled.count
      if open_count > 0
        expect(page).to have_content(open_count.to_s)
      end
    end
  end

  describe "Tickets index page" do
    before do
      visit admin_support_tickets_path
    end

    it "displays the tickets list" do
      expect(page).to have_content("Support Tickets")
      expect(page).to have_content("Manage user questions and support requests")
    end

    it "shows status filter tabs" do
      expect(page).to have_link("All Tickets")
      expect(page).to have_link("Open")
      expect(page).to have_link("In Progress")
      expect(page).to have_link("Resolved")
    end

    it "displays ticket information" do
      within(".tickets-list") do
        expect(page).to have_content("##{open_ticket.id}")
        expect(page).to have_content(open_ticket.subject)
        expect(page).to have_content(open_ticket.user.email)
        expect(page).to have_content(open_ticket.category_name)
      end
    end

    it "shows status and priority badges" do
      expect(page).to have_css(".status-badge")
      expect(page).to have_css(".priority-badge")
    end

    context "filtering tickets" do
      it "filters by status" do
        click_link "Open"

        expect(current_url).to include("status=open")
        expect(page).to have_content(open_ticket.subject)
        expect(page).not_to have_content(resolved_ticket.subject)
      end

      it "filters by resolved status" do
        click_link "Resolved"

        expect(current_url).to include("status=resolved")
        expect(page).to have_content(resolved_ticket.subject)
        expect(page).not_to have_content(open_ticket.subject)
      end
    end

    it "shows pagination when there are many tickets" do
      # Create enough tickets to trigger pagination
      create_list(:support_ticket, 30, user: regular_user)

      visit admin_support_tickets_path

      expect(page).to have_css(".pagination") if SupportTicket.count > 25
    end
  end

  describe "Ticket detail page" do
    before do
      visit admin_support_ticket_path(open_ticket)
    end

    it "displays ticket details" do
      expect(page).to have_content("Support Ticket ##{open_ticket.id}")
      expect(page).to have_content(open_ticket.subject)
      expect(page).to have_content(open_ticket.message)
    end

    it "shows user information sidebar" do
      within(".user-info-sidebar") do
        expect(page).to have_content("User Information")
        expect(page).to have_content(open_ticket.user.email)
        expect(page).to have_content("##{open_ticket.user.id}")
        expect(page).to have_content("Points Balance")
        expect(page).to have_content("Account Status")
      end
    end

    it "displays ticket metadata" do
      within(".ticket-details-sidebar") do
        expect(page).to have_content("Ticket Details")
        expect(page).to have_content(open_ticket.category_name)
        expect(page).to have_content(open_ticket.created_at.strftime("%B %d, %Y"))
      end
    end

    it "shows status and priority update forms" do
      expect(page).to have_select("support_ticket_status")
      expect(page).to have_select("support_ticket_priority")
      expect(page).to have_button("Update")
    end

    context "updating ticket status" do
      it "allows changing ticket status" do
        select "In Progress", from: "support_ticket_status"
        click_button "Update"

        expect(page).to have_content("updated successfully")
        open_ticket.reload
        expect(open_ticket.status).to eq("in_progress")
      end

      it "allows changing ticket priority" do
        select "High", from: "support_ticket_priority"
        click_button "Update"

        expect(page).to have_content("updated successfully")
        open_ticket.reload
        expect(open_ticket.priority).to eq("high")
      end
    end

    context "responding to tickets" do
      it "shows response form" do
        expect(page).to have_content("Send Response")
        expect(page).to have_field("support_ticket_admin_response")
        expect(page).to have_button("Send Response")
      end

      it "allows sending a response" do
        response_text = "Thank you for your question. Here's how to resolve your issue..."

        fill_in "support_ticket_admin_response", with: response_text

        expect {
          click_button "Send Response"
        }.to change { ActionMailer::Base.deliveries.count }.by(1)

        expect(page).to have_content("Response sent successfully")

        open_ticket.reload
        expect(open_ticket.admin_response).to eq(response_text)
        expect(open_ticket.admin_user).to eq(admin_user)
        expect(open_ticket.responded_at).to be_present
      end

      it "validates response content" do
        fill_in "support_ticket_admin_response", with: ""
        click_button "Send Response"

        expect(page).to have_content("Failed to send response")
      end
    end

    context "ticket with existing response" do
      let!(:responded_ticket) { create(:support_ticket, :with_admin_response, user: regular_user) }

      before do
        visit admin_support_ticket_path(responded_ticket)
      end

      it "shows existing response" do
        expect(page).to have_content("Your Response")
        expect(page).to have_content(responded_ticket.admin_response)
        expect(page).to have_content("Response by")
        expect(page).to have_content(responded_ticket.responded_at.strftime("%B %d, %Y"))
      end

      it "allows updating the response" do
        expect(page).to have_content("Update Response")
        expect(page).to have_field("support_ticket_admin_response", with: responded_ticket.admin_response)
      end
    end
  end

  describe "Ticket workflow" do
    it "supports complete ticket resolution workflow" do
      # Start with open ticket
      visit admin_support_ticket_path(open_ticket)

      # Update status to in progress
      select "In Progress", from: "support_ticket_status"
      click_button "Update"

      expect(page).to have_content("updated successfully")

      # Add response
      response_text = "I've reviewed your question about points calculation. Here's the explanation..."
      fill_in "support_ticket_admin_response", with: response_text
      click_button "Send Response"

      expect(page).to have_content("Response sent successfully")

      # Verify ticket is now resolved
      open_ticket.reload
      expect(open_ticket.status).to eq("resolved")
      expect(open_ticket.admin_response).to eq(response_text)
    end
  end

  describe "Responsive admin interface" do
    it "works on tablet viewport" do
      page.driver.browser.manage.window.resize_to(768, 1024)  # iPad size

      visit admin_support_tickets_path

      expect(page).to have_content("Support Tickets")

      # Click on a ticket
      click_link "##{open_ticket.id}"

      expect(page).to have_content(open_ticket.subject)
      expect(page).to have_field("support_ticket_admin_response")
    end
  end

  describe "Email notifications" do
    it "sends email when admin responds to ticket" do
      visit admin_support_ticket_path(open_ticket)

      fill_in "support_ticket_admin_response", with: "Here is your answer..."

      expect {
        click_button "Send Response"
      }.to change { ActionMailer::Base.deliveries.count }.by(1)

      email = ActionMailer::Base.deliveries.last
      expect(email.to).to include(open_ticket.user.email)
      expect(email.subject).to include(open_ticket.subject)
    end
  end
end
