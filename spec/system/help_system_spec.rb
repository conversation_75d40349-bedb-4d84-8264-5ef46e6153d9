require "rails_helper"

RSpec.describe "Help System", type: :system do
  let(:user) { create(:user) }

  before do
    sign_in_user(user)
  end

  describe "Help navigation" do
    it "shows help tab in bottom navigation" do
      visit root_path

      within(".bottom-navigation") do
        expect(page).to have_link("Help", href: help_index_path)
        expect(page).to have_css('[data-lucide="help-circle"]')
      end
    end

    it "navigates to help page from bottom navigation" do
      visit root_path

      within(".bottom-navigation") do
        click_link "Help"
      end

      expect(current_path).to eq(help_index_path)
      expect(page).to have_content("Help & Support")
    end
  end

  describe "FAQ page" do
    before do
      visit help_index_path
    end

    it "displays the help page header" do
      expect(page).to have_content("Help & Support")
      expect(page).to have_content("Find answers and get assistance")
    end

    it "shows the contact support button" do
      expect(page).to have_link("Ask a Question", href: help_contact_path)
      expect(page).to have_content("Get personalized help from our team")
    end

    it "displays FAQ section" do
      expect(page).to have_content("Frequently Asked Questions")
      expect(page).to have_content("How do I earn points?")
      expect(page).to have_content("How do I redeem my points?")
      expect(page).to have_content("Why is my sale still pending?")
    end

    it "allows expanding and collapsing FAQ items", js: true do
      # Find the first FAQ question
      first_question = page.find(".faq-toggle", match: :first)
      first_question.click

      # Check that content becomes visible
      expect(page).to have_css(".faq-content:not(.hidden)", wait: 1)

      # Click again to collapse
      first_question.click
      expect(page).to have_css(".faq-content.hidden", wait: 1)
    end

    it "displays app information section" do
      expect(page).to have_content("App Information")
      expect(page).to have_content("Version")
      expect(page).to have_content("1.0.0")
      expect(page).to have_content("Last Updated")
    end

    it "shows proper navigation with back button" do
      expect(page).to have_css('[data-lucide="arrow-left"]')
      expect(page).to have_link(href: root_path)
    end
  end

  describe "Contact form" do
    before do
      visit help_contact_path
    end

    it "displays the contact form" do
      expect(page).to have_content("Contact Support")
      expect(page).to have_content("Send us your question")
      expect(page).to have_content("We typically respond within 24 hours")
    end

    it "shows all form fields" do
      expect(page).to have_select("question_category")
      expect(page).to have_field("question_subject")
      expect(page).to have_field("question_message")
    end

    it "displays user information" do
      expect(page).to have_content("Your Information")
      expect(page).to have_content(user.email)
      expect(page).to have_content("##{user.id}")
    end

    it "shows category options" do
      expect(page).to have_select("question_category", with_options: [
        "General Question",
        "Sales & Points",
        "Orders & Redemptions",
        "Account & Profile",
        "Technical Issue",
        "App Bug Report",
        "Feature Request"
      ])
    end

    context "submitting a valid question" do
      it "creates a support ticket and shows success message" do
        select "Sales & Points", from: "question_category"
        fill_in "question_subject", with: "Question about point calculation"
        fill_in "question_message", with: "I submitted a sale but the points don't match what I expected. Can you help me understand how points are calculated?"

        expect {
          click_button "Send Message"
        }.to change(SupportTicket, :count).by(1)

        expect(current_path).to eq(help_index_path)
        expect(page).to have_content("submitted to our support team")

        # Verify ticket was created correctly
        ticket = SupportTicket.last
        expect(ticket.user).to eq(user)
        expect(ticket.category).to eq("sales")
        expect(ticket.subject).to eq("Question about point calculation")
      end
    end

    context "submitting invalid data" do
      it "shows validation errors" do
        fill_in "question_subject", with: "Hi"  # Too short
        fill_in "question_message", with: "Help"  # Too short

        click_button "Send Message"

        expect(page).to have_content("error submitting")
        expect(current_path).to eq(help_submit_question_path)
      end
    end

    it "displays alternative contact methods" do
      expect(page).to have_content("Other Ways to Reach Us")
      expect(page).to have_content("Email Support")
      expect(page).to have_content("<EMAIL>")
      expect(page).to have_content("Phone Support")
      expect(page).to have_content("1-800-ZEISS-PT")
    end
  end

  describe "Help integration in profile" do
    it "shows help link in profile menu" do
      visit profile_path

      # Look for help link in profile actions
      expect(page).to have_link("Help & Support", href: help_index_path)
      expect(page).to have_css('[data-lucide="help-circle"]')
    end
  end

  describe "Responsive design" do
    it "works on mobile viewport" do
      page.driver.browser.manage.window.resize_to(375, 667)  # iPhone SE size

      visit help_index_path

      expect(page).to have_content("Help & Support")
      expect(page).to have_link("Ask a Question")

      # Test contact form on mobile
      click_link "Ask a Question"
      expect(page).to have_field("question_subject")
      expect(page).to have_field("question_message")
    end
  end

  describe "Navigation flow" do
    it "allows complete help flow navigation" do
      # Start from dashboard
      visit root_path

      # Go to help via bottom nav
      within(".bottom-navigation") do
        click_link "Help"
      end

      expect(current_path).to eq(help_index_path)

      # Go to contact form
      click_link "Ask a Question"
      expect(current_path).to eq(help_contact_path)

      # Go back to help index
      click_link "", href: help_index_path  # Back button
      expect(current_path).to eq(help_index_path)

      # Go back to dashboard
      click_link "", href: root_path  # Back button
      expect(current_path).to eq(root_path)
    end
  end
end
