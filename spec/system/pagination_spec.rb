require "rails_helper"

RSpec.describe "Pagination", type: :system do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user) }

  describe "User-facing pagination" do
    before do
      sign_in_user(user)
    end

    describe "Products pagination" do
      before do
        # Create enough products to trigger pagination (more than 12)
        create_list(:product, 15, status: "active")
      end

      it "paginates products on shop page" do
        visit products_path

        expect(page).to have_css(".product-card", count: 12)  # First page shows 12 items
        expect(page).to have_css(".pagination")
        expect(page).to have_link("Next")
      end

      it "navigates to next page" do
        visit products_path

        click_link "Next"

        expect(page).to have_css(".product-card", count: 3)  # Remaining 3 items
        expect(page).to have_link("Previous")
      end

      it "maintains search filters across pages" do
        # Create products with specific names
        create(:product, name: "Special Product 1", status: "active")
        create(:product, name: "Special Product 2", status: "active")

        visit products_path
        fill_in "search", with: "Special"
        click_button "Search"

        expect(page).to have_content("Special Product")

        # If pagination appears, test that search is maintained
        if page.has_css?(".pagination")
          click_link "Next"
          expect(page.current_url).to include("search=Special")
        end
      end

      it "shows pagination info on desktop" do
        visit products_path

        within(".pagination") do
          expect(page).to have_content("Showing")
          expect(page).to have_content("to")
          expect(page).to have_content("of")
        end
      end
    end

    describe "Orders pagination" do
      before do
        # Create enough orders to trigger pagination (more than 10)
        create_list(:order, 12, user: user)
      end

      it "paginates user orders" do
        visit orders_path

        expect(page).to have_css(".order-item", count: 10)  # First page shows 10 items
        expect(page).to have_css(".pagination")
      end

      it "navigates between order pages" do
        visit orders_path

        click_link "Next"

        expect(page).to have_css(".order-item", count: 2)  # Remaining 2 items
        expect(page).to have_link("Previous")

        click_link "Previous"
        expect(page).to have_css(".order-item", count: 10)  # Back to first page
      end
    end
  end

  describe "Admin pagination" do
    before do
      sign_in_user(admin_user)
    end

    describe "Support tickets pagination" do
      before do
        # Create enough tickets to trigger pagination (more than 25)
        create_list(:support_ticket, 30, user: user)
      end

      it "paginates support tickets in admin" do
        visit admin_support_tickets_path

        expect(page).to have_css(".ticket-item", count: 25)  # First page shows 25 items
        expect(page).to have_css(".pagination")
        expect(page).to have_content("Showing 1 to 25 of 30 tickets")
      end

      it "navigates to next page of tickets" do
        visit admin_support_tickets_path

        click_link "Next"

        expect(page).to have_css(".ticket-item", count: 5)  # Remaining 5 items
        expect(page).to have_content("Showing 26 to 30 of 30 tickets")
      end

      it "maintains filters across pages" do
        # Create tickets with different statuses
        create_list(:support_ticket, 15, user: user, status: "open")
        create_list(:support_ticket, 15, user: user, status: "resolved")

        visit admin_support_tickets_path
        click_link "Open"  # Filter by open status

        expect(page).to have_css(".ticket-item")

        if page.has_css?(".pagination")
          click_link "Next"
          expect(page.current_url).to include("status=open")
        end
      end
    end

    describe "Orders pagination in admin" do
      before do
        create_list(:order, 30, user: user)
      end

      it "paginates admin orders list" do
        visit admin_orders_path

        expect(page).to have_css(".order-row", count: 25)  # Admin shows 25 per page
        expect(page).to have_css(".pagination")
      end

      it "shows proper pagination controls" do
        visit admin_orders_path

        within(".pagination") do
          expect(page).to have_content("Showing")
          expect(page).to have_content("orders")
          expect(page).to have_link("Next")
        end
      end
    end

    describe "Users pagination in admin" do
      before do
        create_list(:user, 25)  # Create additional users
      end

      it "paginates users list" do
        visit admin_users_path

        expect(page).to have_css(".user-row", count: 20)  # Admin users shows 20 per page
        expect(page).to have_css(".pagination")
      end
    end

    describe "Products pagination in admin" do
      before do
        create_list(:product, 25)
      end

      it "paginates admin products list" do
        visit admin_products_path

        expect(page).to have_css(".product-row", count: 20)  # Admin products shows 20 per page
        expect(page).to have_css(".pagination")
      end
    end
  end

  describe "Pagination edge cases" do
    before do
      sign_in_user(user)
    end

    it "handles empty results gracefully" do
      # Ensure no products exist
      Product.destroy_all

      visit products_path

      expect(page).to have_content("No products available")
      expect(page).not_to have_css(".pagination")
    end

    it "handles single page results" do
      # Create fewer items than pagination threshold
      create_list(:product, 5, status: "active")

      visit products_path

      expect(page).to have_css(".product-card", count: 5)
      expect(page).not_to have_css(".pagination")
    end

    it "handles exact page boundary" do
      # Create exactly the page size
      create_list(:product, 12, status: "active")

      visit products_path

      expect(page).to have_css(".product-card", count: 12)
      expect(page).not_to have_css(".pagination")  # No pagination needed for exact page size
    end
  end

  describe "Mobile pagination" do
    before do
      page.driver.browser.manage.window.resize_to(375, 667)  # iPhone SE size
      sign_in_user(user)
      create_list(:product, 15, status: "active")
    end

    it "shows simplified pagination on mobile" do
      visit products_path

      within(".pagination") do
        # Mobile should show Previous/Next buttons
        expect(page).to have_link("Next")
        expect(page).not_to have_content("Showing")  # Info hidden on mobile
      end
    end

    it "allows navigation on mobile" do
      visit products_path

      click_link "Next"

      expect(page).to have_link("Previous")
      expect(page).to have_css(".product-card")
    end
  end

  describe "Pagination performance" do
    before do
      sign_in_user(admin_user)
    end

    it "loads pages efficiently with large datasets" do
      # Create a large number of tickets
      create_list(:support_ticket, 100, user: user)

      start_time = Time.current
      visit admin_support_tickets_path
      load_time = Time.current - start_time

      expect(load_time).to be < 5.seconds  # Should load reasonably fast
      expect(page).to have_css(".ticket-item", count: 25)
    end
  end
end
