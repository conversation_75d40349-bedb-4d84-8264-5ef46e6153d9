# frozen_string_literal: true

require "rails_helper"

RSpec.describe LandingController, type: :controller do
  describe "GET #index" do
    it "returns a success response" do
      get :index
      expect(response).to be_successful
    end

    it "assigns a new user instance" do
      get :index
      expect(assigns(:user)).to be_a_new(User)
    end

    it "renders the index template" do
      get :index
      expect(response).to render_template(:index)
    end

    context "when user is already signed in" do
      let(:user) { create(:user) }

      before do
        sign_in user
      end

      it "still returns success response" do
        get :index
        expect(response).to be_successful
      end

      it "still assigns a new user instance for the form" do
        get :index
        expect(assigns(:user)).to be_a_new(User)
      end
    end
  end

  describe "routing" do
    it "routes to landing#index" do
      expect(get: "/").to route_to("landing#index")
    end
  end

  describe "layout" do
    it "uses the application layout" do
      get :index
      expect(response).to render_template(layout: "application")
    end
  end

  describe "instance variables" do
    before do
      get :index
    end

    it "sets @user as a new User instance" do
      expect(assigns(:user)).to be_instance_of(User)
      expect(assigns(:user)).to be_new_record
    end

    it "@user has no attributes set" do
      user = assigns(:user)
      expect(user.email).to be_nil
      expect(user.first_name).to be_nil
      expect(user.last_name).to be_nil
    end
  end

  describe "response format" do
    it "responds to HTML requests" do
      get :index
      expect(response.content_type).to include("text/html")
    end

    it "does not respond to JSON requests by default" do
      expect do
        get :index, format: :json
      end.to raise_error(ActionController::UnknownFormat)
    end
  end

  describe "performance" do
    it "does not make unnecessary database queries" do
      expect do
        get :index
      end.not_to exceed_query_limit(0)
    end
  end
end
