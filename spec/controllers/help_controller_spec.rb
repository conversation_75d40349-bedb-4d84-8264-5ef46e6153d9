require "rails_helper"

RSpec.describe Help<PERSON>ontroller, type: :controller do
  let(:user) { create(:user) }

  before do
    sign_in user
  end

  describe "GET #index" do
    it "returns http success" do
      get :index
      expect(response).to have_http_status(:success)
    end

    it "assigns FAQs" do
      get :index
      expect(assigns(:faqs)).to be_present
      expect(assigns(:faqs)).to be_an(Array)
      expect(assigns(:faqs).first).to have_key(:question)
      expect(assigns(:faqs).first).to have_key(:answer)
    end

    it "includes comprehensive FAQ content" do
      get :index
      faqs = assigns(:faqs)

      expect(faqs.map { |faq| faq[:question] }).to include(
        "How do I earn points?",
        "How do I redeem my points?",
        "Why is my sale still pending?",
        "How do I track my orders?"
      )
    end
  end

  describe "GET #contact" do
    it "returns http success" do
      get :contact
      expect(response).to have_http_status(:success)
    end

    it "renders the contact form" do
      get :contact
      expect(response).to render_template(:contact)
    end
  end

  describe "POST #submit_question" do
    let(:valid_params) do
      {
        question: {
          subject: "Test question about sales",
          message: "This is a detailed test message about my sales question",
          category: "sales"
        }
      }
    end

    let(:invalid_params) do
      {
        question: {
          subject: "Hi",
          message: "Short",
          category: "sales"
        }
      }
    end

    context "with valid parameters" do
      it "creates a new support ticket" do
        expect {
          post :submit_question, params: valid_params
        }.to change(SupportTicket, :count).by(1)
      end

      it "sets correct ticket attributes" do
        post :submit_question, params: valid_params

        ticket = SupportTicket.last
        expect(ticket.user).to eq(user)
        expect(ticket.subject).to eq("Test question about sales")
        expect(ticket.category).to eq("sales")
        expect(ticket.status).to eq("open")
        expect(ticket.priority).to eq("normal")
      end

      context "for admin-handled categories" do
        it "sends admin notification email" do
          expect {
            post :submit_question, params: valid_params
          }.to change { ActionMailer::Base.deliveries.count }.by(1)
        end

        it "shows admin notification message" do
          post :submit_question, params: valid_params
          expect(flash[:notice]).to include("submitted to our support team")
        end
      end

      context "for self-service categories" do
        let(:self_service_params) do
          valid_params.deep_merge(question: {category: "technical"})
        end

        it "sends auto-response email" do
          expect {
            post :submit_question, params: self_service_params
          }.to change { ActionMailer::Base.deliveries.count }.by(1)
        end

        it "shows auto-response message" do
          post :submit_question, params: self_service_params
          expect(flash[:notice]).to include("sent some helpful information")
        end
      end

      it "redirects to help index" do
        post :submit_question, params: valid_params
        expect(response).to redirect_to(help_index_path)
      end
    end

    context "with invalid parameters" do
      it "does not create a support ticket" do
        expect {
          post :submit_question, params: invalid_params
        }.not_to change(SupportTicket, :count)
      end

      it "renders the contact form with errors" do
        post :submit_question, params: invalid_params
        expect(response).to render_template(:contact)
        expect(flash[:alert]).to include("error submitting")
      end
    end

    it "requires authentication" do
      sign_out user
      post :submit_question, params: valid_params
      expect(response).to redirect_to(new_user_session_path)
    end
  end
end
