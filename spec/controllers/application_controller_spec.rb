# frozen_string_literal: true

require "rails_helper"
require "warden/test/helpers"

RSpec.describe ApplicationController, type: :controller do
  include Warden::Test::Helpers

  before(:all) { Warden.test_mode! }
  after(:all) { Warden.test_reset! }
  after { Warden.test_reset! }
  controller do
    def index
      render plain: "test"
    end

    def test_current_cart
      cart = current_cart
      render json: {cart_id: cart.id}
    end
  end

  before do
    routes.draw do
      get "index" => "anonymous#index"
      get "test_current_cart" => "anonymous#test_current_cart"
      get "test_barcode" => "anonymous#test_barcode"
      get "lucide_test" => "anonymous#lucide_test"
    end
  end

  describe "browser compatibility" do
    it "allows modern browsers" do
      request.headers["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      get :index
      expect(response).to have_http_status(:ok)
    end

    it "blocks outdated browsers" do
      # Simulate an outdated browser user agent (e.g., IE 11)
      request.headers["User-Agent"] = "Mozilla/5.0 (Windows NT 6.1; Trident/7.0; rv:11.0) like Gecko"
      get :index
      expect(response).to have_http_status(:not_acceptable)
      expect(response.body).to include("unsupported browser").or include("Unsupported Browser").or include("406")
    end
  end

  # The handle_outdated_browser method is tested indirectly via the browser compatibility spec above.

  describe "#current_cart" do
    context "when user is signed in" do
      let(:user) { create(:user) }

      before do
        login_as(user, scope: :user)
      end

      context "when user has a cart" do
        it "returns the user cart" do
          user.reload  # Ensure association is fresh
          expected_cart_id = user.cart.id
          get :test_current_cart
          json_response = JSON.parse(response.body)
          expect(json_response["cart_id"]).to eq(expected_cart_id)
        end
      end

      context "when user does not have a cart" do
        before do
          user.cart&.destroy
          user.reload
        end

        it "creates and returns a new cart for the user" do
          expect do
            get :test_current_cart
          end.to change(Cart, :count).by(1)

          expect(user.reload.cart).to be_present
        end
      end
    end

    context "when user is not signed in" do
      context "when session has cart_id" do
        let(:cart) { create(:cart) }

        before do
          session[:cart_id] = cart.id
        end

        it "returns the cart from session" do
          get :test_current_cart
          json_response = JSON.parse(response.body)
          expect(json_response["cart_id"]).to eq(cart.id)
        end
      end

      context "when session cart_id is invalid" do
        before do
          session[:cart_id] = 99999 # Non-existent cart ID
        end

        it "creates a new cart and stores ID in session" do
          expect do
            get :test_current_cart
          end.to change(Cart, :count).by(1)

          json_response = JSON.parse(response.body)
          new_cart_id = json_response["cart_id"]
          expect(session[:cart_id]).to eq(new_cart_id)
        end
      end

      context "when no cart_id in session" do
        it "creates a new cart and stores ID in session" do
          expect do
            get :test_current_cart
          end.to change(Cart, :count).by(1)

          json_response = JSON.parse(response.body)
          new_cart_id = json_response["cart_id"]
          expect(session[:cart_id]).to eq(new_cart_id)
        end
      end
    end
  end

  describe "#after_sign_in_path_for" do
    let(:user) { create(:user) }

    it "redirects to authenticated root path" do
      expect(controller.send(:after_sign_in_path_for, user)).to eq(authenticated_root_path)
    end
  end

  describe "test routes" do
    describe "#test_barcode" do
      it "renders barcode test page without layout" do
        get :test_barcode
        expect(response).to be_successful
        expect(response).to render_template("test/barcode")
      end
    end

    describe "#lucide_test" do
      it "renders lucide test page with application layout" do
        get :lucide_test
        expect(response).to be_successful
        expect(response).to render_template("test/lucide_test")
      end
    end
  end

  describe "helper methods" do
    it "makes current_cart available as helper method" do
      expect(controller._helper_methods).to include(:current_cart)
    end
  end
end
