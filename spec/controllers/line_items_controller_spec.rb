# frozen_string_literal: true

require "rails_helper"

RSpec.describe LineItemsController, type: :controller do
  let(:user) { create(:user) }
  let(:brand) { create(:brand) }
  let(:store) { create(:store, brand: brand) }
  let(:category) { create(:category, brand: brand) }
  let(:product) { create(:product, category: category) }
  let(:cart) { create(:cart, user: user) }

  before do
    user.update!(store: store)
    sign_in user
    allow(controller).to receive(:current_cart).and_return(cart)
  end

  describe "authentication" do
    context "when user is not signed in" do
      before do
        sign_out user
      end

      it "redirects to sign in page for create action" do
        post :create, params: {product_id: product.id}
        expect(response).to redirect_to(new_user_session_path)
      end

      it "redirects to sign in page for update action" do
        line_item = create(:line_item, cart: cart)
        patch :update, params: {id: line_item.id, line_item: {quantity: 2}}
        expect(response).to redirect_to(new_user_session_path)
      end

      it "redirects to sign in page for destroy action" do
        line_item = create(:line_item, cart: cart)
        delete :destroy, params: {id: line_item.id}
        expect(response).to redirect_to(new_user_session_path)
      end
    end
  end

  describe "POST #create" do
    context "when user has no store" do
      before do
        user.update!(store: nil)
      end

      it "redirects with error message" do
        post :create, params: {product_id: product.id}
        expect(response).to redirect_to(root_path)
        expect(flash[:alert]).to eq("No store or brand associated with your account.")
      end
    end

    context "when user has no brand" do
      before do
        store.update!(brand: nil)
      end

      it "redirects with error message" do
        post :create, params: {product_id: product.id}
        expect(response).to redirect_to(root_path)
        expect(flash[:alert]).to eq("No store or brand associated with your account.")
      end
    end

    context "when product is not found" do
      it "redirects with error message" do
        post :create, params: {product_id: 99999}
        expect(response).to redirect_to(products_path)
        expect(flash[:alert]).to eq("Product not found or not available for your store.")
      end
    end

    context "when product is from different brand" do
      let(:other_brand) { create(:brand) }
      let(:other_category) { create(:category, brand: other_brand) }
      let(:other_product) { create(:product, category: other_category) }

      it "redirects with error message" do
        post :create, params: {product_id: other_product.id}
        expect(response).to redirect_to(products_path)
        expect(flash[:alert]).to eq("Product not found or not available for your store.")
      end
    end

    context "when adding new product to cart" do
      it "creates new line item" do
        expect do
          post :create, params: {product_id: product.id}
        end.to change(LineItem, :count).by(1)
      end

      it "sets quantity to 1" do
        post :create, params: {product_id: product.id}
        line_item = cart.line_items.last
        expect(line_item.quantity).to eq(1)
      end

      it "sets price to 0.0 for points-based ordering" do
        post :create, params: {product_id: product.id}
        line_item = cart.line_items.last
        expect(line_item.price).to eq(0.0)
      end

      it "redirects to cart with success message" do
        post :create, params: {product_id: product.id}
        expect(response).to redirect_to(cart_path(@cart))
        expect(flash[:notice]).to eq("Product added to cart.")
      end
    end

    context "when product already exists in cart" do
      let!(:existing_line_item) { create(:line_item, :in_cart, cart: cart, product: product, quantity: 2) }

      it "does not create new line item" do
        expect do
          post :create, params: {product_id: product.id}
        end.not_to change(LineItem, :count)
      end

      it "increments existing line item quantity" do
        post :create, params: {product_id: product.id}
        expect(existing_line_item.reload.quantity).to eq(3)
      end

      it "redirects to cart with success message" do
        post :create, params: {product_id: product.id}
        expect(response).to redirect_to(cart_path(@cart))
        expect(flash[:notice]).to eq("Product added to cart.")
      end
    end

    context "when line item save fails" do
      before do
        allow_any_instance_of(LineItem).to receive(:save).and_return(false)
      end

      it "redirects to cart with error message" do
        post :create, params: {product_id: product.id}
        expect(response).to redirect_to(cart_path(@cart))
        expect(flash[:alert]).to eq("Could not add product.")
      end
    end
  end

  describe "PATCH #update" do
    let!(:line_item) { create(:line_item, :in_cart, cart: cart, product: product, quantity: 1) }

    context "with valid parameters" do
      it "updates the line item" do
        patch :update, params: {id: line_item.id, line_item: {quantity: 3}}
        expect(line_item.reload.quantity).to eq(3)
      end

      it "redirects to cart with success message" do
        patch :update, params: {id: line_item.id, line_item: {quantity: 3}}
        expect(response).to redirect_to(cart_path(@cart))
        expect(flash[:notice]).to eq("Cart updated.")
      end
    end

    context "with invalid parameters" do
      before do
        allow_any_instance_of(LineItem).to receive(:update).and_return(false)
      end

      it "redirects to cart with error message" do
        patch :update, params: {id: line_item.id, line_item: {quantity: -1}}
        expect(response).to redirect_to(cart_path(@cart))
        expect(flash[:alert]).to eq("Could not update cart.")
      end
    end

    context "when line item does not belong to current cart" do
      let(:other_cart) { create(:cart) }
      let(:other_line_item) { create(:line_item, cart: other_cart) }

      it "raises RecordNotFound error" do
        expect do
          patch :update, params: {id: other_line_item.id, line_item: {quantity: 2}}
        end.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe "DELETE #destroy" do
    let!(:line_item) { create(:line_item, :in_cart, cart: cart, product: product) }

    it "destroys the line item" do
      expect do
        delete :destroy, params: {id: line_item.id}
      end.to change(LineItem, :count).by(-1)
    end

    it "redirects to cart with success message" do
      delete :destroy, params: {id: line_item.id}
      expect(response).to redirect_to(cart_path(@cart))
      expect(flash[:notice]).to eq("Item removed from cart.")
    end

    context "when line item does not belong to current cart" do
      let(:other_cart) { create(:cart) }
      let(:other_line_item) { create(:line_item, cart: other_cart) }

      it "raises RecordNotFound error" do
        expect do
          delete :destroy, params: {id: other_line_item.id}
        end.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe "before_action callbacks" do
    it "authenticates user before all actions" do
      expect(controller.class._process_action_callbacks.map(&:filter)).to include(:authenticate_user!)
    end

    it "sets cart before all actions" do
      expect(controller.class._process_action_callbacks.map(&:filter)).to include(:set_cart)
    end

    it "sets line item before update and destroy actions" do
      callback = controller.class._process_action_callbacks.find { |cb| cb.filter == :set_line_item }
      expect(callback.options[:only]).to include(:update, :destroy)
    end
  end

  describe "private methods" do
    describe "#set_cart" do
      it "sets @cart to current_cart" do
        post :create, params: {product_id: product.id}
        expect(assigns(:cart)).to eq(cart)
      end
    end

    describe "#set_line_item" do
      let!(:line_item) { create(:line_item, cart: cart) }

      it "sets @line_item from cart line items" do
        patch :update, params: {id: line_item.id, line_item: {quantity: 2}}
        expect(assigns(:line_item)).to eq(line_item)
      end
    end

    describe "#line_item_params" do
      it "permits quantity and price parameters" do
        params = ActionController::Parameters.new(line_item: {quantity: 2, price: 10.0, forbidden: "value"})
        allow(controller).to receive(:params).and_return(params)

        permitted = controller.send(:line_item_params)
        expect(permitted.keys).to match_array(%w[quantity price])
      end
    end
  end
end
