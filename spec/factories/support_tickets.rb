# == Schema Information
#
# Table name: support_tickets
#
#  id             :bigint           not null, primary key
#  admin_response :text
#  category       :string           not null
#  message        :text             not null
#  priority       :integer          default("normal"), not null
#  responded_at   :datetime
#  status         :integer          default("open"), not null
#  subject        :string           not null
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  admin_user_id  :bigint
#  user_id        :bigint           not null
#
# Indexes
#
#  index_support_tickets_on_admin_user_id  (admin_user_id)
#  index_support_tickets_on_category       (category)
#  index_support_tickets_on_created_at     (created_at)
#  index_support_tickets_on_priority       (priority)
#  index_support_tickets_on_status         (status)
#  index_support_tickets_on_user_id        (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => users.id)
#  fk_rails_...  (user_id => users.id)
#
FactoryBot.define do
  factory :support_ticket do
    association :user
    subject { Faker::Lorem.sentence(word_count: 5) }
    message { Faker::Lorem.paragraph(sentence_count: 3) }
    category { SupportTicket::CATEGORIES.keys.sample }
    status { :open }
    priority { :normal }

    trait :admin_handled do
      category { SupportTicket::ADMIN_CATEGORIES.sample }
    end

    trait :self_service do
      category { SupportTicket::SELF_SERVICE_CATEGORIES.sample }
    end

    trait :with_admin_response do
      association :admin_user, factory: :user
      admin_response { Faker::Lorem.paragraph(sentence_count: 2) }
      responded_at { 1.hour.ago }
      status { :resolved }
    end

    trait :urgent do
      priority { :urgent }
    end

    trait :high_priority do
      priority { :high }
    end

    trait :in_progress do
      status { :in_progress }
    end

    trait :resolved do
      status { :resolved }
    end

    trait :closed do
      status { :closed }
    end

    # Specific category factories
    factory :sales_support_ticket do
      category { "sales" }
      subject { "Question about points calculation" }
      message { "I submitted a sale but the points don't match what I expected. Can you help?" }
    end

    factory :technical_support_ticket do
      category { "technical" }
      subject { "Barcode scanner not working" }
      message { "The barcode scanner opens but doesn't recognize any barcodes. I've tried cleaning the camera." }
    end

    factory :orders_support_ticket do
      category { "orders" }
      subject { "Order status question" }
      message { "My order has been pending for 3 days. When will it be processed?" }
    end

    factory :account_support_ticket do
      category { "account" }
      subject { "Cannot update profile information" }
      message { "I'm trying to update my store information but the form won't save." }
    end
  end
end
