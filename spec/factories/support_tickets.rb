FactoryBot.define do
  factory :support_ticket do
    association :user
    subject { Faker::Lorem.sentence(word_count: 5) }
    message { Faker::Lorem.paragraph(sentence_count: 3) }
    category { SupportTicket::CATEGORIES.keys.sample }
    status { :open }
    priority { :normal }

    trait :admin_handled do
      category { SupportTicket::ADMIN_CATEGORIES.sample }
    end

    trait :self_service do
      category { SupportTicket::SELF_SERVICE_CATEGORIES.sample }
    end

    trait :with_admin_response do
      association :admin_user, factory: :user
      admin_response { Faker::Lorem.paragraph(sentence_count: 2) }
      responded_at { 1.hour.ago }
      status { :resolved }
    end

    trait :urgent do
      priority { :urgent }
    end

    trait :high_priority do
      priority { :high }
    end

    trait :in_progress do
      status { :in_progress }
    end

    trait :resolved do
      status { :resolved }
    end

    trait :closed do
      status { :closed }
    end

    # Specific category factories
    factory :sales_support_ticket do
      category { "sales" }
      subject { "Question about points calculation" }
      message { "I submitted a sale but the points don't match what I expected. Can you help?" }
    end

    factory :technical_support_ticket do
      category { "technical" }
      subject { "Barcode scanner not working" }
      message { "The barcode scanner opens but doesn't recognize any barcodes. I've tried cleaning the camera." }
    end

    factory :orders_support_ticket do
      category { "orders" }
      subject { "Order status question" }
      message { "My order has been pending for 3 days. When will it be processed?" }
    end

    factory :account_support_ticket do
      category { "account" }
      subject { "Cannot update profile information" }
      message { "I'm trying to update my store information but the form won't save." }
    end
  end
end
