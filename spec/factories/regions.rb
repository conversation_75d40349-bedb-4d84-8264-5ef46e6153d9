# == Schema Information
#
# Table name: regions
#
#  id               :bigint           not null, primary key
#  name             :string           not null
#  promotions_count :integer          default(0), not null
#  states_count     :integer          default(0), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  admin_user_id    :bigint           not null
#
# Indexes
#
#  index_regions_on_admin_user_id  (admin_user_id)
#  index_regions_on_name           (name) UNIQUE
#  index_regions_on_states_count   (states_count)
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => users.id)
#
FactoryBot.define do
  factory :region do
    sequence(:name) { |n| "Region #{n}" }
    association :admin_user, factory: :user
  end
end
