# == Schema Information
#
# Table name: promotion_products
#
#  id           :bigint           not null, primary key
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  product_id   :bigint           not null
#  promotion_id :bigint           not null
#
# Indexes
#
#  index_promotion_products_on_product_id                   (product_id)
#  index_promotion_products_on_promotion_id                 (promotion_id)
#  index_promotion_products_on_promotion_id_and_product_id  (promotion_id,product_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (product_id => products.id)
#  fk_rails_...  (promotion_id => promotions.id)
#
FactoryBot.define do
  factory :promotion_product do
    association :promotion
    association :product
  end
end
