# frozen_string_literal: true

# == Schema Information
#
# Table name: gift_card_orders
#
#  id                   :bigint           not null, primary key
#  amount               :decimal(10, 2)   not null
#  message              :text
#  offer_code           :string
#  recipient_city       :string           not null
#  recipient_company    :string
#  recipient_country    :string           not null
#  recipient_email      :string           not null
#  recipient_first_name :string           not null
#  recipient_last_name  :string           not null
#  recipient_phone      :string
#  recipient_state      :string           not null
#  recipient_street1    :string           not null
#  recipient_street2    :string
#  recipient_zip        :string           not null
#  status               :integer          default("pending"), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  gift_card_batch_id   :bigint
#  order_id             :bigint           not null
#  product_id           :bigint           not null
#
# Indexes
#
#  index_gift_card_orders_on_created_at          (created_at)
#  index_gift_card_orders_on_gift_card_batch_id  (gift_card_batch_id)
#  index_gift_card_orders_on_order_id            (order_id)
#  index_gift_card_orders_on_product_id          (product_id)
#  index_gift_card_orders_on_status              (status)
#
# Foreign Keys
#
#  fk_rails_...  (gift_card_batch_id => gift_card_batches.id)
#  fk_rails_...  (order_id => orders.id)
#  fk_rails_...  (product_id => products.id)
#
FactoryBot.define do
  factory :gift_card_order do
    association :order
    association :product
    gift_card_batch { nil }

    amount { Faker::Commerce.price(range: 25.0..500.0) }
    recipient_first_name { Faker::Name.first_name }
    recipient_last_name { Faker::Name.last_name }
    recipient_company { Faker::Company.name }
    recipient_street1 { Faker::Address.street_address }
    recipient_street2 { Faker::Address.secondary_address }
    recipient_city { Faker::Address.city }
    recipient_state { Faker::Address.state_abbr }
    recipient_zip { Faker::Address.zip_code }
    recipient_country { "US" }
    recipient_phone { Faker::PhoneNumber.phone_number }
    recipient_email { Faker::Internet.email }
    offer_code { Faker::Alphanumeric.alphanumeric(number: 8).upcase }
    message { Faker::Lorem.sentence }
    status { :pending }

    trait :batched do
      gift_card_batch { association(:gift_card_batch) }
      status { :batched }
    end

    trait :uploaded do
      gift_card_batch { association(:gift_card_batch) }
      status { :uploaded }
    end

    trait :failed do
      gift_card_batch { association(:gift_card_batch) }
      status { :failed }
    end

    trait :without_optional_fields do
      recipient_company { nil }
      recipient_street2 { nil }
      recipient_phone { nil }
      offer_code { nil }
      message { nil }
    end
  end
end
