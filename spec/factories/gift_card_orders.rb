# frozen_string_literal: true

FactoryBot.define do
  factory :gift_card_order do
    association :order
    association :product
    gift_card_batch { nil }

    amount { Faker::Commerce.price(range: 25.0..500.0) }
    recipient_first_name { Faker::Name.first_name }
    recipient_last_name { Faker::Name.last_name }
    recipient_company { Faker::Company.name }
    recipient_street1 { Faker::Address.street_address }
    recipient_street2 { Faker::Address.secondary_address }
    recipient_city { Faker::Address.city }
    recipient_state { Faker::Address.state_abbr }
    recipient_zip { Faker::Address.zip_code }
    recipient_country { "US" }
    recipient_phone { Faker::PhoneNumber.phone_number }
    recipient_email { Faker::Internet.email }
    offer_code { Faker::Alphanumeric.alphanumeric(number: 8).upcase }
    message { Faker::Lorem.sentence }
    status { :pending }

    trait :batched do
      gift_card_batch { association(:gift_card_batch) }
      status { :batched }
    end

    trait :uploaded do
      gift_card_batch { association(:gift_card_batch) }
      status { :uploaded }
    end

    trait :failed do
      gift_card_batch { association(:gift_card_batch) }
      status { :failed }
    end

    trait :without_optional_fields do
      recipient_company { nil }
      recipient_street2 { nil }
      recipient_phone { nil }
      offer_code { nil }
      message { nil }
    end
  end
end
