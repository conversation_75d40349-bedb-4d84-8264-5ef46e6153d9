# == Schema Information
#
# Table name: line_items
#
#  id         :bigint           not null, primary key
#  quantity   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  cart_id    :bigint
#  order_id   :bigint
#  product_id :bigint           not null
#
# Indexes
#
#  index_line_items_on_cart_id     (cart_id)
#  index_line_items_on_order_id    (order_id)
#  index_line_items_on_product_id  (product_id)
#
# Foreign Keys
#
#  fk_rails_...  (cart_id => carts.id)
#  fk_rails_...  (order_id => orders.id)
#  fk_rails_...  (product_id => products.id)
#
FactoryBot.define do
  factory :line_item do
    association :product
    quantity { 1 }
    # No price field in line_items table - it's a points-based system

    trait :in_cart do
      association :cart
      order { nil }
    end

    trait :in_order do
      association :order
      cart { nil }
    end

    trait :multiple_quantity do
      quantity { 3 }
    end

    trait :expensive do
      # In points system, price is always 0, but we can have high-point products
      after(:build) do |line_item|
        if line_item.product&.product_country_data&.first
          line_item.product.product_country_data.first.update(points_cost: 500)
        end
      end
    end

    trait :cheap do
      # In points system, price is always 0, but we can have low-point products
      after(:build) do |line_item|
        if line_item.product&.product_country_data&.first
          line_item.product.product_country_data.first.update(points_cost: 10)
        end
      end
    end

    # Default to cart association
    association :cart

    # Ensure product has country data for points calculation
    after(:build) do |line_item|
      if line_item.product && line_item.product.product_country_data.empty?
        country = Country.find_or_create_by(code: "US", name: "United States")
        create(:product_country_datum, product: line_item.product, country: country, points_cost: 100)
      end
    end
  end
end
