# frozen_string_literal: true

# == Schema Information
#
# Table name: users
#
#  id                     :bigint           not null, primary key
#  approved_sales_count   :integer          default(0), not null
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  email                  :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  failed_attempts        :integer          default(0), not null
#  first_name             :string           default("<PERSON>eiss"), not null
#  last_name              :string           default("User"), not null
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  locked_at              :datetime
#  notification_settings  :jsonb
#  orders_count           :integer          default(0), not null
#  pending_sales_count    :integer          default(0), not null
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  role                   :integer          default("regular"), not null
#  sales_count            :integer          default(0), not null
#  sign_in_count          :integer          default(0), not null
#  status                 :integer          default("inactive"), not null
#  total_points_earned    :integer          default(0), not null
#  unconfirmed_email      :string
#  unlock_token           :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  store_id               :bigint
#
# Indexes
#
#  index_users_on_approved_sales_count   (approved_sales_count)
#  index_users_on_confirmation_token     (confirmation_token) UNIQUE
#  index_users_on_email                  (email) UNIQUE
#  index_users_on_notification_settings  (notification_settings) USING gin
#  index_users_on_orders_count           (orders_count)
#  index_users_on_reset_password_token   (reset_password_token) UNIQUE
#  index_users_on_sales_count            (sales_count)
#  index_users_on_status                 (status)
#  index_users_on_store_id               (store_id)
#  index_users_on_total_points_earned    (total_points_earned)
#  index_users_on_unlock_token           (unlock_token) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (store_id => stores.id)
#
FactoryBot.define do
  factory :user do
    sequence(:email) { |n| "user#{n}@example.com" }
    password { "password" }
    password_confirmation { "password" }
    confirmed_at { Time.current }
    status { :active }
    role { :regular }
    association :store, factory: :store, status: :active
    after(:create) do |user|
      user.create_cart unless user.cart
    end
  end
end
