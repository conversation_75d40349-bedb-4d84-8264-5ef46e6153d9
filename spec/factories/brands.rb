# == Schema Information
#
# Table name: brands
#
#  id               :bigint           not null, primary key
#  categories_count :integer          default(0), not null
#  name             :string           not null
#  products_count   :integer          default(0), not null
#  stores_count     :integer          default(0), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#
# Indexes
#
#  index_brands_on_categories_count  (categories_count)
#  index_brands_on_name              (name) UNIQUE
#  index_brands_on_products_count    (products_count)
#  index_brands_on_stores_count      (stores_count)
#
FactoryBot.define do
  factory :brand do
    name { Faker::Company.unique.name }
  end
end
