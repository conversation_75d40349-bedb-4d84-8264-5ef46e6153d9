# == Schema Information
#
# Table name: promotions
#
#  id                       :bigint           not null, primary key
#  bonus_multiplier         :decimal(3, 2)
#  bonus_points             :integer          not null
#  description              :text
#  end_date                 :datetime         not null
#  name                     :string           not null
#  promotion_products_count :integer          default(0), not null
#  sales_count              :integer          default(0), not null
#  start_date               :datetime         not null
#  status                   :integer          default("active"), not null
#  total_bonus_points       :integer          default(0), not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  region_id                :bigint
#  store_chain_id           :bigint
#  store_id                 :bigint
#
# Indexes
#
#  index_promotions_on_end_date                 (end_date)
#  index_promotions_on_region_id                (region_id)
#  index_promotions_on_sales_count              (sales_count)
#  index_promotions_on_start_date               (start_date)
#  index_promotions_on_start_date_and_end_date  (start_date,end_date)
#  index_promotions_on_status                   (status)
#  index_promotions_on_store_chain_id           (store_chain_id)
#  index_promotions_on_store_id                 (store_id)
#  index_promotions_on_total_bonus_points       (total_bonus_points)
#
# Foreign Keys
#
#  fk_rails_...  (region_id => regions.id)
#  fk_rails_...  (store_chain_id => store_chains.id)
#  fk_rails_...  (store_id => stores.id)
#
FactoryBot.define do
  factory :promotion do
    sequence(:name) { |n| "Promotion #{n}" }
    description { "A great promotion offering bonus points" }
    bonus_points { 100 }
    bonus_multiplier { nil }
    start_date { 1.day.ago }
    end_date { 1.week.from_now }
    status { :active }

    # Default to store scope
    association :store
    region { nil }
    store_chain { nil }

    trait :with_multiplier do
      bonus_points { 0 }
      bonus_multiplier { 1.5 }
    end

    trait :for_region do
      store { nil }
      association :region
      store_chain { nil }
    end

    trait :for_store_chain do
      store { nil }
      region { nil }
      association :store_chain
    end

    trait :inactive do
      status { :inactive }
    end

    trait :expired do
      status { :expired }
      start_date { 2.weeks.ago }
      end_date { 1.week.ago }
    end

    trait :future do
      start_date { 1.week.from_now }
      end_date { 2.weeks.from_now }
    end

    trait :with_products do
      after(:create) do |promotion|
        promotion.products << create_list(:product, 2)
      end
    end
  end
end
