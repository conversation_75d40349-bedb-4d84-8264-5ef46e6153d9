# spec/factories/orders.rb
# == Schema Information
#
# Table name: orders
#
#  id                 :bigint           not null, primary key
#  approved_at        :datetime
#  approved_by        :bigint
#  delivered_at       :datetime
#  line_items_count   :integer          default(0), not null
#  points             :integer          not null
#  rejected_at        :datetime
#  rejected_by        :bigint
#  sap_processed_at   :datetime
#  shipped_at         :datetime
#  shipped_by         :bigint
#  shipping_address   :string
#  shipping_type      :string           not null
#  status             :integer          default("pending"), not null
#  total_points_cache :integer          default(0), not null
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  sap_id             :string
#  user_id            :bigint           not null
#
# Indexes
#
#  index_orders_on_approved_by         (approved_by)
#  index_orders_on_line_items_count    (line_items_count)
#  index_orders_on_rejected_by         (rejected_by)
#  index_orders_on_sap_id              (sap_id) UNIQUE
#  index_orders_on_shipped_by          (shipped_by)
#  index_orders_on_total_points_cache  (total_points_cache)
#  index_orders_on_user_id             (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
FactoryBot.define do
  factory :order do
    association :user
    status { :pending }
    shipping_type { "user" }
    shipping_address { "123 Main St, City, State 12345" }
    points { 100 }

    trait :approved do
      status { :approved }
      approved_at { Time.current }
      approved_by { create(:user, role: :admin).id }
    end

    trait :rejected do
      status { :rejected }
      rejected_at { Time.current }
      rejected_by { create(:user, role: :admin).id }
    end

    trait :shipped do
      status { :shipped }
      approved_at { 2.days.ago }
      approved_by { create(:user, role: :admin).id }
      shipped_at { Time.current }
      shipped_by { create(:user, role: :admin).id }
    end

    trait :delivered do
      status { :delivered }
      approved_at { 3.days.ago }
      approved_by { create(:user, role: :admin).id }
      shipped_at { 1.day.ago }
      shipped_by { create(:user, role: :admin).id }
      delivered_at { Time.current }
    end

    trait :ship_to_store do
      shipping_type { "store" }
      shipping_address { nil }
    end

    trait :ship_to_user do
      shipping_type { "user" }
      shipping_address { "123 Main St, City, State 12345" }
    end

    trait :with_line_items do
      after(:create) do |order|
        create_list(:line_item, 2, :in_order, order: order)
        # Update points cache based on line items
        total_points = order.line_items.sum(&:total_points)
        order.update!(total_points_cache: total_points, points: total_points)
      end
    end

    trait :high_points do
      points { 500 }
    end

    trait :low_points do
      points { 10 }
    end
  end
end
