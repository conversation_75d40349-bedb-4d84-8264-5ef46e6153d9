# frozen_string_literal: true

# == Schema Information
#
# Table name: sale_promotion_bonuses
#
#  id           :bigint           not null, primary key
#  bonus_points :integer          not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  promotion_id :bigint           not null
#  sale_id      :bigint           not null
#
# Indexes
#
#  index_sale_promotion_bonuses_on_promotion_id              (promotion_id)
#  index_sale_promotion_bonuses_on_sale_id                   (sale_id)
#  index_sale_promotion_bonuses_on_sale_id_and_promotion_id  (sale_id,promotion_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (promotion_id => promotions.id)
#  fk_rails_...  (sale_id => sales.id)
#
FactoryBot.define do
  factory :sale_promotion_bonus do
    association :sale
    association :promotion
    bonus_points { Faker::Number.between(from: 10, to: 500) }

    trait :high_bonus do
      bonus_points { Faker::Number.between(from: 500, to: 1000) }
    end

    trait :low_bonus do
      bonus_points { Faker::Number.between(from: 1, to: 50) }
    end
  end
end
