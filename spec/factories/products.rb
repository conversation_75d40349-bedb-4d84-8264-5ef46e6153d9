# == Schema Information
#
# Table name: products
#
#  id                       :bigint           not null, primary key
#  description              :text
#  line_items_count         :integer          default(0), not null
#  name                     :string           not null
#  orders_count             :integer          default(0), not null
#  product_type             :integer          default("regular"), not null
#  promotion_products_count :integer          default(0), not null
#  sales_count              :integer          default(0), not null
#  sku                      :string           not null
#  status                   :integer          default("active"), not null
#  total_points_earned      :integer          default(0), not null
#  total_quantity_sold      :integer          default(0), not null
#  upc                      :string           not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  category_id              :bigint           not null
#
# Indexes
#
#  index_products_on_category_id          (category_id)
#  index_products_on_line_items_count     (line_items_count)
#  index_products_on_orders_count         (orders_count)
#  index_products_on_product_type         (product_type)
#  index_products_on_sales_count          (sales_count)
#  index_products_on_sku                  (sku) UNIQUE
#  index_products_on_total_points_earned  (total_points_earned)
#  index_products_on_total_quantity_sold  (total_quantity_sold)
#  index_products_on_upc                  (upc) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (category_id => categories.id)
#
FactoryBot.define do
  factory :product do
    name { Faker::Commerce.product_name }
    sku { Faker::Alphanumeric.unique.alphanumeric(number: 8).upcase }
    upc { Faker::Code.unique.ean } # valid unique EAN-13
    description { Faker::Lorem.sentence }
    status { "active" }
    association :category
  end
end
