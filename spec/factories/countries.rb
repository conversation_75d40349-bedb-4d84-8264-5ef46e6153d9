# frozen_string_literal: true

# == Schema Information
#
# Table name: countries
#
#  id         :bigint           not null, primary key
#  code       :string(2)        not null
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_countries_on_code  (code) UNIQUE
#  index_countries_on_name  (name) UNIQUE
#
FactoryBot.define do
  factory :country do
    sequence(:code) { |n| "C#{n.to_s.rjust(1, "0")}" }
    sequence(:name) { |n| "Country #{n}" }

    trait :united_states do
      code { "US" }
      name { "United States" }
    end

    trait :canada do
      code { "CA" }
      name { "Canada" }
    end

    trait :germany do
      code { "DE" }
      name { "Germany" }
    end

    trait :united_kingdom do
      code { "GB" }
      name { "United Kingdom" }
    end

    trait :france do
      code { "FR" }
      name { "France" }
    end

    trait :japan do
      code { "JP" }
      name { "Japan" }
    end

    trait :australia do
      code { "AU" }
      name { "Australia" }
    end
  end
end
