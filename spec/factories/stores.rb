# frozen_string_literal: true

# == Schema Information
#
# Table name: stores
#
#  id                   :bigint           not null, primary key
#  approved_sales_count :integer          default(0), not null
#  name                 :string           not null
#  orders_count         :integer          default(0), not null
#  pending_sales_count  :integer          default(0), not null
#  phone_number         :string           not null
#  promotions_count     :integer          default(0), not null
#  sales_count          :integer          default(0), not null
#  status               :integer          default("requested"), not null
#  total_sales_points   :integer          default(0), not null
#  users_count          :integer          default(0), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  brand_id             :bigint
#  store_chain_id       :bigint
#
# Indexes
#
#  index_stores_on_approved_sales_count  (approved_sales_count)
#  index_stores_on_brand_id              (brand_id)
#  index_stores_on_orders_count          (orders_count)
#  index_stores_on_sales_count           (sales_count)
#  index_stores_on_status                (status)
#  index_stores_on_store_chain_id        (store_chain_id)
#  index_stores_on_total_sales_points    (total_sales_points)
#  index_stores_on_users_count           (users_count)
#
# Foreign Keys
#
#  fk_rails_...  (brand_id => brands.id)
#  fk_rails_...  (store_chain_id => store_chains.id)
#
FactoryBot.define do
  factory :store do
    name { Faker::Company.name }
    phone_number { Faker::PhoneNumber.phone_number }
    status { :requested }
    association :brand

    trait :active do
      status { :active }
    end
  end
end
