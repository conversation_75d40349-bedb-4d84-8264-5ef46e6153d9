# frozen_string_literal: true

# == Schema Information
#
# Table name: gift_card_batches
#
#  id           :bigint           not null, primary key
#  batch_date   :date             not null
#  filename     :string           not null
#  processed_at :datetime
#  status       :integer          default("pending"), not null
#  total_amount :decimal(12, 2)   default(0.0), not null
#  total_orders :integer          default(0), not null
#  upload_error :text
#  uploaded_at  :datetime
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_gift_card_batches_on_batch_date  (batch_date) UNIQUE
#  index_gift_card_batches_on_filename    (filename)
#  index_gift_card_batches_on_status      (status)
#
FactoryBot.define do
  factory :gift_card_batch do
    batch_date { Date.current }
    filename { "WWR-ZS3PG-#{Time.current.strftime("%Y%m%d_%H%M%S")}.csv" }
    status { :pending }
    total_orders { 0 }
    total_amount { 0.0 }

    trait :generated do
      status { :generated }
      total_orders { 5 }
      total_amount { 500.0 }
    end

    trait :uploaded do
      status { :uploaded }
      total_orders { 5 }
      total_amount { 500.0 }
      uploaded_at { Time.current }
    end

    trait :failed do
      status { :failed }
      upload_error { "SFTP connection failed" }
    end

    trait :with_csv_file do
      after(:create) do |batch|
        batch.csv_file.attach(
          io: StringIO.new("FirstName,LastName,Email\nJohn,Doe,<EMAIL>"),
          filename: batch.filename,
          content_type: "text/csv"
        )
      end
    end

    trait :with_orders do
      after(:create) do |batch|
        create_list(:gift_card_order, 3, gift_card_batch: batch, created_at: batch.batch_date.beginning_of_day)
      end
    end
  end
end
