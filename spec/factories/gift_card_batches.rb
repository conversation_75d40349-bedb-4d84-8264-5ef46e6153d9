# frozen_string_literal: true

FactoryBot.define do
  factory :gift_card_batch do
    batch_date { Date.current }
    filename { "WWR-ZS3PG-#{Time.current.strftime("%Y%m%d_%H%M%S")}.csv" }
    status { :pending }
    total_orders { 0 }
    total_amount { 0.0 }

    trait :generated do
      status { :generated }
      total_orders { 5 }
      total_amount { 500.0 }
    end

    trait :uploaded do
      status { :uploaded }
      total_orders { 5 }
      total_amount { 500.0 }
      uploaded_at { Time.current }
    end

    trait :failed do
      status { :failed }
      upload_error { "SFTP connection failed" }
    end

    trait :with_csv_file do
      after(:create) do |batch|
        batch.csv_file.attach(
          io: StringIO.new("<PERSON><PERSON><PERSON>,Last<PERSON><PERSON>,Email\nJohn,<PERSON><PERSON>,<EMAIL>"),
          filename: batch.filename,
          content_type: "text/csv"
        )
      end
    end

    trait :with_orders do
      after(:create) do |batch|
        create_list(:gift_card_order, 3, gift_card_batch: batch, created_at: batch.batch_date.beginning_of_day)
      end
    end
  end
end
