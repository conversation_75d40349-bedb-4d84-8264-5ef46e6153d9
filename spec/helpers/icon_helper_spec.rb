require "rails_helper"

RSpec.describe IconHelper, type: :helper do
  describe "#lucide_icon" do
    it "renders a lucide icon with default class" do
      result = helper.lucide_icon("home")
      expect(result).to eq('<i data-lucide="home" class="w-5 h-5"></i>')
    end

    it "renders a lucide icon with custom class" do
      result = helper.lucide_icon("home", class: "w-6 h-6 text-blue-500")
      expect(result).to eq('<i data-lucide="home" class="w-6 h-6 text-blue-500"></i>')
    end

    it "handles icons with hyphens in name" do
      result = helper.lucide_icon("arrow-left")
      expect(result).to eq('<i data-lucide="arrow-left" class="w-5 h-5"></i>')
    end
  end

  describe "shortcut icon methods" do
    it "renders dashboard icon" do
      result = helper.dashboard_icon
      expect(result).to eq('<i data-lucide="layout-dashboard" class="w-5 h-5"></i>')
    end

    it "renders users icon with custom class" do
      result = helper.users_icon(class: "w-4 h-4 mr-2")
      expect(result).to eq('<i data-lucide="users" class="w-4 h-4 mr-2"></i>')
    end

    it "renders sales icon" do
      result = helper.sales_icon
      expect(result).to eq('<i data-lucide="clipboard-list" class="w-5 h-5"></i>')
    end

    it "renders orders icon" do
      result = helper.orders_icon
      expect(result).to eq('<i data-lucide="shopping-cart" class="w-5 h-5"></i>')
    end

    it "renders products icon" do
      result = helper.products_icon
      expect(result).to eq('<i data-lucide="package" class="w-5 h-5"></i>')
    end

    it "renders stores icon" do
      result = helper.stores_icon
      expect(result).to eq('<i data-lucide="store" class="w-5 h-5"></i>')
    end

    it "renders promotions icon" do
      result = helper.promotions_icon
      expect(result).to eq('<i data-lucide="percent" class="w-5 h-5"></i>')
    end

    it "renders gift icon" do
      result = helper.gift_icon
      expect(result).to eq('<i data-lucide="gift" class="w-5 h-5"></i>')
    end

    it "renders support icon" do
      result = helper.support_icon
      expect(result).to eq('<i data-lucide="message-circle-question" class="w-5 h-5"></i>')
    end

    it "renders help icon" do
      result = helper.help_icon
      expect(result).to eq('<i data-lucide="help-circle" class="w-5 h-5"></i>')
    end

    it "renders search icon" do
      result = helper.search_icon
      expect(result).to eq('<i data-lucide="search" class="w-5 h-5"></i>')
    end

    it "renders edit icon" do
      result = helper.edit_icon
      expect(result).to eq('<i data-lucide="edit" class="w-5 h-5"></i>')
    end

    it "renders delete icon" do
      result = helper.delete_icon
      expect(result).to eq('<i data-lucide="trash-2" class="w-5 h-5"></i>')
    end

    it "renders add icon" do
      result = helper.add_icon
      expect(result).to eq('<i data-lucide="plus" class="w-5 h-5"></i>')
    end

    it "renders back icon" do
      result = helper.back_icon
      expect(result).to eq('<i data-lucide="arrow-left" class="w-5 h-5"></i>')
    end

    it "renders send icon" do
      result = helper.send_icon
      expect(result).to eq('<i data-lucide="send" class="w-5 h-5"></i>')
    end

    it "renders check icon" do
      result = helper.check_icon
      expect(result).to eq('<i data-lucide="check" class="w-5 h-5"></i>')
    end

    it "renders x icon" do
      result = helper.x_icon
      expect(result).to eq('<i data-lucide="x" class="w-5 h-5"></i>')
    end
  end

  describe "icon consistency" do
    it "all shortcut methods accept custom options" do
      methods_to_test = [
        :dashboard_icon, :users_icon, :sales_icon, :orders_icon, :products_icon,
        :stores_icon, :promotions_icon, :gift_icon, :support_icon, :help_icon,
        :search_icon, :edit_icon, :delete_icon, :add_icon, :back_icon,
        :send_icon, :check_icon, :x_icon
      ]

      methods_to_test.each do |method|
        result = helper.send(method, class: "custom-class")
        expect(result).to include('class="custom-class"')
        expect(result).to include("data-lucide=")
      end
    end

    it "all icons use proper lucide naming convention" do
      # Test that icon names follow lucide conventions (lowercase, hyphens)
      icon_mappings = {
        dashboard_icon: "layout-dashboard",
        users_icon: "users",
        sales_icon: "clipboard-list",
        orders_icon: "shopping-cart",
        products_icon: "package",
        stores_icon: "store",
        promotions_icon: "percent",
        gift_icon: "gift",
        support_icon: "message-circle-question",
        help_icon: "help-circle",
        search_icon: "search",
        edit_icon: "edit",
        delete_icon: "trash-2",
        add_icon: "plus",
        back_icon: "arrow-left",
        send_icon: "send",
        check_icon: "check",
        x_icon: "x"
      }

      icon_mappings.each do |method, expected_icon|
        result = helper.send(method)
        expect(result).to include("data-lucide=\"#{expected_icon}\"")
      end
    end
  end
end
