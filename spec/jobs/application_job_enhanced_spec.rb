# frozen_string_literal: true

require "rails_helper"

RSpec.describe ApplicationJob, type: :job do
  describe "job configuration" do
    it "inherits from ActiveJob::Base" do
      expect(described_class.superclass).to eq(ActiveJob::Base)
    end
  end

  describe "queue configuration" do
    it "has a default queue" do
      expect(described_class.queue_name).to be_present
    end
  end

  describe "job execution" do
    let(:test_job_class) do
      Class.new(ApplicationJob) do
        def perform(message = "test")
          message
        end
      end
    end

    it "can be subclassed and executed" do
      job = test_job_class.new
      expect(job.perform).to eq("test")
    end

    it "can be enqueued" do
      expect do
        test_job_class.perform_later("test message")
      end.to have_enqueued_job(test_job_class).with("test message")
    end
  end

  describe "error handling" do
    let(:failing_job_class) do
      Class.new(ApplicationJob) do
        def perform
          raise StandardError, "Test error"
        end
      end
    end

    it "handles job failures" do
      job = failing_job_class.new
      expect { job.perform }.to raise_error(StandardError, "Test error")
    end
  end

  describe "job inheritance" do
    it "provides base functionality for other jobs" do
      expect(GiftCardBatchProcessorJob.superclass).to eq(ApplicationJob)
    end
  end

  describe "ActiveJob features" do
    let(:test_job_class) do
      Class.new(ApplicationJob) do
        def perform(arg1, arg2 = nil)
          [arg1, arg2]
        end
      end
    end

    it "supports job arguments" do
      job = test_job_class.new("arg1", "arg2")
      expect(job.perform("arg1", "arg2")).to eq(["arg1", "arg2"])
    end

    it "supports delayed execution" do
      expect do
        test_job_class.set(wait: 1.hour).perform_later("delayed")
      end.to have_enqueued_job(test_job_class).with("delayed").at(1.hour.from_now)
    end

    it "supports queue specification" do
      expect do
        test_job_class.set(queue: :high_priority).perform_later("priority")
      end.to have_enqueued_job(test_job_class).with("priority").on_queue(:high_priority)
    end
  end

  describe "job serialization" do
    let(:test_job_class) do
      Class.new(ApplicationJob) do
        def perform(user)
          user.id
        end
      end
    end

    it "can serialize ActiveRecord objects" do
      user = create(:user)
      expect do
        test_job_class.perform_later(user)
      end.to have_enqueued_job(test_job_class).with(user)
    end
  end
end
