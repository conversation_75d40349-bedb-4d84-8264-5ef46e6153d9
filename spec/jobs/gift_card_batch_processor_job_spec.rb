require "rails_helper"

RSpec.describe GiftCardBatchProcessorJob, type: :job do
  let(:date) { Date.current }

  before do
    # Mock the mailer to avoid email sending in tests
    allow(GiftCardBatchMailer).to receive(:batch_uploaded).and_return(double(deliver_now: true))
    allow(GiftCard<PERSON>atchMailer).to receive(:batch_upload_failed).and_return(double(deliver_now: true))
    allow(GiftCardBatchMailer).to receive(:batch_processing_failed).and_return(double(deliver_now: true))

    # Mock SFTP service to avoid actual SFTP calls
    sftp_service = instance_double(GiftCardSftpService)
    allow(GiftCardSftpService).to receive(:new).and_return(sftp_service)
    allow(sftp_service).to receive(:upload_file).and_return(true)
  end

  it "processes a gift card batch" do
    # Create some gift card orders for the date (unbatched status)
    order = create(:order, created_at: date.beginning_of_day)
    product = create(:product, product_type: :gift_card)
    create(:gift_card_order, order: order, product: product, created_at: date.beginning_of_day, status: :pending, gift_card_batch: nil)
    create(:gift_card_order, order: order, product: product, created_at: date.beginning_of_day, status: :pending, gift_card_batch: nil)

    # Verify orders are unbatched and for the correct date
    expect(GiftCardOrder.for_date(date).unbatched.count).to eq(2)

    # Run the job
    described_class.perform_now(date)

    # Find the batch that was created by the job
    batch = GiftCardBatch.find_by(batch_date: date)
    expect(batch).to be_present
    expect(batch.status).to eq("uploaded")
    expect(batch.processed_at).not_to be_nil

    # Verify orders were processed
    expect(GiftCardOrder.for_date(date).unbatched.count).to eq(0)
    expect(GiftCardOrder.for_date(date).where.not(gift_card_batch: nil).count).to eq(2)
  end

  # Add more tests for error handling, edge cases, etc.
end
