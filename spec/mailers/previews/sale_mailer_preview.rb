# Preview all emails at http://localhost:3000/rails/mailers/sale_mailer
class SaleMailerPreview < ActionMailer::Preview
  def new_sale_submitted
    sale = Sale.includes(:user, :product).pending.first || create_sample_sale
    admin = User.admin.first || create_sample_admin

    SaleMailer.with(
      record: sale,
      recipient: admin
    ).new_sale_submitted
  end

  def sale_approved
    sale = Sale.includes(:user, :product).approved.first || create_sample_approved_sale
    admin = User.admin.first || create_sample_admin

    SaleMailer.with(
      record: sale,
      recipient: sale.user,
      approved_by: admin
    ).sale_approved
  end

  def sale_rejected
    sale = Sale.includes(:user, :product).rejected.first || create_sample_rejected_sale
    admin = User.admin.first || create_sample_admin

    SaleMailer.with(
      record: sale,
      recipient: sale.user,
      rejected_by: admin,
      reason: "Invalid serial number format"
    ).sale_rejected
  end

  private

  def create_sample_sale
    user = User.first || User.create!(
      email: "<EMAIL>",
      password: "password123",
      store: Store.first,
      status: :active,
      confirmed_at: Time.current
    )

    product = Product.first || Product.create!(
      name: "Zeiss Conquest HD 10x42",
      status: :active,
      category: Category.first
    )

    unique_serial = "ZC#{SecureRandom.hex(6).upcase}"

    Sale.create!(
      user: user,
      product: product,
      serial_number: unique_serial,
      points: 100,
      sold_at: 2.days.ago,
      status: :pending,
      notes: "Customer was very satisfied with the product quality."
    )
  end

  def create_sample_approved_sale
    sale = create_sample_sale
    sale.update!(
      status: :approved,
      approved_at: 1.day.ago,
      approved_by: create_sample_admin.id
    )
    sale
  end

  def create_sample_rejected_sale
    sale = create_sample_sale
    sale.update!(
      status: :rejected,
      approved_at: 1.day.ago,
      approved_by: create_sample_admin.id
    )
    sale
  end

  def create_sample_admin
    User.find_or_create_by(email: "<EMAIL>") do |user|
      user.password = "password123"
      user.role = :admin
      user.status = :active
      user.confirmed_at = Time.current
      user.store = Store.first
    end
  end
end
