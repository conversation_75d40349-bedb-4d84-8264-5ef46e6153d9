# Preview all emails at http://localhost:3000/rails/mailers/order_mailer
class OrderMailerPreview < ActionMailer::Preview
  def new_order_submitted
    order = Order.includes(line_items: :product).first || create_sample_order
    admin = User.where(role: :admin).first || User.new(
      email: "<EMAIL>",
      first_name: "Admin",
      last_name: "User",
      role: :admin
    )

    OrderMailer.with(record: order, recipient: admin).new_order_submitted
  end

  def order_approved
    order = Order.includes(line_items: :product).first || create_sample_order
    user = order.user
    admin = User.where(role: :admin).first || User.new(
      email: "<EMAIL>",
      first_name: "Admin",
      last_name: "User",
      role: :admin
    )

    # Set order as approved for preview
    order.status = :approved
    order.approved_at = 1.day.ago
    order.sap_id = "SAP-********-143022-#{order.id}"

    OrderMailer.with(record: order, recipient: user, approved_by: admin).order_approved
  end

  def order_rejected
    order = Order.includes(line_items: :product).first || create_sample_order
    user = order.user
    admin = User.where(role: :admin).first || User.new(
      email: "<EMAIL>",
      first_name: "Admin",
      last_name: "User",
      role: :admin
    )

    # Set order as rejected for preview
    order.status = :rejected
    order.rejected_at = 1.day.ago

    OrderMailer.with(
      record: order,
      recipient: user,
      rejected_by: admin,
      reason: "Insufficient points in your account"
    ).order_rejected
  end

  def order_shipped
    order = Order.includes(line_items: :product).first || create_sample_order
    user = order.user
    admin = User.where(role: :admin).first || User.new(
      email: "<EMAIL>",
      first_name: "Admin",
      last_name: "User",
      role: :admin
    )

    # Set order as shipped for preview
    order.status = :shipped
    order.approved_at = 3.days.ago
    order.shipped_at = 1.day.ago
    order.sap_id = "SAP-********-143022-#{order.id}"

    OrderMailer.with(record: order, recipient: user, shipped_by: admin).order_shipped
  end

  def order_delivered
    order = Order.includes(line_items: :product).first || create_sample_order
    user = order.user

    # Set order as delivered for preview
    order.status = :delivered
    order.approved_at = 5.days.ago
    order.shipped_at = 3.days.ago
    order.delivered_at = 1.hour.ago
    order.sap_id = "SAP-********-143022-#{order.id}"

    OrderMailer.with(record: order, recipient: user).order_delivered
  end

  private

  def create_sample_order
    # Use existing data or create sample data for preview
    user = User.first || create_sample_user

    # Get or create sample products
    product1 = Product.first || create_sample_product(
      "ZEISS Conquest HD 10x42 Binoculars", 150
    )
    product2 = Product.second || create_sample_product(
      "ZEISS Victory SF 8x32 Binoculars", 200
    )

    # Calculate the correct total points
    calculated_total = 150 + 200 # product1 + product2
    
    # Create order with proper associations
    order = Order.new(
      id: 12345, # For preview purposes
      user: user,
      points: calculated_total,
      shipping_type: "standard",
      shipping_address: "123 Main St, Anytown, ST 12345",
      status: :pending,
      created_at: 2.days.ago
    )

    # Create line items with proper associations
    line_item1 = LineItem.new(
      id: 1,
      order: order,
      product: product1,
      quantity: 1,
      price: 0.0 # Points-based system, no monetary price
    )

    line_item2 = LineItem.new(
      id: 2,
      order: order,
      product: product2,
      quantity: 1,
      price: 0.0 # Points-based system, no monetary price
    )

    # Manually set the line_items association for preview
    order.define_singleton_method(:line_items) do
      [line_item1, line_item2]
    end

    # Override total_points method for preview to calculate from line items
    order.define_singleton_method(:total_points) do
      line_items.sum(&:total_points)
    end
    
    # Mock the total_points_cache attribute for preview
    order.define_singleton_method(:has_attribute?) do |attr_name|
      attr_name.to_s == 'total_points_cache' ? true : super(attr_name)
    end
    
    order.define_singleton_method(:total_points_cache) do
      line_items.sum(&:total_points)
    end

    order
  end

  def create_sample_user
    User.new(
      id: 1,
      email: "<EMAIL>",
      first_name: "John",
      last_name: "Doe"
    ).tap do |user|
      # Mock wallet for preview
      wallet = Wallet.new(points: 500)
      user.define_singleton_method(:wallet) { wallet }
    end
  end

  def create_sample_product(name, points)
    # Create a sample category if none exists
    category = Category.first || Category.new(
      id: 1,
      name: "Binoculars"
    )

    product = Product.new(
      id: Product.maximum(:id).to_i + 1,
      name: name,
      sku: "SKU-#{rand(10000..99999)}",
      upc: generate_valid_ean13,
      status: :active,
      category: category
    )

    # Mock the points_required method for preview
    product.define_singleton_method(:points_required) do |country_code = "US"|
      points
    end

    product
  end

  def generate_valid_ean13
    # Generate a valid EAN-13 barcode for preview
    base = "123456789012"
    digits = base.chars.map(&:to_i)
    checksum = 10 - (digits.each_with_index.sum { |d, i| i.even? ? d : d * 3 } % 10)
    checksum = 0 if checksum == 10
    "#{base}#{checksum}"
  end
end
