require "rails_helper"

RSpec.describe SupportTicketMailer, type: :mailer do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user) }

  describe "#new_admin_ticket" do
    let(:support_ticket) { create(:support_ticket, user: user, category: "sales", priority: "high") }
    let(:mail) { SupportTicketMailer.new_admin_ticket(support_ticket) }

    it "renders the headers" do
      expect(mail.subject).to eq("[Zeiss Points Support] Sales & Points: " + support_ticket.subject)
      expect(mail.to).to eq(["<EMAIL>"])
      expect(mail.reply_to).to eq([user.email])
    end

    it "renders the body" do
      expect(mail.body.encoded).to include(support_ticket.subject)
      expect(mail.body.encoded).to include(support_ticket.message)
      expect(mail.body.encoded).to include(user.email)
      expect(mail.body.encoded).to include("Sales & Points")
      expect(mail.body.encoded).to include("High Priority")
    end

    it "includes user information" do
      expect(mail.body.encoded).to include("User ID: ##{user.id}")
      expect(mail.body.encoded).to include(user.email)
      expect(mail.body.encoded).to include(user.status.humanize)
    end

    it "includes points and activity information" do
      expect(mail.body.encoded).to include("Points Balance: #{user.wallet&.points || 0}")
      expect(mail.body.encoded).to include("Total Sales: #{user.sales.count}")
      expect(mail.body.encoded).to include("Total Orders: #{user.orders.count}")
    end

    context "when user has a store" do
      let(:store) { create(:store) }
      let(:user) { create(:user, store: store) }

      it "includes store information" do
        expect(mail.body.encoded).to include(store.name)
        expect(mail.body.encoded).to include("Store ID: ##{store.id}")
      end
    end

    context "with urgent priority" do
      let(:support_ticket) { create(:support_ticket, user: user, priority: "urgent") }

      it "highlights urgent priority" do
        expect(mail.body.encoded).to include("urgent")
        expect(mail.body.encoded).to include('class="urgent"')
      end
    end

    it "includes action link to admin portal" do
      expect(mail.body.encoded).to include(admin_support_ticket_url(support_ticket))
      expect(mail.body.encoded).to include("View Ticket ##{support_ticket.id}")
    end
  end

  describe "#admin_response" do
    let(:support_ticket) do
      create(:support_ticket, :with_admin_response,
        user: user,
        admin_user: admin_user,
        admin_response: "Thank you for your question. Here is the solution...")
    end
    let(:mail) { SupportTicketMailer.admin_response(support_ticket) }

    it "renders the headers" do
      expect(mail.subject).to eq("Re: #{support_ticket.subject} [Ticket ##{support_ticket.id}]")
      expect(mail.to).to eq([user.email])
    end

    it "renders the body with admin response" do
      expect(mail.body.encoded).to include("Thank you for your question. Here is the solution...")
      expect(mail.body.encoded).to include(support_ticket.subject)
      expect(mail.body.encoded).to include(support_ticket.message)
    end

    it "includes admin information" do
      expect(mail.body.encoded).to include(admin_user.email)
      expect(mail.body.encoded).to include(support_ticket.responded_at.strftime("%B %d, %Y"))
    end

    it "includes ticket metadata" do
      expect(mail.body.encoded).to include("Ticket ID: ##{support_ticket.id}")
      expect(mail.body.encoded).to include(support_ticket.category_name)
      expect(mail.body.encoded).to include(support_ticket.status.humanize)
    end

    context "when admin_user is nil" do
      let(:support_ticket) do
        create(:support_ticket,
          user: user,
          admin_user: nil,
          admin_response: "Response without specific admin",
          responded_at: 1.hour.ago)
      end

      it "shows generic support team name" do
        expect(mail.body.encoded).to include("Zeiss Points Support Team")
      end
    end
  end

  describe "#auto_response" do
    let(:support_ticket) { create(:support_ticket, user: user, category: "technical") }
    let(:mail) { SupportTicketMailer.auto_response(support_ticket) }

    it "renders the headers" do
      expect(mail.subject).to eq("Thank you for contacting Zeiss Points Support [Ticket ##{support_ticket.id}]")
      expect(mail.to).to eq([user.email])
    end

    it "renders the body" do
      expect(mail.body.encoded).to include(support_ticket.subject)
      expect(mail.body.encoded).to include(support_ticket.category_name)
      expect(mail.body.encoded).to include("ticket ##{support_ticket.id}")
    end

    it "includes helpful resources section" do
      expect(mail.body.encoded).to include("helpful resources")
      expect(mail.body.encoded).to include("While you wait")
    end

    context "for technical category" do
      it "includes technical help content" do
        expect(mail.body.encoded).to include("Barcode Scanner Not Working?")
        expect(mail.body.encoded).to include("camera permissions")
        expect(mail.body.encoded).to include("App Running Slowly?")
      end
    end

    context "for general category" do
      let(:support_ticket) { create(:support_ticket, user: user, category: "general") }

      it "includes general help content" do
        expect(mail.body.encoded).to include("How do I earn points?")
        expect(mail.body.encoded).to include("How do I redeem points?")
      end
    end

    context "for bug category" do
      let(:support_ticket) { create(:support_ticket, user: user, category: "bug") }

      it "includes bug reporting guidance" do
        expect(mail.body.encoded).to include("Reporting a Bug?")
        expect(mail.body.encoded).to include("device type, app version")
      end
    end

    context "for feature category" do
      let(:support_ticket) { create(:support_ticket, user: user, category: "feature") }

      it "includes feature request acknowledgment" do
        expect(mail.body.encoded).to include("Feature Requests")
        expect(mail.body.encoded).to include("development team reviews")
      end
    end

    it "includes ticket metadata" do
      expect(mail.body.encoded).to include("Ticket ID: ##{support_ticket.id}")
      expect(mail.body.encoded).to include(support_ticket.category_name)
      expect(mail.body.encoded).to include(support_ticket.created_at.strftime("%B %d, %Y"))
    end
  end
end
