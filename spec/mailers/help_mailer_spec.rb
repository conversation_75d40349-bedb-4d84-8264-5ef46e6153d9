require "rails_helper"

RSpec.describe <PERSON>Mailer, type: :mailer do
  describe "#new_question" do
    let(:user) { create(:user) }
    let(:question_params) do
      {
        subject: "Test Subject",
        message: "This is a test message with multiple lines.\n\nIt has paragraphs too.",
        category: "technical"
      }
    end
    let(:mail) { HelpMailer.new_question(user, question_params) }

    it "renders the headers" do
      expect(mail.subject).to eq("[Zeiss Points Support] Technical: Test Subject")
      expect(mail.to).to eq(["<EMAIL>"])
      expect(mail.reply_to).to eq([user.email])
    end

    it "renders the body" do
      expect(mail.body.encoded).to include("Test Subject")
      expect(mail.body.encoded).to include("This is a test message")
      expect(mail.body.encoded).to include(user.email)
      expect(mail.body.encoded).to include("Technical")
    end

    it "includes user information" do
      expect(mail.body.encoded).to include("User ID: ##{user.id}")
      expect(mail.body.encoded).to include(user.email)
    end

    context "when user has a store" do
      let(:store) { create(:store) }
      let(:user) { create(:user, store: store) }

      it "includes store information" do
        expect(mail.body.encoded).to include(store.name)
        expect(mail.body.encoded).to include("Store ID: ##{store.id}")
      end
    end
  end
end
