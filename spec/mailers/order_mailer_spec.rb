require "rails_helper"

RSpec.describe OrderMailer, type: :mailer do
  let(:user) { create(:user) }
  let(:admin) { create(:user, role: :admin) }
  let(:order) { create(:order, user: user) }
  let(:product) { create(:product) }
  let!(:line_item) { create(:line_item, order: order, product: product) }

  describe "#new_order_submitted" do
    let(:mail) { OrderMailer.with(record: order, recipient: admin).new_order_submitted }

    it "renders the headers" do
      expect(mail.subject).to eq("New Order ##{order.id} Pending Approval - #{order.total_points} Points")
      expect(mail.to).to eq([admin.email])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to match("New Order Pending Approval")
      expect(mail.body.encoded).to match(user.email)
      expect(mail.body.encoded).to match(order.total_points.to_s)
    end
  end

  describe "#order_approved" do
    let(:mail) { OrderMailer.with(record: order, recipient: user, approved_by: admin).order_approved }

    it "renders the headers" do
      expect(mail.subject).to eq("Order ##{order.id} Approved - #{order.total_points} Points Deducted")
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to match("Order Approved!")
      expect(mail.body.encoded).to match(order.total_points.to_s)
    end
  end

  describe "#order_rejected" do
    let(:reason) { "Insufficient points" }
    let(:mail) { OrderMailer.with(record: order, recipient: user, rejected_by: admin, reason: reason).order_rejected }

    it "renders the headers" do
      expect(mail.subject).to eq("Order ##{order.id} Rejected")
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to match("Order Rejected")
      expect(mail.body.encoded).to match(reason)
    end
  end

  describe "#order_shipped" do
    let(:mail) { OrderMailer.with(record: order, recipient: user, shipped_by: admin).order_shipped }

    it "renders the headers" do
      expect(mail.subject).to eq("Order ##{order.id} Shipped - Tracking Information")
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to match("Order Shipped!")
      expect(mail.body.encoded).to match("on its way")
    end
  end

  describe "#order_delivered" do
    let(:mail) { OrderMailer.with(record: order, recipient: user).order_delivered }

    it "renders the headers" do
      expect(mail.subject).to eq("Order ##{order.id} Delivered - Thank You!")
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to match("Order Delivered!")
      expect(mail.body.encoded).to match("delivered successfully")
    end
  end
end
