require "rails_helper"

RSpec.describe GiftCardBatchMailer, type: :mailer do
  let(:batch) { create(:gift_card_batch) }
  let(:user) { create(:user) }

  it "sends batch processed email" do
    mail = described_class.batch_processed(batch, user)
    expect(mail.subject).to include("Gift Card Batch Processed")
    expect(mail.to).to include(user.email)
  end

  # Add more tests for other mailer methods
end
