require "rails_helper"

RSpec.describe "Comprehensive Help Flow", type: :feature do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user) }

  describe "Complete user help journey" do
    before do
      sign_in_user(user)
    end

    scenario "User discovers help, reads FAQ, and submits question" do
      # User starts on dashboard
      visit root_path
      expect(page).to have_content("Dashboard")

      # User notices help tab in bottom navigation
      within(".bottom-navigation") do
        expect(page).to have_link("Help")
        click_link "Help"
      end

      # User arrives at help page
      expect(current_path).to eq(help_index_path)
      expect(page).to have_content("Help & Support")
      expect(page).to have_content("Find answers and get assistance")

      # User reads through FAQ
      expect(page).to have_content("Frequently Asked Questions")
      expect(page).to have_content("How do I earn points?")

      # User expands first FAQ item (if JavaScript is enabled)
      if page.driver.is_a?(Capybara::Selenium::Driver)
        first(".faq-toggle").click
        expect(page).to have_css(".faq-content:not(.hidden)", wait: 1)
      end

      # User doesn't find their answer in FAQ, decides to ask a question
      click_link "Ask a Question"

      # User arrives at contact form
      expect(current_path).to eq(help_contact_path)
      expect(page).to have_content("Contact Support")
      expect(page).to have_content("Send us your question")

      # User sees their information is pre-filled
      expect(page).to have_content(user.email)
      expect(page).to have_content("##{user.id}")

      # User fills out the form
      select "Sales & Points", from: "question_category"
      fill_in "question_subject", with: "Question about bonus points calculation"
      fill_in "question_message", with: "I sold a premium lens package but only received standard points. The promotion said I should get bonus points. Can you help me understand what happened?"

      # User submits the question
      expect {
        click_button "Send Message"
      }.to change(SupportTicket, :count).by(1)

      # User sees confirmation
      expect(current_path).to eq(help_index_path)
      expect(page).to have_content("submitted to our support team")
      expect(page).to have_content("within 24 hours")

      # Verify the ticket was created correctly
      ticket = SupportTicket.last
      expect(ticket.user).to eq(user)
      expect(ticket.category).to eq("sales")
      expect(ticket.subject).to eq("Question about bonus points calculation")
      expect(ticket.status).to eq("open")
      expect(ticket.admin_handled?).to be true
    end

    scenario "User submits technical question and gets auto-response" do
      visit help_contact_path

      # User submits a technical question
      select "Technical Issue", from: "question_category"
      fill_in "question_subject", with: "Barcode scanner not working on iPhone"
      fill_in "question_message", with: "The barcode scanner opens but the camera view is black. I've tried restarting the app but it doesn't help."

      expect {
        click_button "Send Message"
      }.to change(SupportTicket, :count).by(1)

      # User sees different message for self-service categories
      expect(page).to have_content("sent some helpful information")

      # Verify the ticket was created as self-service
      ticket = SupportTicket.last
      expect(ticket.self_service?).to be true
      expect(ticket.category).to eq("technical")
    end

    scenario "User navigates help system using different entry points" do
      # Entry point 1: From profile page
      visit profile_path
      click_link "Help & Support"
      expect(current_path).to eq(help_index_path)

      # Go back to profile
      click_link "", href: profile_path
      expect(current_path).to eq(profile_path)

      # Entry point 2: From bottom navigation
      within(".bottom-navigation") do
        click_link "Help"
      end
      expect(current_path).to eq(help_index_path)

      # Entry point 3: Direct URL access
      visit help_contact_path
      expect(page).to have_content("Contact Support")

      # Navigation flow: Contact -> Help -> Dashboard
      click_link "", href: help_index_path  # Back to help
      expect(current_path).to eq(help_index_path)

      click_link "", href: root_path  # Back to dashboard
      expect(current_path).to eq(root_path)
    end
  end

  describe "Admin support ticket management flow" do
    let!(:support_ticket) { create(:support_ticket, user: user, category: "sales", status: "open") }

    before do
      sign_in_user(admin_user)
    end

    scenario "Admin receives notification, reviews ticket, and responds" do
      # Admin visits admin dashboard
      visit admin_root_path
      expect(page).to have_content("Admin Dashboard")

      # Admin sees support tickets in navigation with badge
      within(".admin-navigation") do
        expect(page).to have_link("Support Tickets")
        # Badge should show open admin-handled tickets
        open_count = SupportTicket.open.admin_handled.count
        expect(page).to have_content(open_count.to_s) if open_count > 0
      end

      # Admin clicks on support tickets
      click_link "Support Tickets"
      expect(current_path).to eq(admin_support_tickets_path)

      # Admin sees tickets list with filters
      expect(page).to have_content("Support Tickets")
      expect(page).to have_content("Manage user questions")
      expect(page).to have_link("All Tickets")
      expect(page).to have_link("Open")

      # Admin sees the ticket in the list
      expect(page).to have_content("##{support_ticket.id}")
      expect(page).to have_content(support_ticket.subject)
      expect(page).to have_content(support_ticket.user.email)

      # Admin clicks on the ticket to view details
      click_link "##{support_ticket.id}"
      expect(current_path).to eq(admin_support_ticket_path(support_ticket))

      # Admin sees full ticket details
      expect(page).to have_content("Support Ticket ##{support_ticket.id}")
      expect(page).to have_content(support_ticket.subject)
      expect(page).to have_content(support_ticket.message)

      # Admin sees user information
      within(".user-info-sidebar") do
        expect(page).to have_content("User Information")
        expect(page).to have_content(support_ticket.user.email)
        expect(page).to have_content("Points Balance")
      end

      # Admin updates ticket status to in progress
      select "In Progress", from: "support_ticket_status"
      click_button "Update"
      expect(page).to have_content("updated successfully")

      # Admin writes a response
      response_text = "Thank you for your question about bonus points. I've reviewed your account and can see the issue. The promotion you mentioned requires a specific product code to be scanned. Let me help you resolve this..."

      fill_in "support_ticket_admin_response", with: response_text

      # Admin sends the response
      expect {
        click_button "Send Response"
      }.to change { ActionMailer::Base.deliveries.count }.by(1)

      expect(page).to have_content("Response sent successfully")

      # Verify ticket was updated
      support_ticket.reload
      expect(support_ticket.admin_response).to eq(response_text)
      expect(support_ticket.admin_user).to eq(admin_user)
      expect(support_ticket.status).to eq("resolved")
      expect(support_ticket.responded_at).to be_present

      # Admin can see the response in the UI
      expect(page).to have_content("Your Response")
      expect(page).to have_content(response_text)
    end

    scenario "Admin filters and manages multiple tickets" do
      # Create tickets with different statuses and categories
      create(:support_ticket, user: user, status: "open", category: "orders")
      create(:support_ticket, user: user, status: "resolved", category: "account")
      create(:support_ticket, user: user, status: "open", category: "technical")  # Self-service

      visit admin_support_tickets_path

      # Admin sees all tickets initially
      expect(page).to have_css(".ticket-item", minimum: 3)

      # Admin filters by open status
      click_link "Open"
      expect(current_url).to include("status=open")

      # Should only see open tickets
      open_tickets = page.all(".ticket-item")
      expect(open_tickets.count).to eq(SupportTicket.open.count)

      # Admin filters by resolved status
      click_link "Resolved"
      expect(current_url).to include("status=resolved")

      # Admin goes back to all tickets
      click_link "All Tickets"
      expect(page).to have_css(".ticket-item", minimum: 3)
    end
  end

  describe "End-to-end email flow" do
    scenario "Complete email notification flow" do
      # User submits question
      sign_in_user(user)
      visit help_contact_path

      select "Sales & Points", from: "question_category"
      fill_in "question_subject", with: "Points not credited for recent sale"
      fill_in "question_message", with: "I submitted a sale yesterday but the points haven't appeared in my account yet."

      # Email should be sent to admins
      expect {
        click_button "Send Message"
      }.to change { ActionMailer::Base.deliveries.count }.by(1)

      admin_email = ActionMailer::Base.deliveries.last
      expect(admin_email.to).to include("<EMAIL>")
      expect(admin_email.subject).to include("Sales & Points")

      # Admin responds
      sign_out
      sign_in_user(admin_user)

      ticket = SupportTicket.last
      visit admin_support_ticket_path(ticket)

      fill_in "support_ticket_admin_response", with: "I've checked your account and can see the sale. Points are typically credited within 24-48 hours after admin approval. Your sale is currently being reviewed."

      # User should receive response email
      expect {
        click_button "Send Response"
      }.to change { ActionMailer::Base.deliveries.count }.by(1)

      user_email = ActionMailer::Base.deliveries.last
      expect(user_email.to).to include(user.email)
      expect(user_email.subject).to include(ticket.subject)
    end
  end

  describe "Mobile help experience" do
    before do
      page.driver.browser.manage.window.resize_to(375, 667)  # iPhone SE size
      sign_in_user(user)
    end

    scenario "User completes help flow on mobile device" do
      visit root_path

      # Mobile bottom navigation should work
      within(".bottom-navigation") do
        click_link "Help"
      end

      expect(current_path).to eq(help_index_path)

      # FAQ should be readable on mobile
      expect(page).to have_content("Frequently Asked Questions")

      # Contact form should be usable on mobile
      click_link "Ask a Question"

      expect(page).to have_field("question_subject")
      expect(page).to have_field("question_message")

      # Form should submit successfully on mobile
      select "General Question", from: "question_category"
      fill_in "question_subject", with: "Mobile app question"
      fill_in "question_message", with: "Testing the mobile help experience."

      click_button "Send Message"

      expect(page).to have_content("sent some helpful information")
    end
  end

  describe "Error handling and edge cases" do
    before do
      sign_in_user(user)
    end

    scenario "User submits invalid form data" do
      visit help_contact_path

      # Submit form with missing required fields
      click_button "Send Message"

      expect(page).to have_content("error submitting")
      expect(current_path).to eq(help_submit_question_path)

      # Form should retain any valid data entered
      # User can correct and resubmit
      select "Technical Issue", from: "question_category"
      fill_in "question_subject", with: "Valid subject line"
      fill_in "question_message", with: "This is a valid message with enough content to pass validation."

      expect {
        click_button "Send Message"
      }.to change(SupportTicket, :count).by(1)

      expect(page).to have_content("sent some helpful information")
    end

    scenario "User tries to access help without authentication" do
      sign_out

      visit help_index_path
      expect(current_path).to eq(new_user_session_path)

      visit help_contact_path
      expect(current_path).to eq(new_user_session_path)
    end
  end
end
