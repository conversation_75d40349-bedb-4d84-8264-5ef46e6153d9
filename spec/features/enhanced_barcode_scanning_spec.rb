require "rails_helper"

RSpec.feature "Enhanced Barcode Scanning", type: :feature do
  let(:brand) { create(:brand) }
  let(:store_chain) { create(:store_chain) }
  let(:store) { create(:store, store_chain: store_chain, brand: brand, status: :active) }
  let(:user) { create(:user, store: store) }
  let(:category) { create(:category, brand: brand) }
  let!(:product) { create(:product, category: category, name: "Test Product") }

  before do
    # Ensure @products includes the created product for the form
    allow_any_instance_of(ActionController::Base).to receive(:view_assigns).and_wrap_original do |m, *args|
      assigns = m.call(*args)
      assigns["products"] ||= []
      assigns["products"] << product unless assigns["products"].include?(product)
      assigns
    end
    # Ensure the user has an address and the product has a ProductCountryDatum for the user's country
    country = create(:country, code: "CA")
    admin_user = create(:user, email: "<EMAIL>", role: :admin, store: store)
    region = create(:region, admin_user: admin_user)
    state = create(:state, region: region)
    user.create_address(country: country, state: state, street: "123 Main", city: "Test", postal_code: "12345")
    create(:product_country_datum, product: product, country: country, points_earned: 100)
    user.update(password: "password123", password_confirmation: "password123")
    sign_in_with_capybara(user, "password123")
  end

  scenario "User can access barcode scanner", js: true do
    visit new_sale_path
    expect(page).to have_css("[data-controller='barcode']")
    expect(page).to have_css("[data-barcode-target='input']")
    expect(page).to have_css("[data-barcode-btn]")
    expect(page).to have_content("Tap the barcode icon to scan")
  end

  scenario "Barcode scanner button has proper styling and positioning" do
    visit new_sale_path

    # Check button styling
    scan_button = find("[data-barcode-btn]")
    expect(scan_button[:class]).to include("absolute", "inset-y-0", "right-0")

    # Check SVG icon is present
    expect(scan_button).to have_css("svg")

    # Check parent container has relative positioning
    input_container = find("[data-barcode-target='input']").find(:xpath, "..")
    expect(input_container[:style]).to include("position: relative")
  end

  scenario "Barcode controller has configurable values" do
    visit new_sale_path

    # Check default configuration values are set
    controller_element = find("[data-controller='barcode']")

    # Should have default readers
    expect(controller_element["data-barcode-readers-value"]).to be_present

    # Should have default frequency
    expect(controller_element["data-barcode-frequency-value"]).to be_present

    # Should have default half-sample setting
    expect(controller_element["data-barcode-half-sample-value"]).to be_present

    # Should have default patch size
    expect(controller_element["data-barcode-patch-size-value"]).to be_present
  end

  scenario "Form submission works with barcode input" do
    visit new_sale_path

    # Fill in form including barcode field
    select product.name, from: "sale_product_id"
    fill_in "Serial Number", with: "TEST123456789"
    fill_in "Sale Date", with: Date.current

    # Submit form
    click_button "Record Sale"

    # Should redirect to dashboard
    expect(current_path).to eq(root_path)
    # Accept either the flash or the dashboard content as success
    expect(page).to have_content("Sale recorded successfully").or have_content("TEST123456789").or have_content(product.name)

    # Check sale was created with correct serial number
    sale = Sale.find_by(serial_number: "TEST123456789")
    expect(sale).not_to be_nil
    expect(sale.serial_number).to eq("TEST123456789")
  end

  scenario "Barcode scanner integrates with points calculation", js: true do
    # Increase wait time for this test
    using_wait_time(10) do
      # Ensure we're authenticated by visiting dashboard first
      visit root_path
      expect(page).to have_content("Welcome back"), "User should be authenticated"

      # Now visit the sales page
      visit new_sale_path
      expect(page).to have_content("Add Sale"), "Should be on the sales page"

      # Select product to trigger points calculation
      select product.name, from: "sale_product_id"

      # Wait for points field to be set by JS
      expect(page).to have_selector("#sale_points[value='100']", visible: :all)
      expect(page).to have_content("100")
      expect(page).to have_content("points")

      points_value = find(:css, "#sale_points", visible: :hidden).value
      expect(points_value).to eq("100")

      # Fill in barcode field with a unique serial number
      unique_serial = "BARCODE#{SecureRandom.hex(4).upcase}"

      fill_in "Serial Number", with: unique_serial
      fill_in "Sale Date", with: Date.current.strftime("%Y-%m-%d")

      click_button "Record Sale"

      # Wait for the form submission to complete
      expect(page).to have_content("Sale recorded successfully").or(have_content("Please fix the following errors"))

      # Verify sale was created with correct points
      sale = Sale.find_by(serial_number: unique_serial)
      expect(sale).not_to be_nil, "Sale with serial number #{unique_serial} was not created. Current sales: #{Sale.pluck(:serial_number)}"
      expect(sale.points).to eq(100)
      expect(sale.serial_number).to eq(unique_serial)
    end
  end

  scenario "Enhanced barcode field maintains mobile-first design" do
    # Test mobile viewport
    if page.driver.respond_to?(:browser) && page.driver.browser.respond_to?(:manage)
      page.driver.browser.manage.window.resize_to(375, 667)
      visit new_sale_path

      # Check mobile-optimized input styling
      serial_input = find("[data-barcode-target='input']")
      expect(serial_input[:class]).to include("w-full", "px-4", "py-3", "rounded-xl")
      expect(serial_input[:class]).to include("text-base", "sm:text-sm")

      # Check barcode button is properly positioned for mobile
      scan_button = find("[data-barcode-btn]")
      expect(scan_button[:class]).to include("absolute", "right-0")

      # Check placeholder text
      expect(serial_input[:placeholder]).to eq("Enter or scan serial number")
    else
      skip "Window resizing not supported by this Capybara driver."
    end
  end

  scenario "Barcode scanner handles validation errors gracefully" do
    visit new_sale_path

    # Try to submit without required fields
    click_button "Record Sale"

    # Should show validation errors
    expect(page).to have_content("Please fix the following errors")

    # Barcode scanner should still be functional
    expect(page).to have_css("[data-controller='barcode']")
    expect(page).to have_css("[data-barcode-btn]")
  end

  scenario "Barcode scanner works with form repopulation on errors" do
    visit new_sale_path

    # Fill in some fields including barcode
    select product.name, from: "sale_product_id"
    fill_in "Serial Number", with: "TEST1234"
    # Explicitly clear the date field to ensure it is blank
    fill_in "Sale Date", with: ""

    click_button "Record Sale"

    # Should show validation errors but preserve form data
    expect(page).to have_content("Please fix the following errors")
    expect(find_field("Serial Number").value).to eq("TEST1234")
    expect(find_field("sale_product_id").value).to eq(product.id.to_s)

    # Barcode scanner should still work
    expect(page).to have_css("[data-controller='barcode']")
  end

  scenario "Enhanced barcode scanner maintains accessibility" do
    visit new_sale_path

    # Check form labels are properly associated
    expect(page).to have_css("label[for='sale_serial_number']", text: "Serial Number")

    # Check input has proper attributes
    serial_input = find("#sale_serial_number")
    expect(serial_input[:required]).to be_present
    expect(serial_input[:placeholder]).to be_present

    # Check barcode button has title attribute for accessibility
    scan_button = find("[data-barcode-btn]")
    expect(scan_button[:title]).to be_present
  end
end
