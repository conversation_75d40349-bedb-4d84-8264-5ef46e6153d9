require "rails_helper"

RSpec.describe "Complete Help Workflow", type: :feature do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user) }

  describe "End-to-end help system workflow" do
    it "allows user to submit question and admin to respond" do
      # Step 1: User discovers help system
      sign_in_user(user)
      visit root_path

      # User sees help in navigation
      within(".bottom-navigation") do
        expect(page).to have_link("Help")
      end

      # Step 2: User browses FAQ first
      click_link "Help"
      expect(page).to have_content("Frequently Asked Questions")
      expect(page).to have_content("How do I earn points?")

      # Step 3: User doesn't find answer, contacts support
      click_link "Ask a Question"
      expect(page).to have_content("Send us a Message")

      # Step 4: User submits a sales-related question
      select "Sales & Points", from: "question_category"
      fill_in "question_subject", with: "Points not credited for recent sale"
      fill_in "question_message", with: "I submitted a sale for a Zeiss lens yesterday but the points haven't been added to my account yet. The sale was for $500 and I expected 50 points. Can you help me understand what happened?"

      expect {
        click_button "Send Message"
      }.to change(SupportTicket, :count).by(1)

      expect(page).to have_content("submitted to our support team")

      # Verify ticket was created correctly
      ticket = SupportTicket.last
      expect(ticket.user).to eq(user)
      expect(ticket.category).to eq("sales")
      expect(ticket.status).to eq("open")
      expect(ticket.admin_handled?).to be true

      # Step 5: Admin receives notification (email sent)
      expect(ActionMailer::Base.deliveries.count).to eq(1)
      admin_email = ActionMailer::Base.deliveries.last
      expect(admin_email.to).to include("<EMAIL>")
      expect(admin_email.subject).to include("Sales & Points")

      sign_out

      # Step 6: Admin logs in and sees ticket
      sign_in_user(admin_user)
      visit admin_support_tickets_path

      expect(page).to have_content("Ticket ##{ticket.id}")
      expect(page).to have_content("Points not credited for recent sale")

      # Step 7: Admin views ticket details
      click_link "##{ticket.id}"

      expect(page).to have_content(ticket.subject)
      expect(page).to have_content(ticket.message)
      expect(page).to have_content(user.email)

      # Step 8: Admin updates ticket status
      select "In Progress", from: "support_ticket_status"
      click_button "Update"

      expect(page).to have_content("updated successfully")

      # Step 9: Admin responds to ticket
      response_text = "Thank you for contacting us about your points. I've reviewed your account and found that your sale is currently pending approval. Sales typically take 1-2 business days to be processed and points credited. Your sale for $500 should indeed result in 50 points once approved. I'll expedite the approval process for you."

      fill_in "support_ticket_admin_response", with: response_text

      expect {
        click_button "Send Response"
      }.to change { ActionMailer::Base.deliveries.count }.by(1)

      expect(page).to have_content("Response sent successfully")

      # Verify ticket was updated
      ticket.reload
      expect(ticket.admin_response).to eq(response_text)
      expect(ticket.admin_user).to eq(admin_user)
      expect(ticket.status).to eq("resolved")

      # Step 10: User receives email response
      user_email = ActionMailer::Base.deliveries.last
      expect(user_email.to).to include(user.email)
      expect(user_email.subject).to include(ticket.subject)

      sign_out

      # Step 11: User can see the workflow completed
      sign_in_user(user)

      # User could potentially view their ticket history (future feature)
      # For now, they receive the email notification

      expect(ticket.reload.status).to eq("resolved")
      expect(ticket.admin_response).to be_present
    end
  end

  describe "Self-service help workflow" do
    it "provides immediate help for technical questions" do
      sign_in_user(user)

      # User submits technical question
      visit help_contact_path

      select "Technical Issue", from: "question_category"
      fill_in "question_subject", with: "Barcode scanner not working on iPhone"
      fill_in "question_message", with: "The barcode scanner opens but doesn't scan any codes. I've tried cleaning my camera lens."

      expect {
        click_button "Send Message"
      }.to change(SupportTicket, :count).by(1)

      # User gets immediate helpful response
      expect(page).to have_content("sent some helpful information")

      # Verify auto-response email sent
      ticket = SupportTicket.last
      expect(ticket.self_service?).to be true

      email = ActionMailer::Base.deliveries.last
      expect(email.to).to include(user.email)
      expect(email.subject).to include("Thank you for contacting")

      # Email should contain helpful technical tips
      expect(email.body.encoded).to include("Barcode Scanner Not Working?")
      expect(email.body.encoded).to include("camera permissions")
    end
  end

  describe "Help system accessibility" do
    it "provides accessible navigation and forms" do
      sign_in_user(user)
      visit help_index_path

      # Check for proper heading structure
      expect(page).to have_css("h1", text: "Help & Support")
      expect(page).to have_css("h2", text: "Need Help?")
      expect(page).to have_css("h3", text: "Frequently Asked Questions")

      # Check for proper form labels
      visit help_contact_path

      expect(page).to have_css('label[for="question_category"]')
      expect(page).to have_css('label[for="question_subject"]')
      expect(page).to have_css('label[for="question_message"]')

      # Check for proper button text
      expect(page).to have_button("Send Message")
    end
  end

  describe "Help system performance" do
    it "handles multiple concurrent help requests" do
      # Create multiple users submitting questions simultaneously
      users = create_list(:user, 5)

      users.each_with_index do |test_user, index|
        sign_in_user(test_user)
        visit help_contact_path

        select "General Question", from: "question_category"
        fill_in "question_subject", with: "Test question #{index + 1}"
        fill_in "question_message", with: "This is test message number #{index + 1} to verify system performance."

        click_button "Send Message"
        expect(page).to have_content("submitted")

        sign_out
      end

      # Verify all tickets were created
      expect(SupportTicket.count).to eq(5)

      # Verify emails were sent
      expect(ActionMailer::Base.deliveries.count).to eq(5)
    end
  end

  describe "Help system error handling" do
    it "handles form validation errors gracefully" do
      sign_in_user(user)
      visit help_contact_path

      # Submit form with missing required fields
      click_button "Send Message"

      expect(page).to have_content("error submitting")
      expect(page).to have_field("question_subject")  # Form should still be present
      expect(page).to have_field("question_message")
    end

    it "handles network errors gracefully" do
      sign_in_user(user)
      visit help_contact_path

      # Fill out valid form
      select "General Question", from: "question_category"
      fill_in "question_subject", with: "Valid question"
      fill_in "question_message", with: "This is a valid question with enough content."

      # Simulate network error by stubbing the mailer to raise an error
      allow(SupportTicketMailer).to receive(:auto_response).and_raise(StandardError.new("Network error"))

      # Form should still work even if email fails
      expect {
        click_button "Send Message"
      }.to change(SupportTicket, :count).by(1)

      # User should still see success (ticket was created even if email failed)
      expect(page).to have_content("submitted")
    end
  end

  describe "Mobile help workflow" do
    it "works seamlessly on mobile devices" do
      page.driver.browser.manage.window.resize_to(375, 667)  # iPhone SE size

      sign_in_user(user)
      visit root_path

      # Mobile navigation should work
      within(".bottom-navigation") do
        click_link "Help"
      end

      expect(page).to have_content("Help & Support")

      # Contact form should work on mobile
      click_link "Ask a Question"

      select "Account & Profile", from: "question_category"
      fill_in "question_subject", with: "Mobile app question"
      fill_in "question_message", with: "Testing the help system on mobile device."

      click_button "Send Message"

      expect(page).to have_content("submitted")
      expect(SupportTicket.last.category).to eq("account")
    end
  end
end
