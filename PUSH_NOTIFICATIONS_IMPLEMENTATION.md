# Push Notification Subscriptions Implementation

## Overview

This implementation adds support for storing and managing user push notification subscriptions, allowing users to subscribe multiple devices for push notifications.

## Files Created/Modified

### Database Migration
- `db/migrate/20250115000007_create_push_notification_subscriptions.rb`
  - Creates the `push_notification_subscriptions` table
  - Stores endpoint, p256dh_key, auth_key, user_agent, device_name, and active status
  - Includes unique index on user_id + endpoint to prevent duplicates

### Models
- `app/models/push_notification_subscription.rb`
  - Main model for managing push subscriptions
  - Includes validation, scopes, and utility methods
  - Handles device detection and subscription management

- `app/models/user.rb` (modified)
  - Added `has_many :push_notification_subscriptions` relationship
  - Added helper methods for managing subscriptions:
    - `active_push_subscriptions`
    - `has_push_subscriptions?`
    - `add_push_subscription`
    - `remove_push_subscription`
    - `cleanup_stale_subscriptions!`

### Controller
- `app/controllers/push_subscriptions_controller.rb`
  - RESTful API for managing push subscriptions
  - Endpoints for create, destroy, index, cleanup, and test
  - Handles device detection from user agent

### Frontend
- `app/javascript/controllers/push_notifications_controller.js`
  - Stimulus controller for handling push notification subscription in the browser
  - Manages service worker registration and subscription
  - Handles VAPID key conversion and API communication

### Views
- `app/views/shared/_push_notifications.html.erb`
  - Reusable partial for push notification management UI
  - Includes subscribe/unsubscribe buttons and status display

### Notifier
- `app/notifiers/delivery_methods/web_push.rb` (modified)
  - Updated to use the new subscription model
  - Handles sending push notifications to all active user subscriptions
  - Includes error handling for invalid/expired subscriptions

### Tests
- `spec/models/push_notification_subscription_spec.rb`
- `spec/models/user_push_notifications_spec.rb`
- `spec/factories/push_notification_subscriptions.rb`

### Routes
- Added push subscription routes in `config/routes.rb`

## Key Features

1. **Multiple Device Support**: Users can subscribe multiple devices
2. **Automatic Cleanup**: Stale subscriptions (>30 days) can be cleaned up
3. **Device Detection**: Automatic device name detection from user agent
4. **Error Handling**: Invalid/expired subscriptions are automatically deactivated
5. **Testing Support**: Test endpoint to verify push notifications work
6. **Unique Constraints**: Prevents duplicate subscriptions per user/endpoint

## Database Schema

```ruby
create_table :push_notification_subscriptions do |t|
  t.references :user, null: false, foreign_key: true
  t.text :endpoint, null: false
  t.string :p256dh_key, null: false
  t.string :auth_key, null: false
  t.text :user_agent
  t.string :device_name
  t.boolean :active, default: true, null: false
  t.datetime :last_used_at
  t.timestamps
end
```

## Usage

### In Views
```erb
<%= render 'shared/push_notifications' %>
```

### In Controllers/Services
```ruby
# Add a subscription
user.add_push_subscription(
  endpoint: subscription_data[:endpoint],
  p256dh_key: subscription_data[:p256dh],
  auth_key: subscription_data[:auth],
  device_name: 'iPhone 15'
)

# Send notification to all user devices
notification.deliver(user)

# Cleanup stale subscriptions
user.cleanup_stale_subscriptions!
```

## Configuration Required

Add VAPID keys to Rails credentials:
```yaml
web_push:
  vapid_public_key: "your_public_key"
  vapid_private_key: "your_private_key"
  vapid_subject: "mailto:<EMAIL>"
```

## Next Steps

1. Run the migration: `rails db:migrate`
2. Configure VAPID keys in Rails credentials
3. Add the push notifications partial to user profile/settings pages
4. Configure the web push delivery method in your notifiers
5. Set up service worker for handling push notifications