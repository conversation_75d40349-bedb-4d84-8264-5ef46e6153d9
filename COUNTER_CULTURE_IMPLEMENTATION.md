# Counter Culture Implementation for Order Points

## Overview

Implemented the `counter_culture` gem to maintain running totals of points on orders instead of calculating them on-demand. This improves performance by avoiding expensive calculations and database queries.

## Implementation Details

### 1. Gem Installation

Added to `Gemfile`:

```ruby
gem "counter_culture", "~> 3.0"
```

### 2. Database Migration

**File**: `db/migrate/20250115000010_add_line_items_count_and_total_points_to_orders.rb`

Added two counter cache columns to the `orders` table:

- `line_items_count` - Tracks number of line items in the order
- `total_points_cache` - Maintains running total of points from all line items

Both columns are indexed for performance.

### 3. LineItem Model Updates

**File**: `app/models/line_item.rb`

Added counter_culture declarations:

```ruby
# Counter culture for maintaining order totals
counter_culture :order,
  column_name: proc { |model| model.order_id? ? 'total_points_cache' : nil },
  delta_column: 'total_points'

counter_culture :order, column_name: 'line_items_count'
```

**How it works:**

- When a line item is added/updated/deleted, counter_culture automatically updates the order's counters
- `total_points_cache` uses the `total_points` method as the delta value
- Only updates when the line item belongs to an order (not a cart)

### 4. Order Model Updates

**File**: `app/models/order.rb`

Updated `total_points` method to use cached value:

```ruby
def total_points
  # Use counter cache if available, otherwise calculate from line items
  if has_attribute?(:total_points_cache)
    total_points_cache
  elsif line_items.loaded? || line_items.any?
    line_items.sum(&:total_points)
  else
    points
  end
end
```

**Fallback Strategy:**

1. **Primary**: Use `total_points_cache` (fastest)
2. **Secondary**: Calculate from loaded line items
3. **Tertiary**: Use stored `points` attribute

### 5. Rake Tasks for Maintenance

**File**: `lib/tasks/counter_culture.rake`

#### Fix Counter Caches

```bash
rails counter_culture:fix_orders
```

- Recalculates and fixes any incorrect counter cache values
- Useful after data imports or manual database changes

#### Verify Counter Caches

```bash
rails counter_culture:verify_orders
```

- Checks all orders for counter cache accuracy
- Reports any discrepancies without fixing them

### 6. Email Preview Updates

**File**: `spec/mailers/previews/order_mailer_preview.rb`

Updated preview to mock the counter cache attributes for proper email rendering.

## Benefits

### Performance Improvements

- **Before**: `O(n)` calculation requiring database query for each line item
- **After**: `O(1)` lookup from cached column
- Eliminates expensive `SUM()` queries on order display

### Consistency

- Counter caches are automatically maintained by the gem
- No risk of manual calculation errors
- Atomic updates ensure data consistency

### Scalability

- Order listing pages load faster (no per-order calculations)
- Email generation is more efficient
- Admin dashboards perform better with large datasets

## Usage Examples

### Automatic Updates

```ruby
# Adding line items automatically updates counters
order.line_items.create!(product: product, quantity: 2)
# order.total_points_cache and line_items_count are automatically updated

# Updating quantities triggers recalculation
line_item.update!(quantity: 5)
# order.total_points_cache is automatically recalculated

# Deleting line items decrements counters
line_item.destroy
# Both counters are automatically decremented
```

### Manual Verification

```ruby
# Check if counters are accurate
order = Order.find(123)
calculated_total = order.line_items.sum(&:total_points)
cached_total = order.total_points_cache

if calculated_total != cached_total
  puts "Counter cache mismatch detected!"
  # Run: rails counter_culture:fix_orders
end
```

## Migration Strategy

### For Existing Data

1. **Run Migration**: `rails db:migrate`
2. **Populate Counters**: `rails counter_culture:fix_orders`
3. **Verify Accuracy**: `rails counter_culture:verify_orders`

### For New Installations

Counter caches will be automatically maintained from the start.

## Monitoring

### Regular Verification

Add to your deployment process:

```bash
# Verify counter cache accuracy after deployments
rails counter_culture:verify_orders
```

### Performance Monitoring

Monitor query performance improvements:

- Order index pages should show reduced database load
- Email generation should be faster
- Admin dashboards should be more responsive

## Troubleshooting

### Counter Cache Drift

If counters become inaccurate:

```bash
# Fix all orders
rails counter_culture:fix_orders

# Or fix specific order
Order.find(123).line_items.counter_culture_fix_counts
```

### Performance Issues

If you notice performance problems:

1. Ensure indexes are present on counter columns
2. Verify counter_culture gem is properly configured
3. Check for N+1 queries in line item operations

## Testing

### RSpec Examples

```ruby
describe "counter culture" do
  let(:order) { create(:order) }
  let(:product) { create(:product) }

  it "updates total_points_cache when line items change" do
    expect {
      order.line_items.create!(product: product, quantity: 2)
    }.to change { order.reload.total_points_cache }
      .from(0).to(product.points_required * 2)
  end

  it "maintains accurate line_items_count" do
    expect {
      order.line_items.create!(product: product, quantity: 1)
    }.to change { order.reload.line_items_count }.from(0).to(1)
  end
end
```

## Next Steps

1. **Run Migration**: Execute the database migration
2. **Install Gem**: Run `bundle install`
3. **Populate Counters**: Run `rails counter_culture:fix_orders`
4. **Verify Setup**: Run `rails counter_culture:verify_orders`
5. **Test Email Previews**: Verify mailer previews work correctly
6. **Monitor Performance**: Check for improved query performance
