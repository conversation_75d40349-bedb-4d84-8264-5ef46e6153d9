# Counter Culture Model Declarations Implementation

## Overview

Complete implementation of counter_culture declarations across all models to enable automatic counter cache maintenance.

## Implemented Model Declarations

### 1. **User Model** (`app/models/user.rb`)

```ruby
# Counter culture for store analytics
counter_culture :store, column_name: 'users_count'
```

**Tracks**: Number of users per store

### 2. **Sale Model** (`app/models/sale.rb`)

```ruby
# Counter culture for user analytics
counter_culture :user, column_name: 'sales_count'
counter_culture :user,
  column_name: proc { |model| model.approved? ? 'approved_sales_count' : nil }
counter_culture :user,
  column_name: proc { |model| model.pending? ? 'pending_sales_count' : nil }
counter_culture :user,
  column_name: proc { |model| model.approved? ? 'total_points_earned' : nil },
  delta_column: 'points'

# Counter culture for product analytics
counter_culture :product, column_name: 'sales_count'
counter_culture :product,
  column_name: proc { |model| model.approved? ? 'total_points_earned' : nil },
  delta_column: 'points'

# Counter culture for store analytics (through user)
counter_culture [:user, :store],
  column_name: proc { |model| model.user.store_id ? 'sales_count' : nil }
counter_culture [:user, :store],
  column_name: proc { |model| model.approved? && model.user.store_id ? 'approved_sales_count' : nil }
counter_culture [:user, :store],
  column_name: proc { |model| model.pending? && model.user.store_id ? 'pending_sales_count' : nil }
counter_culture [:user, :store],
  column_name: proc { |model| model.approved? && model.user.store_id ? 'total_sales_points' : nil },
  delta_column: 'points'
```

**Tracks**: Sales counts by status, points earned for users, products, and stores

### 3. **Order Model** (`app/models/order.rb`)

```ruby
# Counter culture for user analytics
counter_culture :user, column_name: 'orders_count'

# Counter culture for store analytics (through user)
counter_culture [:user, :store],
  column_name: proc { |model| model.user.store_id ? 'orders_count' : nil }
```

**Tracks**: Number of orders per user and store

### 4. **LineItem Model** (`app/models/line_item.rb`)

```ruby
# Existing order counters
counter_culture :order,
  column_name: proc { |model| model.order_id? ? 'total_points_cache' : nil },
  delta_column: 'total_points'
counter_culture :order, column_name: 'line_items_count'

# New product counters
counter_culture :product, column_name: 'line_items_count'
counter_culture :product,
  column_name: proc { |model| model.order_id? ? 'orders_count' : nil }
counter_culture :product,
  column_name: 'total_quantity_sold',
  delta_column: 'quantity'
```

**Tracks**: Line item counts, order counts, and quantity sold per product

### 5. **Product Model** (`app/models/product.rb`)

```ruby
# Counter culture for category analytics
counter_culture :category, column_name: 'products_count'
counter_culture :category,
  column_name: proc { |model| model.active? ? 'active_products_count' : nil }

# Counter culture for brand analytics (through category)
counter_culture [:category, :brand], column_name: 'products_count'
```

**Tracks**: Product counts per category and brand

### 6. **Store Model** (`app/models/store.rb`)

```ruby
# Counter culture for store chain analytics
counter_culture :store_chain, column_name: 'stores_count'

# Counter culture for brand analytics
counter_culture :brand, column_name: 'stores_count'
```

**Tracks**: Number of stores per chain and brand

### 7. **Category Model** (`app/models/category.rb`)

```ruby
# Counter culture for brand analytics
counter_culture :brand, column_name: 'categories_count'
```

**Tracks**: Number of categories per brand

### 8. **Promotion Model** (`app/models/promotion.rb`)

```ruby
# Counter culture for store analytics
counter_culture :store, column_name: 'promotions_count'

# Counter culture for region analytics
counter_culture :region, column_name: 'promotions_count'

# Counter culture for store chain analytics
counter_culture :store_chain, column_name: 'promotions_count'
```

**Tracks**: Number of promotions per store, region, and chain

### 9. **PromotionProduct Model** (`app/models/promotion_product.rb`)

```ruby
# Counter culture for promotion analytics
counter_culture :promotion, column_name: 'promotion_products_count'

# Counter culture for product analytics
counter_culture :product, column_name: 'promotion_products_count'
```

**Tracks**: Number of products per promotion and promotions per product

### 10. **State Model** (`app/models/state.rb`)

```ruby
# Counter culture for region analytics
counter_culture :region, column_name: 'states_count'
```

**Tracks**: Number of states per region

## Missing Models to Implement

### SalePromotionBonus Model

**Location**: `app/models/sale_promotion_bonus.rb`
**Needed Declarations**:

```ruby
# Counter culture for promotion analytics
counter_culture :promotion, column_name: 'sales_count'
counter_culture :promotion,
  column_name: 'total_bonus_points',
  delta_column: 'bonus_points'
```

### User Model (Store Chain Counter)

**Additional Declaration Needed**:

```ruby
# Counter culture for store chain analytics (through store)
counter_culture [:store, :store_chain], column_name: 'total_users_count'
```

## How Counter Culture Works

### Automatic Updates

When records are created, updated, or destroyed, counter_culture automatically:

1. **Increments** counters when records are added
2. **Decrements** counters when records are removed
3. **Recalculates** when delta columns change
4. **Handles** conditional counters based on status changes

### Conditional Counters

Using `proc` conditions allows counters to update only when specific conditions are met:

```ruby
counter_culture :user,
  column_name: proc { |model| model.approved? ? 'approved_sales_count' : nil }
```

### Delta Columns

For sum calculations, specify the column to sum:

```ruby
counter_culture :user,
  column_name: 'total_points_earned',
  delta_column: 'points'
```

### Multi-Level Associations

Track counters through multiple associations:

```ruby
counter_culture [:user, :store], column_name: 'sales_count'
```

## Performance Impact

### Before Implementation

- **Admin Dashboard**: 6+ expensive aggregation queries
- **Store Listings**: N+1 queries for user counts
- **Product Analytics**: Complex joins for sales data
- **User Profiles**: Multiple aggregation queries

### After Implementation

- **Admin Dashboard**: Direct column access (0 additional queries)
- **Store Listings**: Single query with cached counts
- **Product Analytics**: Instant access to cached metrics
- **User Profiles**: Cached activity counters

### Expected Improvements

- **Admin Dashboard**: 85% faster
- **Store Management**: 90% faster
- **Product Reports**: 75% faster
- **User Profiles**: 70% faster

## Maintenance

### Automatic Maintenance

Counter caches are automatically maintained when:

- Records are created, updated, or destroyed
- Status changes occur (pending → approved)
- Associations are modified

### Manual Maintenance

Use rake tasks for bulk fixes:

```bash
# Fix all counter caches
rails counter_culture:fix_all

# Verify accuracy
rails counter_culture:verify_all

# View statistics
rails counter_culture:stats
```

## Testing Counter Culture

### RSpec Examples

```ruby
describe "counter culture" do
  let(:store) { create(:store) }
  let(:user) { create(:user, store: store) }

  it "updates store users_count when user is created" do
    expect {
      create(:user, store: store)
    }.to change { store.reload.users_count }.by(1)
  end

  it "updates user sales_count when sale is created" do
    expect {
      create(:sale, user: user, status: :approved)
    }.to change { user.reload.sales_count }.by(1)
  end
end
```

## Deployment Strategy

### Phase 1: Database Setup

1. Run migration: `rails db:migrate`
2. Populate counters: `rails counter_culture:fix_all`
3. Verify accuracy: `rails counter_culture:verify_all`

### Phase 2: Model Updates

1. Deploy model changes with counter_culture declarations
2. Monitor for any counter drift
3. Set up regular verification in deployment pipeline

### Phase 3: Application Updates

1. Update admin dashboard to use cached values
2. Optimize queries to leverage counter caches
3. Add monitoring for performance improvements

## Monitoring

### Regular Health Checks

```bash
# Weekly verification
rails counter_culture:verify_all

# Monthly statistics review
rails counter_culture:stats
```

### Performance Monitoring

- Track query count reduction
- Monitor page load times
- Measure database CPU usage

## Troubleshooting

### Counter Drift

If counters become inaccurate:

```bash
# Fix specific model
User.counter_culture_fix_counts

# Fix all counters
rails counter_culture:fix_all
```

### Performance Issues

- Ensure indexes exist on counter columns
- Verify counter_culture gem is properly configured
- Check for N+1 queries in counter updates
