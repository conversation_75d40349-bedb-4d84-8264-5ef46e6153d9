# Gift Card System Implementation

## Overview

I've implemented a comprehensive gift card system that handles special ordering, CSV generation, SFTP upload, and daily bundling as requested. Gift cards are treated separately from regular products and have their own fulfillment workflow.

## Features Implemented

### 1. **Product Type Classification**
- Added `product_type` enum to Product model (`regular`, `gift_card`)
- Gift cards are excluded from regular sales workflow
- Separate handling for gift card orders

### 2. **Gift Card Order Management**
**GiftCardOrder Model:**
- Stores all required CSV fields (FirstName, LastName, Company, etc.)
- Links to Order and Product
- Tracks status (pending, batched, uploaded, failed)
- Validates recipient information

### 3. **Daily Batch Processing**
**GiftCardBatch Model:**
- Groups all gift card orders by date
- Generates CSV with exact format: `WWR-ZS3PG-${timestamp}.csv`
- Tracks batch status and upload history
- Attaches CSV file for later retrieval

### 4. **SFTP Integration**
**GiftCardSftpService:**
- Secure file transfer to remote server
- Configurable via Rails credentials or environment variables
- Connection testing and error handling
- Automatic directory creation

### 5. **Admin Interface**
**Complete management dashboard:**
- View all batches with status indicators
- Download CSV files
- Manual batch processing
- Retry failed uploads
- SFTP connection testing

### 6. **Automated Processing**
**Background Jobs:**
- `GiftCardBatchProcessorJob` - Processes daily batches
- Scheduled via cron (6 PM daily)
- Automatic retry for failed uploads (every 4 hours)
- Email notifications for admins

## CSV Format Implementation

**Exact format as requested:**
```csv
FirstName,LastName,Company,Street1,Street2,City,State,Zip,Country,Phone,Email,Amount,OfferCode,Message
John,Doe,Acme Corp,123 Main St,Suite 100,Anytown,CA,12345,US,555-1234,<EMAIL>,100.00,WELCOME,Happy Birthday!
```

**Filename format:** `WWR-ZS3PG-20250115_143022.csv`

## Database Schema

### **Products Table** (Enhanced)
```ruby
add_column :products, :product_type, :integer, default: 0, null: false
# 0 = regular, 1 = gift_card
```

### **Gift Card Orders Table**
```ruby
create_table :gift_card_orders do |t|
  t.references :order, :product
  t.decimal :amount
  t.string :recipient_first_name, :recipient_last_name
  t.string :recipient_company, :recipient_street1, :recipient_street2
  t.string :recipient_city, :recipient_state, :recipient_zip, :recipient_country
  t.string :recipient_phone, :recipient_email
  t.string :offer_code
  t.text :message
  t.integer :status
  t.references :gift_card_batch
end
```

### **Gift Card Batches Table**
```ruby
create_table :gift_card_batches do |t|
  t.date :batch_date
  t.string :filename
  t.integer :total_orders, :status
  t.decimal :total_amount
  t.datetime :uploaded_at
  t.text :upload_error
end
```

## Configuration

### **SFTP Settings** (Rails Credentials)
```yaml
gift_card_sftp:
  host: "sftp.example.com"
  username: "gift_cards"
  password: "secure_password"
  port: 22
  remote_path: "/uploads"

gift_card_notifications:
  admin_emails:
    - "<EMAIL>"
    - "<EMAIL>"
```

### **Environment Variables** (Alternative)
```bash
GIFT_CARD_SFTP_HOST=sftp.example.com
GIFT_CARD_SFTP_USERNAME=gift_cards
GIFT_CARD_SFTP_PASSWORD=secure_password
GIFT_CARD_SFTP_PORT=22
GIFT_CARD_SFTP_PATH=/uploads
GIFT_CARD_ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

## Workflow

### **Daily Processing Flow:**
1. **6 PM Daily** - Cron job triggers batch processing
2. **Collect Orders** - Find all unbatched gift card orders for the day
3. **Create Batch** - Group orders into daily batch
4. **Generate CSV** - Create file with exact format requirements
5. **Upload SFTP** - Transfer file to remote server
6. **Update Status** - Mark orders and batch as uploaded
7. **Send Notifications** - Email admins about success/failure

### **Manual Processing:**
- Admins can manually trigger batch processing
- Individual batch retry for failed uploads
- Download CSV files for review
- Test SFTP connection

## Admin Interface Features

### **Batch Management:**
- ✅ List all batches with search/filter
- ✅ View batch details and orders
- ✅ Download CSV files
- ✅ Manual processing controls
- ✅ Retry failed uploads
- ✅ SFTP connection testing

### **Status Indicators:**
- **Pending** - Orders collected, not yet processed
- **Generated** - CSV created, ready for upload
- **Uploaded** - Successfully sent to SFTP server
- **Failed** - Upload failed, needs retry

### **Navigation Integration:**
- Added to admin sidebar with pending batch count
- Breadcrumb navigation
- Consistent styling with existing admin interface

## Error Handling & Monitoring

### **Robust Error Handling:**
- SFTP connection failures
- File generation errors
- Invalid recipient data
- Network timeouts

### **Monitoring & Alerts:**
- Email notifications for batch status
- Failed upload retry mechanism
- Detailed error logging
- Admin dashboard visibility

### **Recovery Features:**
- Manual retry for failed batches
- CSV file preservation for re-upload
- Error message display
- Connection testing tools

## Usage Examples

### **Creating Gift Card Product:**
```ruby
gift_card = Product.create!(
  name: "Digital Gift Card",
  sku: "GIFT-CARD-100",
  product_type: :gift_card,
  description: "Redeemable digital gift card"
)
```

### **Processing Daily Batch:**
```ruby
# Manual processing
GiftCardBatchProcessorJob.perform_now(Date.current)

# Or via rake task
rake gift_cards:process_daily
```

### **Admin Operations:**
- View batches: `/admin/gift_card_batches`
- Process today's orders: Click "Process Today's Orders"
- Download CSV: Click "Download" on any generated batch
- Test SFTP: Click "Test SFTP" button

## Benefits

### **Operational:**
- ✅ **Automated daily processing** - No manual intervention needed
- ✅ **Separate fulfillment** - Gift cards don't interfere with regular orders
- ✅ **Audit trail** - Complete history of all batches and uploads
- ✅ **Error recovery** - Automatic retry and manual override options

### **Technical:**
- ✅ **Exact CSV format** - Matches specification precisely
- ✅ **Secure SFTP** - Encrypted file transfer
- ✅ **File preservation** - CSV attached to batch for later retrieval
- ✅ **Daily bundling** - All orders for a day in single file

### **Administrative:**
- ✅ **Complete visibility** - Dashboard shows all batch status
- ✅ **Manual controls** - Override automatic processing when needed
- ✅ **Download capability** - Access any historical CSV file
- ✅ **Status tracking** - Know exactly what happened with each batch

The gift card system is now fully implemented and ready for production use!