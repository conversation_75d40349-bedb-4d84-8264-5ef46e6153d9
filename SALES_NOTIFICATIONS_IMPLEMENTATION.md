# Sales Notifications Implementation

## Overview

This implementation adds comprehensive sales notifications using the Noticed gem. The system notifies admins when new sales are submitted and notifies users when their sales are approved or rejected, supporting both email and push notification delivery methods.

## Files Created/Modified

### Notifiers
- `app/notifiers/new_sale_notifier.rb` - Notifies admins of new sales
- `app/notifiers/sale_approved_notifier.rb` - Notifies users when sales are approved
- `app/notifiers/sale_rejected_notifier.rb` - Notifies users when sales are rejected

### Mailer
- `app/mailers/sale_mailer.rb` (modified) - Added email methods for all sale notifications

### Email Templates (MJML)
- `app/views/sale_mailer/new_sale_submitted.html.mjml` - Admin notification email
- `app/views/sale_mailer/sale_approved.html.mjml` - User approval email  
- `app/views/sale_mailer/sale_rejected.html.mjml` - User rejection email

### Controllers
- `app/controllers/sales_controller.rb` (modified) - Triggers new sale notifications
- `app/controllers/admin/sales_controller.rb` (modified) - Triggers approval/rejection notifications

### Tests
- `spec/notifiers/new_sale_notifier_spec.rb` - Tests for admin notifications
- `spec/notifiers/sale_approved_notifier_spec.rb` - Tests for approval notifications
- `spec/mailers/previews/sale_mailer_preview.rb` (modified) - Email previews for development

## Notification Flow

### 1. New Sale Submitted
**Trigger**: When a user creates a new sale
**Recipients**: All admin users
**Delivery Methods**: Email + Push notifications
**Conditions**: 
- Respects admin notification preferences
- Respects quiet hours for push notifications
- Only sends to admins with sale notifications enabled

### 2. Sale Approved
**Trigger**: When an admin approves a pending sale
**Recipients**: The sale's user
**Delivery Methods**: Email + Push notifications
**Conditions**:
- Respects user notification preferences
- Respects quiet hours for push notifications
- Includes bonus points information if applicable

### 3. Sale Rejected
**Trigger**: When an admin rejects a pending sale
**Recipients**: The sale's user
**Delivery Methods**: Email + Push notifications
**Conditions**:
- Respects user notification preferences
- Respects quiet hours for push notifications
- Includes rejection reason if provided

## Notifier Features

### NewSaleNotifier
```ruby
# Usage
NewSaleNotifier.with(record: @sale).deliver

# Automatic recipient targeting
recipients -> { User.where(role: :admin) }

# Available methods in templates
notification.message    # "New sale <NAME_EMAIL> for Product Name (100 points)"
notification.title      # "New Sale Pending Approval"
notification.url        # Link to admin sale page
notification.sale       # Sale record
notification.seller     # User who submitted sale
notification.product    # Product sold
notification.points     # Points value
```

### SaleApprovedNotifier
```ruby
# Usage
SaleApprovedNotifier.with(record: @sale, approved_by: current_user).deliver(@sale.user)

# Available methods in templates
notification.message         # Includes bonus points info
notification.title          # "Sale Approved!"
notification.base_points    # Original points
notification.total_points   # Points including bonuses
notification.bonus_points   # Bonus points earned
notification.has_bonus?     # Whether bonus points were earned
notification.approved_by_admin # Admin who approved
```

### SaleRejectedNotifier
```ruby
# Usage
SaleRejectedNotifier.with(
  record: @sale, 
  rejected_by: current_user,
  reason: "Invalid serial number"
).deliver(@sale.user)

# Available methods in templates
notification.message           # Includes rejection reason
notification.title            # "Sale Rejected"
notification.rejection_reason # Optional reason text
notification.has_reason?      # Whether reason was provided
notification.rejected_by_admin # Admin who rejected
```

## Integration with User Preferences

All notifications respect the user notification settings:

- **Global toggles**: `push_notifications_enabled`, `email_notifications_enabled`
- **Type-specific**: `sale_notifications` setting
- **Quiet hours**: Notifications respect user's quiet hours for push delivery
- **Frequency**: Currently immediate, but framework supports digest options

## Email Templates (MJML)

### Why MJML?
MJML (Mailjet Markup Language) is used for creating responsive email templates that work consistently across all email clients, including Outlook. It provides:
- **Cross-client compatibility** - Works in Gmail, Outlook, Apple Mail, etc.
- **Responsive by default** - Automatically adapts to mobile and desktop
- **Simplified syntax** - Clean, semantic markup that compiles to complex HTML tables
- **Professional appearance** - Consistent rendering across all email clients

### Design Features
- **Modern responsive design** with MJML's bulletproof compatibility
- **Zeiss brand colors** (#1e40af blue, #059669 green, #dc2626 red)
- **Card-based layouts** with subtle shadows and borders
- **Emoji icons** for visual appeal and quick recognition
- **Gradient backgrounds** for points summary highlighting
- **Professional typography** with proper spacing and hierarchy
- **Clear call-to-action buttons** with hover states
- **Structured information** in easy-to-scan card formats

### Template Variables
All templates have access to:
- `@sale` - The sale record
- `@user` - The recipient user
- `@product` - The product sold
- Email-specific variables (admin, approved_by, rejected_by, reason, etc.)

## Push Notification Integration

Push notifications include:
- **Title**: Short, descriptive title
- **Body**: Detailed message with sale info
- **Icon**: App icon
- **Data**: URL for navigation when clicked
- **Respect for quiet hours**: Won't send during user's quiet time

## Testing

### RSpec Tests
- **Notifier behavior**: Validates delivery logic and recipient targeting
- **User preferences**: Ensures notifications respect settings
- **Quiet hours**: Verifies time-based delivery rules
- **Message generation**: Tests notification content and URLs
- **Validations**: Ensures required parameters are present

### Mailer Previews
Access email previews in development:
- `/rails/mailers/sale_mailer/new_sale_submitted`
- `/rails/mailers/sale_mailer/sale_approved`
- `/rails/mailers/sale_mailer/sale_rejected`

## Configuration Requirements

### MJML Setup
Add the MJML gem to your Gemfile:
```ruby
# Gemfile
gem 'mjml-rails'
```

Then run:
```bash
bundle install
```

MJML templates with `.mjml` extension will automatically be compiled to HTML by Rails.

### VAPID Keys (for Push Notifications)
```yaml
# config/credentials.yml.enc
web_push:
  vapid_public_key: "your_public_key"
  vapid_private_key: "your_private_key"
  vapid_subject: "mailto:<EMAIL>"
```

### URL Configuration
Ensure `default_url_options` are set for email links:
```ruby
# config/environments/production.rb
config.action_mailer.default_url_options = { host: 'your-domain.com' }
```

### MJML Development
For development, you can preview MJML templates:
- Install MJML CLI: `npm install -g mjml`
- Preview templates: `mjml app/views/sale_mailer/new_sale_submitted.html.mjml -o preview.html`

## Usage Examples

### In Controllers
```ruby
# When creating a sale
if @sale.save
  NewSaleNotifier.with(record: @sale).deliver
  flash[:notice] = "Sale recorded and submitted for approval."
end

# When approving a sale
SaleApprovedNotifier.with(record: @sale, approved_by: current_user).deliver(@sale.user)

# When rejecting a sale
SaleRejectedNotifier.with(
  record: @sale, 
  rejected_by: current_user,
  reason: params[:reason]
).deliver(@sale.user)
```

### In Views (accessing notifications)
```erb
<% current_user.notifications.recent.each do |notification| %>
  <div class="notification">
    <%= link_to notification.message, notification.url %>
    <small><%= time_ago_in_words(notification.created_at) %> ago</small>
  </div>
<% end %>
```

## Performance Considerations

- **Background jobs**: All notifications are delivered via background jobs
- **Batch processing**: Admin notifications are sent to multiple recipients efficiently
- **Database indexing**: Noticed gem includes proper indexes for notifications
- **Cleanup**: Consider implementing notification cleanup for old records

## Future Enhancements

1. **Digest notifications**: Implement daily/weekly digest options
2. **In-app notifications**: Add real-time in-app notification display
3. **SMS notifications**: Add SMS delivery for critical notifications
4. **Notification history**: Add UI for users to view notification history
5. **Advanced filtering**: Allow users to filter notifications by type/importance
6. **Rich notifications**: Add images and action buttons to push notifications

## Monitoring and Analytics

Consider adding:
- **Delivery tracking**: Monitor notification delivery success rates
- **User engagement**: Track notification click-through rates
- **Preference analytics**: Understand user notification preferences
- **Error handling**: Log and monitor notification delivery failures