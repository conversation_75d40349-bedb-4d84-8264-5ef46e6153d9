namespace :counter_culture do
  desc "Fix counter culture counts for orders"
  task fix_orders: :environment do
    puts "Fixing counter culture counts for orders..."

    Order.find_each do |order|
      # Update line items count
      actual_count = order.line_items.count
      if order.line_items_count != actual_count
        puts "Order #{order.id}: Updating line_items_count from #{order.line_items_count} to #{actual_count}"
        order.update_column(:line_items_count, actual_count)
      end

      # Update total points cache
      actual_total = order.line_items.sum(&:total_points)
      if order.total_points_cache != actual_total
        puts "Order #{order.id}: Updating total_points_cache from #{order.total_points_cache} to #{actual_total}"
        order.update_column(:total_points_cache, actual_total)
      end
    end

    puts "Counter culture fix complete!"
  end

  desc "Verify counter culture counts for orders"
  task verify_orders: :environment do
    puts "Verifying counter culture counts for orders..."

    errors = []

    Order.includes(:line_items).find_each do |order|
      actual_count = order.line_items.count
      actual_total = order.line_items.sum(&:total_points)

      if order.line_items_count != actual_count
        errors << "Order #{order.id}: line_items_count is #{order.line_items_count}, should be #{actual_count}"
      end

      if order.total_points_cache != actual_total
        errors << "Order #{order.id}: total_points_cache is #{order.total_points_cache}, should be #{actual_total}"
      end
    end

    if errors.empty?
      puts "✅ All counter culture counts are correct!"
    else
      puts "❌ Found #{errors.count} errors:"
      errors.each { |error| puts "  #{error}" }
      puts "\nRun 'rails counter_culture:fix_orders' to fix these issues."
    end
  end
end
