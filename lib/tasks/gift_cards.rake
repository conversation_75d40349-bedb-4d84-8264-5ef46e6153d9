# frozen_string_literal: true

namespace :gift_cards do
  desc "Process daily gift card batches"
  task process_daily: :environment do
    puts "Processing gift card batches for #{Date.current}"

    begin
      GiftCardBatchProcessorJob.perform_now(Date.current)
      puts "Gift card batch processing completed successfully"
    rescue => e
      puts "Error processing gift card batches: #{e.message}"
      raise e
    end
  end

  desc "Retry failed gift card batch uploads"
  task retry_failed: :environment do
    failed_batches = GiftCardBatch.failed.where("created_at > ?", 7.days.ago)

    puts "Found #{failed_batches.count} failed batches to retry"

    failed_batches.each do |batch|
      puts "Retrying batch #{batch.filename}..."

      if batch.retry_upload!
        puts "  ✓ Successfully uploaded #{batch.filename}"
      else
        puts "  ✗ Failed to upload #{batch.filename}: #{batch.upload_error}"
      end
    end
  end

  desc "Test SFTP connection"
  task test_sftp: :environment do
    service = GiftCardSftpService.new
    if service.test_connection
      puts "✓ SFTP connection test successful"

      # List files
      files = service.list_files
      puts "Files in remote directory: #{files.count}"
      files.first(5).each { |file| puts "  - #{file}" }
      puts "  ... and #{files.count - 5} more" if files.count > 5
    else
      puts "✗ SFTP connection test failed"
    end
  rescue GiftCardSftpService::SftpError => e
    puts "✗ SFTP error: #{e.message}"
  rescue => e
    puts "✗ Unexpected error: #{e.message}"
  end

  desc "Generate sample gift card orders for testing"
  task generate_sample_data: :environment do
    # Create a gift card product if it doesn't exist
    gift_card = Product.find_or_create_by(name: "Gift Card", product_type: :gift_card) do |product|
      product.sku = "GIFT-CARD-001"
      product.description = "Digital gift card"
      product.points_required = 0
    end

    # Create sample orders
    5.times do |i|
      user = User.first || FactoryBot.create(:user)
      order = Order.create!(user: user, status: :approved, total: 100.0)

      GiftCardOrder.create!(
        order: order,
        product: gift_card,
        amount: [25, 50, 100, 150, 200].sample,
        recipient_first_name: "Recipient#{i + 1}",
        recipient_last_name: "Test",
        recipient_street1: "123 Main St",
        recipient_city: "Test City",
        recipient_state: "CA",
        recipient_zip: "12345",
        recipient_country: "US",
        recipient_email: "recipient#{i + 1}@example.com",
        message: "Happy Birthday!"
      )
    end

    puts "Created 5 sample gift card orders"
  end
end
