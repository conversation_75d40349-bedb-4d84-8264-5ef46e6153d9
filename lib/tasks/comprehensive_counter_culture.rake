namespace :counter_culture do
  desc "Fix all comprehensive counter culture counts"
  task fix_all: :environment do
    puts "🔧 Fixing all comprehensive counter culture counts..."
    start_time = Time.current

    # === STORES COUNTERS ===
    puts "\n📍 Fixing store counters..."
    Store.find_each do |store|
      print "."

      # Users count
      users_count = store.users.count
      store.update_column(:users_count, users_count) if store.users_count != users_count

      # Sales counts
      sales_count = store.users.joins(:sales).count
      approved_sales_count = store.users.joins(:sales).where(sales: {status: :approved}).count
      pending_sales_count = store.users.joins(:sales).where(sales: {status: :pending}).count
      total_sales_points = store.users.joins(:sales).where(sales: {status: :approved}).sum("sales.points")

      store.update_columns(
        sales_count: sales_count,
        approved_sales_count: approved_sales_count,
        pending_sales_count: pending_sales_count,
        total_sales_points: total_sales_points
      )

      # Orders count
      orders_count = store.users.joins(:orders).count
      store.update_column(:orders_count, orders_count) if store.orders_count != orders_count

      # Promotions count
      promotions_count = store.promotions.count
      store.update_column(:promotions_count, promotions_count) if store.promotions_count != promotions_count
    end
    puts "\n✅ Store counters fixed!"

    # === USERS COUNTERS ===
    puts "\n👥 Fixing user counters..."
    User.find_each do |user|
      print "."

      sales_count = user.sales.count
      approved_sales_count = user.sales.approved.count
      pending_sales_count = user.sales.pending.count
      orders_count = user.orders.count
      total_points_earned = user.sales.approved.sum(:points)

      user.update_columns(
        sales_count: sales_count,
        approved_sales_count: approved_sales_count,
        pending_sales_count: pending_sales_count,
        orders_count: orders_count,
        total_points_earned: total_points_earned
      )
    end
    puts "\n✅ User counters fixed!"

    # === PRODUCTS COUNTERS ===
    puts "\n📦 Fixing product counters..."
    Product.find_each do |product|
      print "."

      # Use Sale.where(product: product) instead of product.sales
      sales_count = Sale.where(product: product).count
      orders_count = product.line_items.joins(:order).count
      line_items_count = product.line_items.count
      total_points_earned = Sale.where(product: product, status: :approved).sum(:points)
      total_quantity_sold = product.line_items.sum(:quantity)
      promotion_products_count = product.promotion_products.count

      product.update_columns(
        sales_count: sales_count,
        orders_count: orders_count,
        line_items_count: line_items_count,
        total_points_earned: total_points_earned,
        total_quantity_sold: total_quantity_sold,
        promotion_products_count: promotion_products_count
      )
    end
    puts "\n✅ Product counters fixed!"

    # === CATEGORIES COUNTERS ===
    puts "\n📂 Fixing category counters..."
    Category.find_each do |category|
      print "."

      products_count = category.products.count
      active_products_count = category.products.active.count

      category.update_columns(
        products_count: products_count,
        active_products_count: active_products_count
      )
    end
    puts "\n✅ Category counters fixed!"

    # === BRANDS COUNTERS ===
    puts "\n🏷️ Fixing brand counters..."
    Brand.find_each do |brand|
      print "."

      stores_count = brand.stores.count
      categories_count = brand.categories.count
      products_count = brand.categories.joins(:products).count

      brand.update_columns(
        stores_count: stores_count,
        categories_count: categories_count,
        products_count: products_count
      )
    end
    puts "\n✅ Brand counters fixed!"

    # === STORE CHAINS COUNTERS ===
    puts "\n🏪 Fixing store chain counters..."
    StoreChain.find_each do |chain|
      print "."

      stores_count = chain.stores.count
      total_users_count = chain.stores.joins(:users).count
      promotions_count = chain.promotions.count

      chain.update_columns(
        stores_count: stores_count,
        total_users_count: total_users_count,
        promotions_count: promotions_count
      )
    end
    puts "\n✅ Store chain counters fixed!"

    # === REGIONS COUNTERS ===
    puts "\n🗺️ Fixing region counters..."
    Region.find_each do |region|
      print "."

      states_count = region.states.count
      promotions_count = region.promotions.count

      region.update_columns(
        states_count: states_count,
        promotions_count: promotions_count
      )
    end
    puts "\n✅ Region counters fixed!"

    # === PROMOTIONS COUNTERS ===
    puts "\n🎯 Fixing promotion counters..."
    Promotion.find_each do |promotion|
      print "."

      # Use SalePromotionBonus to count sales for this promotion
      sales_count = promotion.sale_promotion_bonuses.count
      total_bonus_points = promotion.sale_promotion_bonuses.sum(:bonus_points)
      promotion_products_count = promotion.promotion_products.count

      promotion.update_columns(
        sales_count: sales_count,
        total_bonus_points: total_bonus_points,
        promotion_products_count: promotion_products_count
      )
    end
    puts "\n✅ Promotion counters fixed!"

    end_time = Time.current
    duration = (end_time - start_time).round(2)

    puts "\n🎉 All comprehensive counter culture counts fixed!"
    puts "⏱️ Total time: #{duration} seconds"
  end

  desc "Verify all comprehensive counter culture counts"
  task verify_all: :environment do
    puts "🔍 Verifying all comprehensive counter culture counts..."
    errors = []

    # === STORES VERIFICATION ===
    puts "\n📍 Verifying store counters..."
    Store.includes(:users, :promotions).find_each do |store|
      print "."

      # Verify users count
      actual_users = store.users.count
      errors << "Store #{store.id}: users_count is #{store.users_count}, should be #{actual_users}" if store.users_count != actual_users

      # Verify sales counts
      actual_sales = store.users.joins(:sales).count
      errors << "Store #{store.id}: sales_count is #{store.sales_count}, should be #{actual_sales}" if store.sales_count != actual_sales

      actual_approved_sales = store.users.joins(:sales).where(sales: {status: :approved}).count
      errors << "Store #{store.id}: approved_sales_count is #{store.approved_sales_count}, should be #{actual_approved_sales}" if store.approved_sales_count != actual_approved_sales

      # Verify promotions count
      actual_promotions = store.promotions.count
      errors << "Store #{store.id}: promotions_count is #{store.promotions_count}, should be #{actual_promotions}" if store.promotions_count != actual_promotions
    end

    # === USERS VERIFICATION ===
    puts "\n👥 Verifying user counters..."
    User.includes(:sales, :orders).find_each do |user|
      print "."

      actual_sales = user.sales.count
      errors << "User #{user.id}: sales_count is #{user.sales_count}, should be #{actual_sales}" if user.sales_count != actual_sales

      actual_orders = user.orders.count
      errors << "User #{user.id}: orders_count is #{user.orders_count}, should be #{actual_orders}" if user.orders_count != actual_orders
    end

    # === PRODUCTS VERIFICATION ===
    puts "\n📦 Verifying product counters..."
    Product.includes(:line_items, :promotion_products).find_each do |product|
      print "."

      actual_line_items = product.line_items.count
      errors << "Product #{product.id}: line_items_count is #{product.line_items_count}, should be #{actual_line_items}" if product.line_items_count != actual_line_items

      actual_promotion_products = product.promotion_products.count
      errors << "Product #{product.id}: promotion_products_count is #{product.promotion_products_count}, should be #{actual_promotion_products}" if product.promotion_products_count != actual_promotion_products
    end

    # === CATEGORIES VERIFICATION ===
    puts "\n📂 Verifying category counters..."
    Category.includes(:products).find_each do |category|
      print "."

      actual_products = category.products.count
      errors << "Category #{category.id}: products_count is #{category.products_count}, should be #{actual_products}" if category.products_count != actual_products

      actual_active_products = category.products.active.count
      errors << "Category #{category.id}: active_products_count is #{category.active_products_count}, should be #{actual_active_products}" if category.active_products_count != actual_active_products
    end

    # === BRANDS VERIFICATION ===
    puts "\n🏷️ Verifying brand counters..."
    Brand.includes(:stores, :categories).find_each do |brand|
      print "."

      actual_stores = brand.stores.count
      errors << "Brand #{brand.id}: stores_count is #{brand.stores_count}, should be #{actual_stores}" if brand.stores_count != actual_stores

      actual_categories = brand.categories.count
      errors << "Brand #{brand.id}: categories_count is #{brand.categories_count}, should be #{actual_categories}" if brand.categories_count != actual_categories
    end

    puts "\n"

    if errors.empty?
      puts "✅ All comprehensive counter culture counts are correct!"
    else
      puts "❌ Found #{errors.count} errors:"
      errors.each { |error| puts "  #{error}" }
      puts "\n💡 Run 'rails counter_culture:fix_all' to fix these issues."
    end
  end

  desc "Show comprehensive counter culture statistics"
  task stats: :environment do
    puts "📊 Comprehensive Counter Culture Statistics"
    puts "=" * 50

    puts "\n📍 STORES:"
    puts "  Total stores: #{Store.count}"
    puts "  Stores with users: #{Store.where("users_count > 0").count}"
    puts "  Stores with sales: #{Store.where("sales_count > 0").count}"
    puts "  Top store by users: #{Store.order(users_count: :desc).first&.name} (#{Store.maximum(:users_count)} users)"
    puts "  Top store by sales: #{Store.order(sales_count: :desc).first&.name} (#{Store.maximum(:sales_count)} sales)"

    puts "\n👥 USERS:"
    puts "  Total users: #{User.count}"
    puts "  Users with sales: #{User.where("sales_count > 0").count}"
    puts "  Users with orders: #{User.where("orders_count > 0").count}"
    puts "  Most active user: #{User.maximum(:sales_count)} sales"
    puts "  Total points earned by all users: #{User.sum(:total_points_earned)}"

    puts "\n📦 PRODUCTS:"
    puts "  Total products: #{Product.count}"
    puts "  Products with sales: #{Product.where("sales_count > 0").count}"
    puts "  Products in orders: #{Product.where("orders_count > 0").count}"
    puts "  Most popular product: #{Product.maximum(:sales_count)} sales"
    puts "  Total quantity sold: #{Product.sum(:total_quantity_sold)}"

    puts "\n📂 CATEGORIES:"
    puts "  Total categories: #{Category.count}"
    puts "  Categories with products: #{Category.where("products_count > 0").count}"
    puts "  Largest category: #{Category.maximum(:products_count)} products"
    puts "  Active products across all categories: #{Category.sum(:active_products_count)}"

    puts "\n🏷️ BRANDS:"
    puts "  Total brands: #{Brand.count}"
    puts "  Brands with stores: #{Brand.where("stores_count > 0").count}"
    puts "  Brands with categories: #{Brand.where("categories_count > 0").count}"
    puts "  Largest brand by products: #{Brand.maximum(:products_count)} products"

    puts "\n🏪 STORE CHAINS:"
    puts "  Total chains: #{StoreChain.count}"
    puts "  Chains with stores: #{StoreChain.where("stores_count > 0").count}"
    puts "  Largest chain: #{StoreChain.maximum(:stores_count)} stores"
    puts "  Total users in chains: #{StoreChain.sum(:total_users_count)}"

    puts "\n🎯 PROMOTIONS:"
    puts "  Total promotions: #{Promotion.count}"
    puts "  Promotions with sales: #{Promotion.where("sales_count > 0").count}"
    puts "  Most effective promotion: #{Promotion.maximum(:sales_count)} sales"
    puts "  Total bonus points awarded: #{Promotion.sum(:total_bonus_points)}"

    puts "\n" + "=" * 50
  end
end
